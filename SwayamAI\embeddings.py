import os
import numpy as np
import joblib
from sklearn.feature_extraction.text import TfidfVectorizer
from nltk.tokenize import word_tokenize
import random
import logging
from sklearn.decomposition import TruncatedSVD

logging.getLogger(__name__)

class TextEmbeddings:
    output_folder = None

    def __init__(self) -> None:
        self.tfidf_vector = None
        self.w2v_model = None
        self.svd_model = None  # added for TF-IDF dimensionality reduction

    def set_output_folder(self, output_folder):
        self.output_folder = output_folder
        if self.output_folder and not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder, exist_ok=True)

    def embeddings_tfidf(self, text, ngram=1, sparse_return=True, max_features=200000, min_df=2, max_df=0.95):
        """Fit TF-IDF on text. Returns sparse matrix by default for scalability."""
        self.tfidf_vector = TfidfVectorizer(
            analyzer='word',
            ngram_range=(ngram, ngram),
            max_features=max_features,
            min_df=min_df,
            max_df=max_df,
            dtype=np.float32
        )
        arr = text.astype('U') if hasattr(text, 'astype') else np.array(text, dtype='U')
        self.tfidf_vector.fit(arr)
        pkl_file_name = f'tfidf_vectorizer_ngram{ngram}.pkl'
        output_pkl_file = os.path.join(self.output_folder or '.', pkl_file_name)
        joblib.dump(self.tfidf_vector, output_pkl_file)
        transformed = self.tfidf_vector.transform(arr)
        if sparse_return:
            return transformed
        return transformed.toarray()

    def embeddings_tfidf_test_data(self, text, sparse_return=True):
        arr = text.astype('U') if hasattr(text, 'astype') else np.array(text, dtype='U')
        transformed = self.tfidf_vector.transform(arr)
        if sparse_return:
            return transformed
        return transformed.toarray()

    def svd_fit(self, X_sparse, n_components=300, random_state=42):
        """Fit TruncatedSVD on sparse TF-IDF and return reduced dense matrix."""
        if not hasattr(X_sparse, 'shape'):
            raise ValueError("X_sparse must be a scipy sparse matrix or array-like with .shape")
        # cap components to valid range
        max_comps = max(1, min(n_components, X_sparse.shape[1] - 1)) if X_sparse.shape[1] > 1 else 1
        self.svd_model = TruncatedSVD(n_components=max_comps, random_state=random_state)
        X_reduced = self.svd_model.fit_transform(X_sparse)
        svd_pkl = os.path.join(self.output_folder or '.', f'tfidf_svd_{max_comps}.pkl')
        joblib.dump(self.svd_model, svd_pkl)
        return X_reduced

    def svd_transform(self, X_sparse):
        if self.svd_model is None:
            raise RuntimeError("SVD model is not initialized. Call svd_fit first.")
        return self.svd_model.transform(X_sparse)

    def embeddings_word2vec(self, text, seed=42):
        """Accepts either iterable of token lists or iterable of raw strings (will tokenize)."""
        self.set_random_seed(seed)
        try:
            from gensim.models import Word2Vec
        except Exception as e:
            raise RuntimeError("gensim is required for Word2Vec embeddings") from e

        # normalize input to list of token lists
        tokenized = []
        for doc in text:
            if isinstance(doc, (list, tuple)):
                tokenized.append(list(doc))
            else:
                try:
                    tokenized.append(word_tokenize(str(doc)))
                except Exception:
                    tokenized.append(str(doc).split())

        self.w2v_model = Word2Vec(sentences=tokenized, vector_size=100, sg=0, hs=1, window=5, min_count=1, workers=1, seed=seed)
        pkl_file_name = f'word2vec_model.pkl'
        output_pkl_file = os.path.join(self.output_folder or '.', pkl_file_name)
        joblib.dump(self.w2v_model, output_pkl_file)
        return np.asarray(self.vectorize(tokenized, model=self.w2v_model))

    def embeddings_word2vec_test_data(self, text):
        if self.w2v_model is None:
            raise RuntimeError("Word2Vec model is not initialized. Call embeddings_word2vec first.")
        tokenized = []
        for doc in text:
            if isinstance(doc, (list, tuple)):
                tokenized.append(list(doc))
            else:
                try:
                    tokenized.append(word_tokenize(str(doc)))
                except Exception:
                    tokenized.append(str(doc).split())
        return np.asarray(self.vectorize(tokenized, model=self.w2v_model))

    def vectorize(self, list_of_docs, model):
        features = []
        for tokens in list_of_docs:
            zero_vector = np.zeros(model.vector_size, dtype=np.float32)
            vectors = []
            for token in tokens:
                if token in model.wv:
                    try:
                        vectors.append(model.wv[token])
                    except Exception:
                        continue
            if vectors:
                vectors = np.asarray(vectors, dtype=np.float32)
                avg_sec = vectors.mean(axis=0)
                features.append(avg_sec)
            else:
                features.append(zero_vector)
        return features

    def embeddings_bert(self, text, seed=42, batch_size=8, device='cpu'):
        self.set_random_seed(seed)
        try:
            from transformers import AutoTokenizer, AutoModel
            import torch
            from torch.utils.data import DataLoader
        except Exception as e:
            raise RuntimeError("transformers and torch are required for BERT embeddings") from e

        tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
        model = AutoModel.from_pretrained('bert-base-uncased')
        model.eval()
        if device == 'cuda' and getattr(__import__('torch'), 'cuda').is_available():
            model.to('cuda')

        text_list = text.tolist() if hasattr(text, 'tolist') else list(text)
        dataloader = DataLoader(text_list, batch_size=batch_size, shuffle=False)
        embeddings = []
        for batch in dataloader:
            inputs = tokenizer(batch, return_tensors='pt', padding=True, truncation=True)
            if device == 'cuda':
                inputs = {k: v.to('cuda') for k, v in inputs.items()}
            with torch.no_grad():
                outputs = model(**inputs)
            emb_batch = outputs.last_hidden_state[:, 0, :].cpu().numpy()
            embeddings.append(emb_batch)
        embeddings = np.vstack(embeddings) if embeddings else np.zeros((0, model.config.hidden_size), dtype=np.float32)
        return embeddings

    def text_tokenizer(self, text):
        try:
            return word_tokenize(text)
        except Exception:
            return text.split()

    def set_random_seed(self, seed):
        random.seed(seed)
        np.random.seed(seed)
        try:
            import torch
            torch.manual_seed(seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(seed)
        except Exception:
            pass
