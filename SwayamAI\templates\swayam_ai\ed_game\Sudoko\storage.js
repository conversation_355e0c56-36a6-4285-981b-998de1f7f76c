/**
 * Storage - Local storage management for game state and statistics
 */

class SudokuStorage {
    constructor() {
        this.GAME_STATE_KEY = 'sudoku_game_state';
        this.STATISTICS_KEY = 'sudoku_statistics';
        this.SETTINGS_KEY = 'sudoku_settings';
    }

    saveGameState(gameState) {
        try {
            localStorage.setItem(this.GAME_STATE_KEY, JSON.stringify(gameState));
            return true;
        } catch (error) {
            console.error('Failed to save game state:', error);
            return false;
        }
    }

    loadGameState() {
        try {
            const data = localStorage.getItem(this.GAME_STATE_KEY);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Failed to load game state:', error);
            return null;
        }
    }

    clearGameState() {
        try {
            localStorage.removeItem(this.GAME_STATE_KEY);
            return true;
        } catch (error) {
            console.error('Failed to clear game state:', error);
            return false;
        }
    }

    saveGameCompletion(completionData) {
        try {
            const stats = this.loadStatistics();

            if (!stats.completions) {
                stats.completions = [];
            }

            stats.completions.push(completionData);

            // Update aggregate stats
            const difficulty = completionData.difficulty;
            if (!stats.byDifficulty[difficulty]) {
                stats.byDifficulty[difficulty] = {
                    played: 0,
                    completed: 0,
                    bestTime: null,
                    averageTime: 0,
                    totalTime: 0
                };
            }

            const diffStats = stats.byDifficulty[difficulty];
            diffStats.completed++;
            diffStats.totalTime += completionData.time;
            diffStats.averageTime = diffStats.totalTime / diffStats.completed;

            if (!diffStats.bestTime || completionData.time < diffStats.bestTime) {
                diffStats.bestTime = completionData.time;
            }

            // Keep only last 100 completions
            if (stats.completions.length > 100) {
                stats.completions = stats.completions.slice(-100);
            }

            this.saveStatistics(stats);
            return true;

        } catch (error) {
            console.error('Failed to save completion:', error);
            return false;
        }
    }

    loadStatistics() {
        try {
            const data = localStorage.getItem(this.STATISTICS_KEY);
            return data ? JSON.parse(data) : this.getDefaultStatistics();
        } catch (error) {
            console.error('Failed to load statistics:', error);
            return this.getDefaultStatistics();
        }
    }

    saveStatistics(stats) {
        try {
            localStorage.setItem(this.STATISTICS_KEY, JSON.stringify(stats));
            return true;
        } catch (error) {
            console.error('Failed to save statistics:', error);
            return false;
        }
    }

    getDefaultStatistics() {
        return {
            totalGamesStarted: 0,
            totalGamesCompleted: 0,
            byDifficulty: {
                easy: { played: 0, completed: 0, bestTime: null, averageTime: 0, totalTime: 0 },
                medium: { played: 0, completed: 0, bestTime: null, averageTime: 0, totalTime: 0 },
                hard: { played: 0, completed: 0, bestTime: null, averageTime: 0, totalTime: 0 }
            },
            completions: []
        };
    }

    incrementGamesStarted(difficulty) {
        try {
            const stats = this.loadStatistics();
            stats.totalGamesStarted++;
            stats.byDifficulty[difficulty].played++;
            this.saveStatistics(stats);
        } catch (error) {
            console.error('Failed to increment games started:', error);
        }
    }

    saveSettings(settings) {
        try {
            localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('Failed to save settings:', error);
            return false;
        }
    }

    loadSettings() {
        try {
            const data = localStorage.getItem(this.SETTINGS_KEY);
            return data ? JSON.parse(data) : this.getDefaultSettings();
        } catch (error) {
            console.error('Failed to load settings:', error);
            return this.getDefaultSettings();
        }
    }

    getDefaultSettings() {
        return {
            theme: 'default',
            soundEnabled: false,
            autoSave: true,
            showTimer: true,
            showErrors: true,
            highlightRelated: true,
            highlightSameNumbers: true
        };
    }

    exportData() {
        try {
            const data = {
                gameState: this.loadGameState(),
                statistics: this.loadStatistics(),
                settings: this.loadSettings(),
                exportDate: new Date().toISOString()
            };

            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error('Failed to export data:', error);
            return null;
        }
    }

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);

            if (data.gameState) {
                this.saveGameState(data.gameState);
            }

            if (data.statistics) {
                this.saveStatistics(data.statistics);
            }

            if (data.settings) {
                this.saveSettings(data.settings);
            }

            return true;
        } catch (error) {
            console.error('Failed to import data:', error);
            return false;
        }
    }

    clearAllData() {
        try {
            localStorage.removeItem(this.GAME_STATE_KEY);
            localStorage.removeItem(this.STATISTICS_KEY);
            localStorage.removeItem(this.SETTINGS_KEY);
            return true;
        } catch (error) {
            console.error('Failed to clear all data:', error);
            return false;
        }
    }

    getStorageUsage() {
        try {
            let total = 0;

            const gameState = localStorage.getItem(this.GAME_STATE_KEY);
            const statistics = localStorage.getItem(this.STATISTICS_KEY);
            const settings = localStorage.getItem(this.SETTINGS_KEY);

            if (gameState) total += gameState.length * 2; // UTF-16 encoding
            if (statistics) total += statistics.length * 2;
            if (settings) total += settings.length * 2;

            return {
                bytes: total,
                kb: (total / 1024).toFixed(2),
                gameState: gameState ? gameState.length * 2 : 0,
                statistics: statistics ? statistics.length * 2 : 0,
                settings: settings ? settings.length * 2 : 0
            };
        } catch (error) {
            console.error('Failed to calculate storage usage:', error);
            return null;
        }
    }
}

if (typeof window !== 'undefined') {
    window.SudokuStorage = SudokuStorage;
}