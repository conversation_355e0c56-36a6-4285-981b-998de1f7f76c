{% extends "base.html" %}

{% block title %}Results · SwayamAI{% endblock %}

{% block content %}
<div class="sa-card p-4 p-md-5">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="h5 mb-0">Results</h2>
    <div class="d-flex align-items-center gap-2">
      {% if num_models is not none %}
        <small class="text-muted">Models trained: <strong>{{ num_models }}</strong></small>
      {% endif %}
      <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#accuracyTipsModal">
        <i class="bi bi-lightbulb me-1"></i> Improve accuracy
      </button>
    </div>
  </div>

  {% if error %}
    <div class="alert alert-danger">
      <h5 class="alert-heading">Error</h5>
      <div class="mb-2">{{ error }}</div>
      {% if traceback %}<pre class="mb-0 small bg-light p-2 rounded">{{ traceback }}</pre>{% endif %}
    </div>
  {% endif %}

  {% if summary_html %}
    <section id="summary" class="mb-4">
      <h3 class="h6">Summary</h3>
      <div class="mt-2">{{ summary_html|safe }}</div>
    </section>
  {% endif %}

  {% if selected_fields or selected_label or use_case == 'text_similarity' %}
    <section id="io_vars" class="mb-4">
      <h3 class="h6">Inputs and Target</h3>
      <div class="table-responsive">
        <table class="table table-sm table-bordered mb-0">
          <tbody>
            {% if selected_fields %}
              <tr>
                <th scope="row" style="width: 160px;">Selected input fields</th>
                <td>{{ selected_fields | join(', ') }}</td>
              </tr>
            {% endif %}
            {% if selected_label %}
              <tr>
                <th scope="row">Target</th>
                <td>{{ selected_label }}</td>
              </tr>
            {% endif %}
            {% if use_case == 'text_similarity' %}
              <tr>
                <th scope="row">Text column</th>
                <td>{{ text_column }}</td>
              </tr>
              {% if input_text %}
              <tr>
                <th scope="row">Input text</th>
                <td class="small">{{ input_text }}</td>
              </tr>
              {% endif %}
            {% endif %}
            {% if use_case == 'non_nlp_classification' and best_model_name %}
              <tr>
                <th scope="row">Best model</th>
                <td>
                  <div><strong>{{ best_model_name }}</strong></div>
                  {% if best_model_params_compact %}
                    <div class="text-muted small">{{ best_model_params_compact }}</div>
                  {% endif %}
                </td>
              </tr>
            {% endif %}
            {% if use_case == 'affinity' %}
              <tr>
                <th scope="row">Task</th>
                <td>Affinity Analysis</td>
              </tr>
            {% endif %}
          </tbody>
        </table>
      </div>
    </section>
  {% endif %}

  <section id="downloads" class="mb-4">
    <h3 class="h6">Downloads</h3>
    {% if download_files %}
      <ul class="list-group">
        {% for f in download_files %}
          <li class="list-group-item d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-2">
              <i class="bi bi-download"></i>
              <a href="{{ url_for('download', job_id=job_id, filename=f) }}" class="link-primary">{{ f }}</a>
            </div>
            <span class="badge bg-secondary">file</span>
          </li>
        {% endfor %}
      </ul>
    {% else %}
      <p class="text-muted">No files generated.</p>
    {% endif %}
  </section>

  {% if use_case == 'clustering' or use_case == 'non_nlp_clustering' %}
  <section id="guidance" class="mb-4">
    <h3 class="h6">How to use the clustering results</h3>
    <div class="small text-muted">
      <p class="mb-1">The Excel file <code>clustering_Results.xlsx</code> contains:</p>
      <ul>
  <li><strong>clusters</strong> sheet: top models with their metrics. Higher <em>Silhouette</em> and <em>Calinski–Harabasz</em> are better; lower <em>Davies–Bouldin</em> is better.</li>
  <li><strong>Top 3 clusters</strong> sheet: your input rows alongside cluster labels from the top models.</li>
      </ul>
      <p class="mb-1">Guidance:</p>
      <ul>
        <li>If <strong>Silhouette &lt; 0.5</strong> for a selected model, clustering may be weak; consider cleaning the text, removing duplicates, or adding more data.</li>
        <li>Check whether one cluster dominates (&gt;90% of rows). If so, tune inputs (e.g., remove boilerplate text) to reveal structure.</li>
  <li>Use <strong>Top 3 clusters</strong> to inspect cluster coherence and rename clusters as needed.</li>
      </ul>
    </div>
  </section>
  {% elif use_case == 'classification' %}
  <section id="guidance" class="mb-4">
    <h3 class="h6">How to use the classification results</h3>
    <div class="small text-muted">
      <p class="mb-1">The Excel file <code>classification_results.xlsx</code> contains:</p>
      <ul>
        <li><strong>results</strong>: cross-validation metrics per model (accuracy, F1-weighted, precision, recall).</li>
        <li><strong>validation</strong>: hold-out metrics to complement CV scores.</li>
        <li>Per-model <strong>cm_…</strong> (confusion matrices) and <strong>report_…</strong> (per-class metrics) sheets when available.</li>
      </ul>
      <p class="mb-1">Next steps:</p>
      <ul>
        <li>Prefer models with higher <em>F1-weighted</em> and strong validation scores.</li>
        <li>Review confusion matrices for systematic misclassifications; consider rebalancing data or cleaning text.</li>
        <li>Use the <code>sample_predictions.csv</code> to spot-check outputs on real rows.</li>
        <li>Artifacts in Downloads include the best model <code>*.pkl</code> and <code>label_encoder.pkl</code>; load both for deployment.</li>
      </ul>
  <p class="mb-1">Deployment (run locally after downloading artifacts to the same folder):</p>
      {% if best_model_name %}
      <div class="alert alert-info py-2 px-3 small mb-2">
        <strong>Chosen model:</strong> {{ best_model_name }}
      </div>
      <div class="text-muted small mb-2">
        <em>Naming tip</em>: e.g. <code>RF_TFIDF_SVD</code> means <strong>Random Forest</strong> on <strong>TF‑IDF</strong> features with <strong>SVD</strong> reduction (RF = Random Forest; TF‑IDF = term‑frequency/inverse‑document‑frequency; SVD = truncated SVD).
      </div>
      {% endif %}
  <div class="position-relative"> 
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-cls-nlp" class="language-powershell">python deployment_script_classification.py data.csv</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-cls-nlp')">Copy</button>
      </div>
      <p class="mb-0">Tip: You can still use flags, e.g. <code>--input data.csv --output preds.csv</code>. If <code>--output</code> is omitted, it defaults to <code>data_preds.csv</code> or <code>data_preds.xlsx</code>.</p>
  <p class="mb-0">Files expected next to your input: <code>swayamml.conf</code>, best model <code>*.pkl</code>, <code>label_encoder.pkl</code>, and embedding artifact(s) like <code>tfidf_vectorizer_ngram1.pkl</code> plus <code>tfidf_svd_*.pkl</code> (if used) or <code>word2vec_model.pkl</code>.</p>
    </div>
  </section>
  {% elif use_case == 'non_nlp_classification' %}
  <section id="guidance" class="mb-4">
    <h3 class="h6">How to use the classification results (numeric/categorical)</h3>
    <div class="small text-muted">
      <p class="mb-1">Generated files:</p>
      <ul>
        <li><strong>classification_results.xlsx</strong>: per-model metrics, confusion matrix, and a textual report for the best model.</li>
        <li><strong>classification_model.pkl</strong>: the best model pipeline including preprocessing (imputation, scaling, one-hot encoding).</li>
        <li><strong>sample_prediction_classification.csv</strong>: first 10 predictions with class probabilities.</li>
        <li><strong>deployment_script_non_nlp_classification.py</strong>: CLI to apply the model to new data.</li>
      </ul>
      {% if selected_fields %}
      <p class="mb-1">For deployment, ensure your input includes these feature columns: <code>{{ selected_fields | join(', ') }}</code>.</p>
      {% endif %}
      <p class="mb-1">Deployment (run locally from the output folder):</p>
      <div class="position-relative">
  <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-non-nlp-cls" class="language-powershell">python deployment_script_non_nlp_classification.py data.csv</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-non-nlp-cls')">Copy</button>
      </div>
      <p class="mb-0">Files expected next to your input: <code>classification_model.pkl</code> and <code>deployment_script_non_nlp_classification.py</code>. The saved pipeline already includes preprocessing.</p>

      <div class="mt-3">
        <div class="fw-semibold">Model glossary</div>
        <ul class="mb-0">
          <li><strong>Logistic Regression</strong>: linear classifier with L2/L1 regularization.</li>
          <li><strong>Stochastic Gradient Descent</strong>: linear classifier trained with SGD (can behave like logreg/SVM depending on loss).</li>
          <li><strong>Random Forest</strong>: ensemble of decision trees averaged to reduce variance; robust to outliers.</li>
          <li><strong>Extra Trees</strong>: like Random Forest but with more randomness; often faster/stronger on tabular data.</li>
          <li><strong>Gradient Boosting</strong>: sequential trees focusing on correcting previous errors; strong on structured data.</li>
          <li><strong>SVC</strong>: Support Vector Classifier with kernels; good for smaller/medium datasets.</li>
          <li><strong>XGBoost</strong>: optimized gradient boosting (if available) with multi-class support.</li>
        </ul>
      </div>
    </div>
  </section>
  {% elif use_case == 'regression' %}
  <section id="guidance" class="mb-4">
    <h3 class="h6">How to use the regression results</h3>
    <div class="small text-muted">
      {% if selection_policy %}
        <p class="mb-2">Selection policy: <code>{{ selection_policy }}</code> (test = best on hold-out Adjusted R2; cv = best by CV mean R2)</p>
      {% endif %}
      <p class="mb-1">The Excel file <code>regression_results*.xlsx</code> can include:</p>
      <ul>
        <li><strong>results</strong>: metrics computed on the held-out test set (MSE, MAE, R2, Adjusted R2).</li>
        <li><strong>validation</strong>: per-model test metrics in a compact table.</li>
        <li><strong>cv_grid_results</strong> (optional): GridSearchCV means/stdev across folds for all parameter combinations (used to pick hyperparameters).</li>
        <li><strong>final_features</strong>: the final selected feature list used for training/prediction (includes engineered features).</li>
        <li><strong>run_config</strong>: inputs/target, artifact filenames (model, encoder, imputer, scaler, and embedding if applicable).</li>
      </ul>
      <p class="mb-1">Deployment (run locally after downloading artifacts to the same folder):</p>
      <ul>
        <li><strong>NON-NLP (numeric/categorical only)</strong></li>
      </ul>
      <div class="position-relative">
  <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-non-nlp" class="language-powershell">python deployment_script_regression_non_nlp.py data.csv</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-non-nlp')">Copy</button>
      </div>
      <ul>
        <li><strong>NLP/Mixed (TF‑IDF or Word2Vec + numeric)</strong></li>
      </ul>
      <div class="position-relative">
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-mixed" class="language-powershell"># Flags
python deployment_script_regression.py --input data.csv --output preds.csv
# Or positional
python deployment_script_regression.py data.csv preds.csv</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-mixed')">Copy</button>
      </div>
      <div class="text-muted small mb-2">
        Tips:
        <ul class="mb-1">
          <li>If Cleaned_data is missing, the script concatenates all object columns.</li>
          <li>Override numeric schema when needed: <code>--features "F1,F2,..."</code> and/or <code>--numerical-cols "N1,N2,..."</code>.</li>
          <li>If <code>--output</code> is omitted, it defaults to <code>data_preds.csv</code> or <code>data_preds.xlsx</code>.</li>
        </ul>
      </div>
      <p class="mb-0">Files the script expects next to your input file: <code>swayamml.conf</code>, the best model <code>*.pkl</code>, <code>encoding.pkl</code>, <code>imputer.pkl</code>, <code>scaler.pkl</code>, and, for NLP/Mixed, the embedding artifact (e.g., <code>tfidf_vectorizer_ngram1.pkl</code> or <code>word2vec_model.pkl</code>).</p>
    </div>
  </section>
  {% elif use_case == 'affinity' %}
  <section id="guidance-affinity" class="mb-4">
    <h3 class="h6">How to use the affinity results</h3>
    <div class="small text-muted">
      <p class="mb-1">Generated files:</p>
      <ul>
        <li><strong>affinity_itemsets.csv</strong>: frequent itemsets and their <em>Support</em>.</li>
        <li><strong>affinity_rules.csv</strong> (or <strong>affinity_rules.db</strong> if there are more than 10,000 rules; a <strong>affinity_rules_top10k.csv</strong> preview is also included): associations shown as <em>Antecedent</em> → <em>Consequent</em>.</li>
        <li><strong>affinity_results.xlsx</strong>: Excel workbook with two sheets: <em>itemsets</em> and <em>rules</em>.</li>
        <li><strong>affinity_baskets.csv</strong>: basket-to-items mapping for drill‑down. The first column is your selected Index column (or a generated <code>row_id</code>).</li>
      </ul>
      <p class="mb-1">Columns in <em>rules</em>:</p>
      <ul>
        <li><strong>Antecedent</strong>: items on the left-hand side (if present).</li>
        <li><strong>Consequent</strong>: items on the right-hand side (predicted to co-occur).</li>
        <li><strong>Support</strong>: fraction of baskets containing all items in the rule.</li>
        <li><strong>Confidence</strong>: P(Consequent | Antecedent).</li>
        <li><strong>Lift</strong>: how many times more likely the consequent appears with the antecedent vs. at random (lift &gt; 1 indicates positive association).</li>
        <li><strong>Leverage</strong>: difference between observed and expected joint frequency (<code>support(A∪C) − support(A)×support(C)</code>).</li>
        <li><strong>Conviction</strong>: strength of implication considering how often the rule would be wrong; higher is stronger.</li>
      </ul>
      <p class="mb-1">Tips:</p>
      <ul>
        <li>Sort by <em>Lift</em> (desc) then <em>Confidence</em> to find the most actionable rules.</li>
        <li>Use <strong>affinity_baskets.csv</strong> to inspect specific baskets that triggered a rule.</li>
        <li>If you see too many/few rules, adjust <em>Min support</em>, <em>Min confidence</em>, or add a <em>Min lift</em> filter and rerun.</li>
      </ul>
      <p class="mb-1">How rules are generated and why you might see only a few:</p>
      <ul>
        <li>We first mine frequent itemsets using your <em>Min support</em> threshold and chosen algorithm (Apriori or FP‑Growth).</li>
        <li>From these itemsets, we generate association rules that meet <em>Min confidence</em> (and optional <em>Min lift</em>).</li>
        <li>If only a handful of rules appear (e.g., 3), it typically means item co‑occurrence is sparse at your thresholds. Try lowering <em>Min support</em> (e.g., 0.02 → 0.01) or <em>Min confidence</em>, or remove a strict <em>Min lift</em> to discover more rules.</li>
      </ul>

      <h4 class="h6 mt-3">Use the SQLite DB (if generated)</h4>
      <p class="mb-1">When the rules exceed 10,000, we save them to <code>affinity_rules.db</code> (table: <code>rules</code>). You can query or export with Python or the <code>sqlite3</code> CLI.</p>

      <div class="mb-2 small">Option A — Python (recommended):</div>
      <div class="position-relative mb-2">
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-db-preview" class="language-powershell">python -c 'import sqlite3, pandas as pd; con=sqlite3.connect("affinity_rules.db"); q="SELECT * FROM rules ORDER BY Lift DESC, Confidence DESC LIMIT 20"; df=pd.read_sql(q, con); print(df.to_string(index=False))'</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-db-preview')">Copy</button>
      </div>
      <div class="position-relative mb-2">
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-db-count" class="language-powershell">python -c 'import sqlite3; con=sqlite3.connect("affinity_rules.db"); cur=con.cursor(); cur.execute("SELECT COUNT(*) FROM rules"); print(cur.fetchone()[0])'</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-db-count')">Copy</button>
      </div>
      <div class="position-relative mb-3">
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-db-export" class="language-powershell">python -c 'import sqlite3, pandas as pd; con=sqlite3.connect("affinity_rules.db"); q="SELECT * FROM rules ORDER BY Lift DESC, Confidence DESC LIMIT 100000"; pd.read_sql(q, con).to_csv("rules_export.csv", index=False)'</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-db-export')">Copy</button>
      </div>

      <div class="mb-2 small">Option B — sqlite3 CLI (if installed):</div>
      <div class="position-relative mb-2">
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-cli-schema" class="language-powershell">sqlite3 .\affinity_rules.db ".schema rules"</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-cli-schema')">Copy</button>
      </div>
      <div class="position-relative mb-2">
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-cli-count" class="language-powershell">sqlite3 .\affinity_rules.db "SELECT COUNT(*) FROM rules;"</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-cli-count')">Copy</button>
      </div>
      <div class="position-relative mb-2">
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-cli-top" class="language-powershell">sqlite3 .\affinity_rules.db "SELECT * FROM rules ORDER BY Lift DESC, Confidence DESC LIMIT 20;"</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-cli-top')">Copy</button>
      </div>
      <div class="position-relative mb-3">
        <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-cli-export" class="language-powershell">sqlite3 .\affinity_rules.db -header -csv "SELECT * FROM rules ORDER BY Lift DESC, Confidence DESC LIMIT 100000" &gt; rules_export.csv</code></pre>
        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-cli-export')">Copy</button>
      </div>
    </div>
  </section>
  {% elif use_case == 'anomaly_numeric' %}
  <section id="guidance" class="mb-4">
    <h3 class="h6">How to use the anomaly detection results</h3>
    <div class="small text-muted">
  <p class="mb-1">The pipeline evaluates multiple detectors (Isolation Forest, KNN, LOF, COPOD, ROD, One‑Class SVM) and selects the best by <em>Silhouette</em> score (tie‑breaker: Calinski–Harabasz).</p>
  {% if anomaly_mode %}
  <p class="mb-2"><strong>Detection mode:</strong> {{ 'All numeric columns together (multivariate)' if anomaly_mode == 'all' else 'Per-column (univariate)' }}</p>
  {% endif %}
  {% if use_case == 'text_similarity' %}
  <section id="guidance-text-sim" class="mb-4">
    <h3 class="h6">How to use the text similarity results</h3>
    <div class="small text-muted">
  <p class="mb-1">If you provided an input text, <code>text_similarity.csv</code> contains the optional index column (or generated <code>row_id</code>), the dataset text, your input text, and a cosine similarity score in [-1, 1].</p>
  <p class="mb-1">If you left input text empty, <code>text_similarity_pairs.csv</code> lists pairwise similarities among rows for the selected column. Large datasets may be capped for performance.</p>
      <ul>
        <li>Higher values indicate more similar meaning; near 0 suggests unrelated content.</li>
        <li>Use a domain-appropriate threshold (e.g., ≥ 0.7) to flag similar pairs.</li>
      </ul>
    </div>
  </section>
  {% endif %}
      <ul>
        <li><strong>anomalies.csv</strong> (multivariate mode): original numeric columns plus <code>is_anomaly</code> and <code>anomaly_score</code>.</li>
        <li><strong>anomalies_per_column.csv</strong> (per‑column mode): per‑feature flags like <code>&lt;col&gt;_is_anomaly</code> and <code>&lt;col&gt;_score</code>.</li>
        <li><strong>anomaly_results.xlsx</strong> or <strong>anomaly_per_column_results.xlsx</strong>: metrics and model summary.</li>
      </ul>
      <p class="mb-1">Next steps:</p>
      <ul>
        <li>Inspect high <em>anomaly_score</em> rows and consider domain validation.</li>
        <li>If too many/few anomalies, consider adjusting selected fields or running per‑column mode.</li>
      </ul>
  <p class="mb-1">Deployment (run locally after downloading artifacts to the same folder):</p>
      <div class="mb-2">
        <div class="fw-semibold small mb-1">Multivariate (all numeric columns):</div>
        <div class="position-relative">
          <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-anom" class="language-powershell">python deployment_script_anomaly_numeric.py data.csv</code></pre>
          <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-anom')">Copy</button>
        </div>
        <div class="text-muted small">Tips:
          <ul class="mb-1">
            <li>Flags work too: <code>--input data.csv --output anomalies.xlsx</code>.</li>
            <li>If feature counts differ from training, the script now supports:
              <ul class="mb-1">
                <li><code>feature_columns.json</code> or <code>feature_columns.txt</code> next to artifacts (preferred).</li>
                <li><code>--features "Col1,Col2,..."</code> to specify training order explicitly.</li>
                <li><code>--allow-mismatch</code> to auto‑truncate or pad with NaNs.</li>
              </ul>
            </li>
            <li>If <code>--output</code> is omitted, it defaults based on input extension.</li>
          </ul>
        </div>
      </div>
      <div class="mb-2">
        <div class="fw-semibold small mb-1">Per‑column (univariate for one feature):</div>
        <div class="position-relative">
          <pre class="mb-2 small bg-light p-2 rounded"><code id="cmd-anom-col" class="language-powershell">python deployment_script_anomaly_numeric_per_column.py data.csv ColumnName</code></pre>
          <button type="button" class="btn btn-sm btn-outline-secondary position-absolute" style="top:4px; right:4px;" onclick="copyCmd('cmd-anom-col')">Copy</button>
        </div>
        <div class="text-muted small">Artifacts expected: <code>imputer_ColumnName.pkl</code>, <code>scaler_ColumnName.pkl</code>, and <code>anomaly_model_ColumnName.pkl</code> (or use <code>--artifact-name</code> if names differ). Output keeps all original columns plus <code>ColumnName_is_anomaly</code> and <code>ColumnName_score</code>.</div>
      </div>
      <p class="mb-0">Files expected next to your input (multivariate mode): <code>imputer.pkl</code>, <code>scaler.pkl</code>, <code>anomaly_model.pkl</code>, and <code>deployment_script_anomaly_numeric.py</code>. Per‑column mode uses column‑specific artifacts and <code>deployment_script_anomaly_numeric_per_column.py</code>.</p>
    </div>
  </section>
  {% endif %}
  {% if use_case == 'clustering' %}
  <!-- Clustering deployment guidance intentionally removed -->
  {% endif %}

  {% if show_processing_log and processing_logger %}
    <section id="processing_log" class="mb-4">
      <h3 class="h6">Processing Log</h3>
      <pre class="bg-gray-100 p-3 rounded small overflow-auto">{{ processing_logger | tojson(indent=2) }}</pre>
    </section>
  {% endif %}

  <div class="d-flex justify-content-end">
    <a href="{{ url_for('swayam_ml') }}" class="btn btn-outline-primary">Run another job</a>
  </div>
  <!-- Accuracy Tips Modal -->
  <div class="modal fade" id="accuracyTipsModal" tabindex="-1" aria-labelledby="accuracyTipsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="accuracyTipsModalLabel">Improve model accuracy</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Consider these practical steps to improve performance on your next run:</p>
        <ul>
          <li><strong>Manage outliers:</strong> Detect and mitigate extreme values using IQR/z-score rules, winsorization, or robust scalers. Outliers can distort loss and feature scaling.</li>
          <li><strong>Increase dataset size:</strong> More representative data reduces variance and improves generalization. Add labeled examples, cover rare classes, or augment safely.</li>
          <li><strong>Create meaningful features:</strong> Engineer domain-informed signals (ratios, interactions, log/box-cox transforms, date parts). For text, consider better embeddings or cleaning.</li>
          <li><strong>Select relevant features:</strong> Remove leaky or redundant variables, reduce multicollinearity, and focus on impactful predictors (filter/embedded methods, importance ranks).</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function copyCmd(id){
  try {
    const el = document.getElementById(id);
    const txt = el ? el.textContent : '';
    if (!txt) return;
    navigator.clipboard.writeText(txt);
  } catch(e) { /* ignore */ }
}
</script>
{% endblock %}
