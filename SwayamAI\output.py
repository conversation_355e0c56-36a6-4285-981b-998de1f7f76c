import os
import re
import pandas as pd
import joblib
import logging
from sklearn.metrics import confusion_matrix, classification_report
from typing import Tu<PERSON>, Dict, Any, Optional

logging.getLogger(__name__)

def _sanitize_sheet_name(name: str) -> str:
    n = re.sub(r'[^A-Za-z0-9 _-]', '_', name)[:31]
    return n or "Sheet"

class BaseExcelMixin:
    def _load_data_from_excel(self, file_path, sheet_name=None):
        if not os.path.exists(file_path):
            raise FileNotFoundError(file_path)
        return pd.read_excel(file_path, sheet_name=sheet_name)

    def _write_data_to_excel(self, df: pd.DataFrame, file_path: str, sheet_name: str, mode: str = "w"):
        # mode 'w' will overwrite; mode 'a' will append sheet
        sheet = _sanitize_sheet_name(sheet_name)
        try:
            if mode == "w" or not os.path.exists(file_path):
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name=sheet, index=False)
            else:
                # append/replace sheet using pandas-native append mode
                try:
                    with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                        df.to_excel(writer, sheet_name=sheet, index=False)
                except TypeError:
                    # older pandas without if_sheet_exists
                    with pd.ExcelWriter(file_path, engine='openpyxl', mode='a') as writer:
                        df.to_excel(writer, sheet_name=sheet, index=False)
        except Exception:
            # Fallback to CSV next to the intended Excel
            base, _ = os.path.splitext(file_path)
            csv_path = base + ("_" + sheet if sheet else "") + ".csv"
            try:
                df.to_csv(csv_path, index=False)
            except Exception:
                pass

class DuplicateCodeDetectionOutput(BaseExcelMixin):
    def generate_excel_output(self, results_df: pd.DataFrame, results_file_path: str):
        self._write_data_to_excel(results_df, results_file_path, sheet_name='duplicates', mode='w')

    def get_summary(self, results_df: pd.DataFrame) -> pd.DataFrame:
        return results_df.describe()

class ClusterOutput(BaseExcelMixin):
    def __init__(self):
        pass

    def generate_clustering_output(self, data: pd.DataFrame, results: pd.DataFrame, output_result_file: str, fields: list, top_models: Optional[pd.DataFrame] = None):
        """
        Persist clustering outputs into an Excel workbook:
        - 'clusters': table of all evaluated models and their metrics (results)
        - 'Top 3 clusters': original/input columns plus per-row cluster labels for the top 3 models

        If top_models is provided, it's used to choose which model columns to include in 'Top 3 clusters'.
        Otherwise, falls back to using 'results'.
        """
        with pd.ExcelWriter(output_result_file, engine='openpyxl') as writer:
            # 1) All models and metrics
            try:
                results.to_excel(writer, sheet_name='clusters', index=False)
            except Exception:
                pd.DataFrame(results).to_excel(writer, sheet_name='clusters', index=False)

            # 2) Per-row cluster labels for top models only
            try:
                # Fallback: if top_models empty or None, take top 3 from results
                src = top_models
                if src is None or (hasattr(src, 'empty') and src.empty) or ('Model Name' not in getattr(src, 'columns', [])):
                    try:
                        src = results.head(3)
                    except Exception:
                        src = results

                # Build robust mapping from model names -> actual dataframe columns
                model_cols: list[str] = []
                if src is not None and hasattr(src, 'empty') and not src.empty and 'Model Name' in src.columns:
                    # Normalize helper to increase matching tolerance
                    def _norm(s: str) -> str:
                        s = str(s)
                        s = s.strip().lower()
                        # remove spaces and common punctuation
                        s = re.sub(r"[\s\-_.()\[\]]+", "", s)
                        return s

                    model_names = [str(m) for m in src['Model Name'].tolist()]
                    # First, try exact matches
                    exact_hits = [m for m in model_names if m in data.columns]
                    # If some are missing, try normalized matching against data.columns
                    if len(exact_hits) < len(model_names):
                        norm_to_col = {}
                        for c in data.columns:
                            norm_to_col[_norm(c)] = c
                        for m in model_names:
                            if m in data.columns:
                                continue
                            nm = _norm(m)
                            if nm in norm_to_col:
                                exact_hits.append(norm_to_col[nm])
                    # Preserve original order and drop duplicates
                    seen = set()
                    for c in exact_hits:
                        if c not in seen:
                            seen.add(c)
                            model_cols.append(c)

                # Identify all model columns present in data (based on results table) to separate original columns
                all_model_cols: list[str] = []
                try:
                    if 'Model Name' in results.columns:
                        candidate_names = [str(m) for m in results['Model Name'].tolist()]
                        all_model_cols = [m for m in candidate_names if m in data.columns]
                except Exception:
                    all_model_cols = []

                # Original/base columns = everything except any clustering model columns
                base_cols = [c for c in data.columns if c not in set(all_model_cols)]
                # Final Top 3 view = original columns + only the selected top model columns (if any)
                top_df = data[base_cols + model_cols].copy()

                # Also add 3 summary columns with the cluster number per row for Top 1/2/3 models
                try:
                    for idx, mcol in enumerate(model_cols[:3], start=1):
                        safe_col = str(mcol)
                        if safe_col in data.columns:
                            top_df[f'Top {idx} cluster'] = data[safe_col].values
                except Exception:
                    pass

                # Write the primary Top 3 sheet
                top3_sheet = _sanitize_sheet_name('Top 3 clusters')
                top_df.to_excel(writer, sheet_name=top3_sheet, index=False)
                # Do not write legacy alias to avoid duplicate sheets; deployment script already supports fallback
            except Exception as e:
                logging.error(f"Failed to write Top 3 clusters sheet: {e}")

    def get_summary(self, Results: pd.DataFrame) -> pd.DataFrame:
        return Results.describe()

    def get_model_and_feature_importance(self, best_model_name: str, data, features: list, output_folder: str):
        return None

    def permutation_importance(self, X, model, num_permutations: int = 10):
        return {}

class ClassificationOutput(BaseExcelMixin):
    def __init__(self):
        pass

    def generate_classification_excel_output(self, Results: pd.DataFrame, validation_Results: pd.DataFrame, file_path: str, eval_details=None, class_names=None):
        # write results + validation
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            Results.to_excel(writer, sheet_name='results', index=False)
            validation_Results.to_excel(writer, sheet_name='validation', index=False)

            # extra: per-model confusion matrix and per-class metrics
            if eval_details:
                # bound class_names if provided
                labels = list(range(len(class_names))) if class_names is not None else None
                target_names = list(class_names) if class_names is not None else None
                for i, ed in enumerate(eval_details):
                    model_name = ed.get('model_name', f'model_{i}')
                    y_true = ed['y_true']
                    y_pred = ed['y_pred']

                    # confusion matrix
                    cm = confusion_matrix(y_true, y_pred, labels=labels)
                    if target_names is None:
                        # derive sorted unique labels as strings
                        uniq = sorted(set(y_true) | set(y_pred))
                        tn = [str(u) for u in uniq]
                        cm_df = pd.DataFrame(cm, index=tn, columns=tn)
                    else:
                        cm_df = pd.DataFrame(cm, index=target_names, columns=target_names)
                    cm_sheet = _sanitize_sheet_name(f'cm_{model_name[:20]}')
                    cm_df.to_excel(writer, sheet_name=cm_sheet)

                    # per-class metrics
                    report = classification_report(y_true, y_pred, labels=labels, target_names=target_names, output_dict=True, zero_division=0)
                    report_df = pd.DataFrame(report).transpose()
                    rp_sheet = _sanitize_sheet_name(f'report_{model_name[:20]}')
                    report_df.to_excel(writer, sheet_name=rp_sheet)

    def get_summary(self, Results: pd.DataFrame) -> pd.DataFrame:
        return Results.describe()

    def generate_test_prediction_excel(self, Data: pd.DataFrame, embedding_name: str, model_pkl_file_name: str,
                                       embedding_pkl_file_name: str, label_pkl_file_name: str,
                                       embeddings_module, fields: list, label: str,
                                       output_folder: str, sample_prediction_file_name: str):
        # load model, embeddings and label encoder then predict and save
        model_path = os.path.join(output_folder or '.', model_pkl_file_name)
        label_path = os.path.join(output_folder or '.', label_pkl_file_name)
        model = joblib.load(model_path)
        label_encoder = joblib.load(label_path)

        # produce features
        if embedding_name.startswith('tfidf_svd'):
            X_sparse = embeddings_module.embeddings_tfidf_test_data(Data['Cleaned_data'], sparse_return=True)
            X_feat = embeddings_module.svd_transform(X_sparse)
        elif embedding_name.startswith('tfidf'):
            X_feat = embeddings_module.embeddings_tfidf_test_data(Data['Cleaned_data'], sparse_return=False)
        elif embedding_name.startswith('w2v'):
            X_feat = embeddings_module.embeddings_word2vec_test_data(Data['Cleaned_data'])
        elif embedding_name.startswith('bert'):
            X_feat = embeddings_module.embeddings_bert(Data['Cleaned_data'])
        else:
            X_feat = embeddings_module.embeddings_tfidf_test_data(Data['Cleaned_data'], sparse_return=False)

        preds = model.predict(X_feat)
        decoded = label_encoder.inverse_transform(preds)
        out_df = Data.copy()
        out_df['predicted_label_encoded'] = preds
        out_df['predicted_label'] = decoded
        output_file = os.path.join(output_folder or '.', sample_prediction_file_name)
        out_df.to_csv(output_file, index=False)
        return output_file

class NonNlpClassificationOutput(ClassificationOutput):
    def generate_classification_excel_output(self, Results: pd.DataFrame, validation_Results: pd.DataFrame, file_path: str):
        super().generate_classification_excel_output(Results, validation_Results, file_path)

    def generate_test_prediction_excel(self, Data: pd.DataFrame, model_pkl_file_name: str,
                                       numerical_cols_scaled: dict, fields: list, label: str,
                                       output_folder: str, label_pkl_file_name: str, sample_prediction_file_name: str):
        model_path = os.path.join(output_folder or '.', model_pkl_file_name)
        label_path = os.path.join(output_folder or '.', label_pkl_file_name)
        model = joblib.load(model_path)
        label_encoder = joblib.load(label_path)
        # assume Data already preprocessed numeric
        preds = model.predict(Data[fields].values)
        decoded = label_encoder.inverse_transform(preds)
        out_df = Data.copy()
        out_df['predicted_label_encoded'] = preds
        out_df['predicted_label'] = decoded
        output_file = os.path.join(output_folder or '.', sample_prediction_file_name)
        out_df.to_csv(output_file, index=False)
        return output_file

# Other classes kept minimal / unchanged (stubs)
class NonNLPRegressionOutput(BaseExcelMixin):
    def _write_results(self, Results: pd.DataFrame, validation_Results: Dict[str, Dict[str, Any]], file_path: str, grid_rows: Optional[pd.DataFrame] = None, final_features: Optional[list] = None, run_config: Optional[Dict[str, Any]] = None):
        # If CSV path provided, write Results CSV and a separate validation CSV; else write to Excel with two sheets
        try:
            if file_path.lower().endswith('.csv'):
                Results.to_csv(file_path, index=False)
                try:
                    val_df = pd.DataFrame(validation_Results).T.reset_index().rename(columns={'index': 'Model Name'})
                    val_path = file_path.replace('.csv', '_validation.csv')
                    val_df.to_csv(val_path, index=False)
                except Exception:
                    pass
                # write grid rows CSV fallback
                try:
                    if grid_rows is not None:
                        gr = pd.DataFrame(grid_rows)
                        if not gr.empty:
                            gr_path = file_path.replace('.csv', '_cv_grid_results.csv')
                            gr.to_csv(gr_path, index=False)
                except Exception:
                    pass
            else:
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    Results.to_excel(writer, sheet_name='results', index=False)
                    try:
                        val_df = pd.DataFrame(validation_Results).T.reset_index().rename(columns={'index': 'Model Name'})
                        val_df.to_excel(writer, sheet_name='validation', index=False)
                    except Exception:
                        pass
                    # Optionally write grid search combinations
                    try:
                        if grid_rows is not None:
                            gr = pd.DataFrame(grid_rows)
                            if not gr.empty:
                                gr.to_excel(writer, sheet_name='cv_grid_results', index=False)
                    except Exception:
                        pass
                    # Optionally write final selected features
                    try:
                        if isinstance(final_features, (list, tuple)) and len(final_features) > 0:
                            ff_df = pd.DataFrame({'feature': list(final_features)})
                            ff_df.to_excel(writer, sheet_name='final_features', index=False)
                    except Exception:
                        pass
                    # Optionally write run configuration (inputs, target, artifacts)
                    try:
                        if isinstance(run_config, dict) and run_config:
                            rc = pd.DataFrame([
                                { 'key': k, 'value': ', '.join(v) if isinstance(v, (list, tuple)) else v }
                                for k, v in run_config.items()
                            ])
                            rc.to_excel(writer, sheet_name='run_config', index=False)
                    except Exception:
                        pass
        except Exception:
            base, _ = os.path.splitext(file_path)
            try:
                Results.to_csv(base + '_results.csv', index=False)
            except Exception:
                pass
            try:
                val_df = pd.DataFrame(validation_Results).T.reset_index().rename(columns={'index': 'Model Name'})
                val_df.to_csv(base + '_validation.csv', index=False)
            except Exception:
                pass

    def _best_model_from_validation(self, validation_Results: Dict[str, Dict[str, Any]]) -> Tuple[str, float]:
        # Choose best by adjusted R2 when available
        if not validation_Results:
            return "", float('nan')
        best_name = ""
        best_val = float('-inf')
        for name, metrics in validation_Results.items():
            try:
                val = float(metrics.get('adj_r2', metrics.get('r2', float('-inf'))))
            except Exception:
                val = float('-inf')
            if val > best_val:
                best_val = val
                best_name = name
        return best_name, best_val

    def generate_regression_excel_output(self, Results: pd.DataFrame, validation_Results: Dict[str, Dict[str, Any]], file_path: str, *, total_combinations: Optional[int] = None, grid_rows: Optional[pd.DataFrame] = None, final_features: Optional[list] = None, run_config: Optional[Dict[str, Any]] = None, selected_model_override: Optional[str] = None):
        # Persist results and return (summary_df, best_model_pkl_file_name, embedding_name)
        self._write_results(Results, validation_Results, file_path, grid_rows=grid_rows, final_features=final_features, run_config=run_config)
        # Pick using adjusted R2 (fallback to R2 if missing)
        if selected_model_override:
            best_model_name = selected_model_override
        else:
            best_model_name, _ = self._best_model_from_validation(validation_Results)
        if not best_model_name:
            try:
                df = pd.DataFrame(Results)
                use_col = 'validation_adjusted_r2_score' if 'validation_adjusted_r2_score' in df.columns else (
                    'validation_r2_score' if 'validation_r2_score' in df.columns else None
                )
                if use_col:
                    idx = df[use_col].astype(float).idxmax()
                    best_model_name = str(df.loc[idx, 'Model Name'])
            except Exception:
                best_model_name = None  # type: ignore[assignment]
        # Model file naming follows modelling._fit_eval_save convention
        model_pkl_file_name = f"{best_model_name.replace(' ', '_')}_model.pkl" if best_model_name else None
        # Non-NLP flow has no embedding
        embedding_name = None

        # Build a concise summary showing the selected model and its metrics
        def _summary_df():
            sel = str(best_model_name) if best_model_name else None
            mse = mae = r2 = adj_r2 = None
            best_params = ''
            if sel and isinstance(validation_Results, dict) and sel in validation_Results:
                m = validation_Results.get(sel, {}) or {}
                mse = m.get('mse')
                mae = m.get('mae')
                r2 = m.get('r2')
                adj_r2 = m.get('adj_r2')
            elif sel and isinstance(Results, pd.DataFrame) and not Results.empty:
                try:
                    row = Results[Results['Model Name'] == sel].iloc[0]
                    best_params = row.get('best_params', '') if 'best_params' in Results.columns else ''
                    mse = row.get('validation_mean_squared_error')
                    mae = row.get('validation_mean_absolute_error')
                    r2 = row.get('validation_r2_score')
                    adj_r2 = row.get('validation_adjusted_r2_score', r2)
                except Exception:
                    pass
            # If still empty, try read best_params by matching startswith model name
            if not best_params and isinstance(Results, pd.DataFrame) and 'best_params' in Results.columns and sel:
                try:
                    match = Results[Results['Model Name'].astype(str).str.startswith(sel)].iloc[0]
                    best_params = match.get('best_params', '')
                except Exception:
                    pass
            return pd.DataFrame([{
                'selected_model': sel or '',
                'selected_model_params': best_params,
                'adjusted_r2': adj_r2 if adj_r2 is not None else '',
                'r2': r2 if r2 is not None else '',
                'mse': mse if mse is not None else '',
                'mae': mae if mae is not None else '',
                'num_models': (int(total_combinations) if total_combinations is not None else ((len(Results) if isinstance(Results, pd.DataFrame) else None))) or ''
            }])

        summary = _summary_df()
        return summary, model_pkl_file_name, embedding_name

    def generate_test_prediction_excel(self, Data: pd.DataFrame, model_pkl_file_name: str,
                                       numerical_cols_scaled: dict, features: list, target: str,
                                       output_folder: str, sample_prediction_file_name: str):
        # Load model and predict numeric targets on numeric features
        model_path = os.path.join(output_folder or '.', model_pkl_file_name)
        model = joblib.load(model_path)
        # Prefer the explicit features list (includes encoded categoricals and any engineered features)
        feat_cols = list(features) if features else (list(numerical_cols_scaled.keys()) if isinstance(numerical_cols_scaled, dict) else [])
        X = Data[feat_cols]
        preds = model.predict(X)
        out_df = Data.copy()
        out_df['predicted_value'] = preds
        if target in Data.columns:
            out_df['actual_value'] = Data[target]
        out_file = os.path.join(output_folder or '.', sample_prediction_file_name)
        try:
            if out_file.lower().endswith('.csv'):
                out_df.to_csv(out_file, index=False)
            else:
                with pd.ExcelWriter(out_file, engine='openpyxl') as writer:
                    out_df.to_excel(writer, index=False)
        except Exception:
            out_df.to_csv(out_file.replace('.xlsx', '.csv'), index=False)
        return out_file

    # Optional hooks used by callers; return None if not implemented
    def generate_feature_importance(self, features: list, output_folder: str, model_pkl_file_name: str):
        """Compute and persist feature importance for supported regressors.

        Supports estimators with .feature_importances_ (tree-based) or .coef_ (linear).
        Writes feature_importance.csv in output_folder and returns its path.
        """
        try:
            model_path = os.path.join(output_folder or '.', model_pkl_file_name)
            model = joblib.load(model_path)
        except Exception:
            logging.warning("Failed to load model for feature importance: %s", model_pkl_file_name)
            return None

        import numpy as _np
        import pandas as _pd
        names = list(features) if features else []
        imp = None
        try:
            if hasattr(model, 'feature_importances_'):
                imp = _np.asarray(getattr(model, 'feature_importances_'))
            elif hasattr(model, 'coef_'):
                coef = getattr(model, 'coef_')
                imp = _np.abs(_np.ravel(coef))  # absolute coefficients
        except Exception:
            imp = None

        if imp is None:
            logging.info("Model has no feature_importances_ or coef_; skipping feature importance generation")
            return None

        # Align names length
        if len(names) != len(imp):
            names = [f"f{i}" for i in range(len(imp))]
        df = _pd.DataFrame({
            'feature': names,
            'importance': imp
        }).sort_values('importance', ascending=False)
        out_path = os.path.join(output_folder or '.', 'feature_importance.csv')
        try:
            df.to_csv(out_path, index=False)
        except Exception:
            try:
                df.to_excel(out_path.replace('.csv', '.xlsx'), index=False)
            except Exception:
                pass
        return out_path

    def generate_shap_values(self, train_data: pd.DataFrame, model_pkl_file_name: str, output_folder: str):
        return None

class NLPRegressionOutput(BaseExcelMixin):
    def _write_results(self, Results: pd.DataFrame, validation_Results: Dict[str, Dict[str, Any]], file_path: str, grid_rows: Optional[pd.DataFrame] = None, final_features: Optional[list] = None, run_config: Optional[Dict[str, Any]] = None):
        # If CSV, write Results CSV and separate validation CSV; else Excel sheets
        try:
            if file_path.lower().endswith('.csv'):
                Results.to_csv(file_path, index=False)
                try:
                    val_df = pd.DataFrame(validation_Results).T.reset_index().rename(columns={'index': 'Model Name'})
                    val_path = file_path.replace('.csv', '_validation.csv')
                    val_df.to_csv(val_path, index=False)
                except Exception:
                    pass
                # write grid rows CSV fallback
                try:
                    if grid_rows is not None:
                        gr = pd.DataFrame(grid_rows)
                        if not gr.empty:
                            gr_path = file_path.replace('.csv', '_cv_grid_results.csv')
                            gr.to_csv(gr_path, index=False)
                except Exception:
                    pass
            else:
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    Results.to_excel(writer, sheet_name='results', index=False)
                    try:
                        val_df = pd.DataFrame(validation_Results).T.reset_index().rename(columns={'index': 'Model Name'})
                        val_df.to_excel(writer, sheet_name='validation', index=False)
                    except Exception:
                        pass
                    # Optionally write grid search combinations
                    try:
                        if grid_rows is not None:
                            gr = pd.DataFrame(grid_rows)
                            if not gr.empty:
                                gr.to_excel(writer, sheet_name='cv_grid_results', index=False)
                    except Exception:
                        pass
                    # Optionally write final selected features
                    try:
                        if isinstance(final_features, (list, tuple)) and len(final_features) > 0:
                            ff_df = pd.DataFrame({'feature': list(final_features)})
                            ff_df.to_excel(writer, sheet_name='final_features', index=False)
                    except Exception:
                        pass
                    # Optionally write run configuration
                    try:
                        if isinstance(run_config, dict) and run_config:
                            rc = pd.DataFrame([
                                { 'key': k, 'value': ', '.join(v) if isinstance(v, (list, tuple)) else v }
                                for k, v in run_config.items()
                            ])
                            rc.to_excel(writer, sheet_name='run_config', index=False)
                    except Exception:
                        pass
                    # Optionally write final selected features (used alongside embeddings)
                    try:
                        if isinstance(final_features, (list, tuple)) and len(final_features) > 0:
                            ff_df = pd.DataFrame({'feature': list(final_features)})
                            ff_df.to_excel(writer, sheet_name='final_features', index=False)
                    except Exception:
                        pass
        except Exception:
            base, _ = os.path.splitext(file_path)
            try:
                Results.to_csv(base + '_results.csv', index=False)
            except Exception:
                pass
            try:
                val_df = pd.DataFrame(validation_Results).T.reset_index().rename(columns={'index': 'Model Name'})
                val_df.to_csv(base + '_validation.csv', index=False)
            except Exception:
                pass

    def _best_model_from_validation(self, validation_Results: Dict[str, Dict[str, Any]]) -> Tuple[str, float]:
        if not validation_Results:
            return "", float('nan')
        best_name = ""
        best_val = float('-inf')
        for name, metrics in validation_Results.items():
            try:
                val = float(metrics.get('adj_r2', metrics.get('r2', float('-inf'))))
            except Exception:
                val = float('-inf')
            if val > best_val:
                best_val = val
                best_name = name
        return best_name, best_val

    def _infer_embedding_name(self, model_name: str) -> Optional[str]:
        name = model_name.lower()
        if 'tfidf' in name:
            return 'tfidf1gram'
        if 'w2v' in name or 'word2vec' in name:
            return 'w2v'
        if 'bert' in name:
            return 'bert'
        return None

    def generate_regression_excel_output(self, Results: pd.DataFrame, validation_Results: Dict[str, Dict[str, Any]], file_path: str, *, total_combinations: Optional[int] = None, grid_rows: Optional[pd.DataFrame] = None, final_features: Optional[list] = None, run_config: Optional[Dict[str, Any]] = None, selected_model_override: Optional[str] = None):
        self._write_results(Results, validation_Results, file_path, grid_rows=grid_rows, final_features=final_features, run_config=run_config)
        best_model_name_override = selected_model_override
        best_model_name, _ = (best_model_name_override, 0.0) if best_model_name_override else self._best_model_from_validation(validation_Results)
        if not best_model_name:
            try:
                df = pd.DataFrame(Results)
                use_col = 'validation_adjusted_r2_score' if 'validation_adjusted_r2_score' in df.columns else (
                    'validation_r2_score' if 'validation_r2_score' in df.columns else None
                )
                if use_col:
                    idx = df[use_col].astype(float).idxmax()
                    best_model_name = str(df.loc[idx, 'Model Name'])
            except Exception:
                best_model_name = None  # type: ignore[assignment]
        model_pkl_file_name = f"{best_model_name.replace(' ', '_')}_model.pkl" if best_model_name else None
        embedding_name = self._infer_embedding_name(best_model_name) if best_model_name else None

        # Build concise summary row with selected model + metrics
        def _summary_df():
            sel = str(best_model_name) if best_model_name else None
            mse = mae = r2 = adj_r2 = None
            best_params = ''
            if sel and isinstance(validation_Results, dict) and sel in validation_Results:
                m = validation_Results.get(sel, {}) or {}
                mse = m.get('mse')
                mae = m.get('mae')
                r2 = m.get('r2')
                adj_r2 = m.get('adj_r2')
            elif sel and isinstance(Results, pd.DataFrame) and not Results.empty:
                try:
                    row = Results[Results['Model Name'] == sel].iloc[0]
                    best_params = row.get('best_params', '') if 'best_params' in Results.columns else ''
                    mse = row.get('validation_mean_squared_error')
                    mae = row.get('validation_mean_absolute_error')
                    r2 = row.get('validation_r2_score')
                    adj_r2 = row.get('validation_adjusted_r2_score', r2)
                except Exception:
                    pass
            if not best_params and isinstance(Results, pd.DataFrame) and 'best_params' in Results.columns and sel:
                try:
                    match = Results[Results['Model Name'].astype(str).str.startswith(sel)].iloc[0]
                    best_params = match.get('best_params', '')
                except Exception:
                    pass
            return pd.DataFrame([{
                'selected_model': sel or '',
                'selected_model_params': best_params,
                'adjusted_r2': adj_r2 if adj_r2 is not None else '',
                'r2': r2 if r2 is not None else '',
                'mse': mse if mse is not None else '',
                'mae': mae if mae is not None else '',
                'num_models': (int(total_combinations) if total_combinations is not None else ((len(Results) if isinstance(Results, pd.DataFrame) else None))) or ''
            }])

        summary = _summary_df()
        return summary, model_pkl_file_name, embedding_name

    def generate_test_prediction_excel(self, Data: pd.DataFrame, embedding_name: str, model_pkl_file_name: str,
                                       embedding_pkl_file_name: Optional[str], embeddings_module,
                                       numerical_cols_scaled: dict, textual_cols: list, features: list, target: str,
                                       output_folder: str, sample_prediction_file_name: str):
        # Load the trained model and produce predictions using the chosen embedding
        model_path = os.path.join(output_folder or '.', model_pkl_file_name)
        model = joblib.load(model_path)

        emb = (embedding_name or '').lower()
        if emb.startswith('tfidf'):
            X_feat = embeddings_module.embeddings_tfidf_test_data(Data['Cleaned_data'], sparse_return=False)
        elif emb.startswith('w2v'):
            X_feat = embeddings_module.embeddings_word2vec_test_data(Data['Cleaned_data'])
        elif emb.startswith('bert'):
            X_feat = embeddings_module.embeddings_bert(Data['Cleaned_data'])
        else:
            X_feat = embeddings_module.embeddings_tfidf_test_data(Data['Cleaned_data'], sparse_return=False)

        # Ensure numpy array and 2D shape
        import numpy as _np
        X_feat = _np.asarray(X_feat)
        if X_feat.ndim == 1:
            X_feat = X_feat.reshape(-1, 1)

        # Append the same training features (numeric + encoded categoricals + engineered) if provided
        try:
            if isinstance(features, (list, tuple)) and len(features) > 0:
                X_num = Data[list(features)].values
                X_feat = _np.concatenate((X_feat, X_num), axis=1)
            elif isinstance(numerical_cols_scaled, dict) and len(numerical_cols_scaled) > 0:
                # fallback to original numeric set
                X_num = Data[list(numerical_cols_scaled.keys())].values
                X_feat = _np.concatenate((X_feat, X_num), axis=1)
        except Exception:
            # best-effort: continue with embeddings-only if alignment fails
            pass

        preds = model.predict(X_feat)
        out_df = Data.copy()
        out_df['predicted_value'] = preds
        if target in Data.columns:
            out_df['actual_value'] = Data[target]
        out_file = os.path.join(output_folder or '.', sample_prediction_file_name)
        try:
            if out_file.lower().endswith('.csv'):
                out_df.to_csv(out_file, index=False)
            else:
                with pd.ExcelWriter(out_file, engine='openpyxl') as writer:
                    out_df.to_excel(writer, index=False)
        except Exception:
            out_df.to_csv(out_file.replace('.xlsx', '.csv'), index=False)
        return out_file

class DLClassificationOutput(BaseExcelMixin):
    def load_model(self, output_folder: str, model_file_name: str):
        return joblib.load(os.path.join(output_folder, model_file_name))

    def pad_sequences(self, batch):
        return batch

    def generate_excel_output_summary(self, Results: pd.DataFrame, validation_Results: pd.DataFrame, file_path: str):
        # Minimal writer for DL flows: just write results and validation sheets
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            try:
                Results.to_excel(writer, sheet_name='results', index=False)
            except Exception:
                pd.DataFrame(Results).to_excel(writer, sheet_name='results', index=False)
            try:
                validation_Results.to_excel(writer, sheet_name='validation', index=False)
            except Exception:
                pd.DataFrame(validation_Results).to_excel(writer, sheet_name='validation', index=False)

    def get_summary(self, Results: pd.DataFrame) -> pd.DataFrame:
        return Results.describe()

    def predict_and_save_to_excel(self, validation_loader, best_model_file_name: str, output_folder: str,
                                  fields: list, valid_df: pd.DataFrame, label_pkl_file_name: str, label: str):
        model = self.load_model(output_folder, best_model_file_name)
        # placeholder: produce CSV with no predictions
        out_file = os.path.join(output_folder or '.', 'dl_predictions.csv')
        valid_df.to_csv(out_file, index=False)
        return out_file