import os
import time
import logging
import joblib
import pandas as pd

from log_utils import setup_logging
from utils import copy_deployment_collaterals
from preprocess import TextPreprocess
from embeddings import TextEmbeddings
from class_label import LabelTransform
from modelling import Classification
from output import ClassificationOutput
from sklearn.model_selection import train_test_split

logging.getLogger(__name__)

def classification_process(file, fields, label, output_folder):
    start_time = time.time()
    logging.info("Starting classification process...")
    summary = None
    customer_files = []
    num_models = 0

    processing_logger = {
        "number_of_classes_atleast2": True,
        "process_complete": False,
        "class_with_lessthan_30sample_before_preprocessing": False,
        "classes_dropped_before_preprocessing": {},
        "preprocessing_complete": False,
        "class_with_lessthan_30sample_post_preprocessing": False,
        "classes_dropped_post_preprocessing": {},
        "dupliccate_input_rows_dropped": 0,
    }

    pd.set_option('display.max_columns', None)
    _, file_extension = os.path.splitext(file)
    if file_extension.lower() == ".csv":
        df = pd.read_csv(file)
    elif file_extension.lower() in [".xls", ".xlsx"]:
        df = pd.read_excel(file)
    else:
        raise ValueError("Unsupported file type")

    # Fix: use df variable (was Date / Data typos)
    num_rows = df.shape[0]

    # concatenate specified text fields
    df["concatenated_fields"] = df[fields].astype(str).agg(' '.join, axis=1)
    df = df.drop_duplicates(subset=['concatenated_fields', label])
    processing_logger["dupliccate_input_rows_dropped"] = num_rows - df.shape[0]

    if df[label].nunique() < 2:
        processing_logger["number_of_classes_atleast2"] = False
        raise ValueError("Need at least 2 classes")

    value_counts = df[label].value_counts()
    filtered_value_counts = value_counts[value_counts < 30]
    if filtered_value_counts.any():
        processing_logger["class_with_lessthan_30sample_before_preprocessing"] = True
        processing_logger["classes_dropped_before_preprocessing"] = filtered_value_counts.to_dict()

    if df[label].nunique() < 2:
        processing_logger["number_of_classes_atleast2"] = False
        raise ValueError("Need at least 2 classes after filtering")

    num_rows = df.shape[0]
    text_preproc = TextPreprocess()
    selected_columns = [label]
    df = text_preproc.preprocess_text(df, 'concatenated_fields', selected_columns)
    processing_logger["preprocessing_complete"] = True

    # drop duplicates after preprocessing
    df = df.drop_duplicates(subset=['Cleaned_data', label])
    processing_logger["num_rows_dropped_in_preprocessing"] = num_rows - df.shape[0]

    value_counts = df[label].value_counts()
    filtered_value_counts = value_counts[value_counts < 30]
    if filtered_value_counts.any():
        processing_logger["class_with_lessthan_30sample_post_preprocessing"] = True
        processing_logger["classes_dropped_post_preprocessing"] = filtered_value_counts.to_dict()

    if df[label].nunique() < 2:
        processing_logger["number_of_classes_atleast2"] = False
        raise ValueError("Need at least 2 classes after preprocessing")

    # label encoding
    label_pkl_file_name = "label_encoder.pkl"
    label_transform = LabelTransform()
    label_transform.set_output_folder(output_folder)
    df = label_transform.encode_labels(df, label, label_pkl_file_name)
    class_names = list(label_transform.label_encoder.classes_)

    # Prepare training/testing data
    X = df[[*fields, "Cleaned_data"]]
    y = df["label_col_encoded"]
    stratify = y if y.nunique() > 1 and all(y.value_counts() >= 2) else None
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=stratify)

    embeddings = TextEmbeddings()
    embeddings.set_output_folder(output_folder)

    # --- TF-IDF sparse + SVD reduction (always on) ---
    tfidf_sparse_train = embeddings.embeddings_tfidf(
        X_train["Cleaned_data"], ngram=1, sparse_return=True, max_features=200000, min_df=2, max_df=0.95
    )
    tfidf_sparse_test = embeddings.embeddings_tfidf_test_data(
        X_test["Cleaned_data"], sparse_return=True
    )

    n_comps = 300
    try:
        X_train_tfidf_svd = embeddings.svd_fit(tfidf_sparse_train, n_components=n_comps)
        X_test_tfidf_svd = embeddings.svd_transform(tfidf_sparse_test)
    except Exception as e:
        logging.warning("SVD failed; falling back to dense TF-IDF: %s", e)
        import numpy as _np
        X_train_tfidf_svd = _np.asarray(tfidf_sparse_train.todense())
        X_test_tfidf_svd = _np.asarray(tfidf_sparse_test.todense())

    # --- Optional: Word2Vec (if gensim available) ---
    use_w2v = False
    try:
        import gensim  # noqa: F401
        use_w2v = True
    except Exception:
        logging.info("gensim not installed; skipping Word2Vec embeddings")

    X_train_w2v = X_test_w2v = None
    if use_w2v:
        try:
            X_train_w2v = embeddings.embeddings_word2vec(X_train["Cleaned_data"])
            X_test_w2v = embeddings.embeddings_word2vec_test_data(X_test["Cleaned_data"])
        except Exception as e:
            logging.warning("Word2Vec failed; skipping W2V models: %s", e)
            use_w2v = False

    # --- Optional: BERT (if transformers+torch available) ---
    use_bert = False
    try:
        import transformers, torch  # noqa: F401
        # simple guard to avoid very large inference by default
        if len(X_train) <= 10000:
            use_bert = True
        else:
            logging.info("Dataset large; skipping BERT for speed")
    except Exception:
        logging.info("transformers/torch not installed; skipping BERT embeddings")

    X_train_bert = X_test_bert = None
    if use_bert:
        try:
            X_train_bert = embeddings.embeddings_bert(X_train["Cleaned_data"])
            X_test_bert = embeddings.embeddings_bert(X_test["Cleaned_data"])
        except Exception as e:
            logging.warning("BERT embedding failed; skipping BERT models: %s", e)
            use_bert = False

    # Results DataFrames
    Results = pd.DataFrame(columns=[
        "Model Name", "params",
        "mean_test_accuracy", "mean_test_f1_weighted",
        "mean_test_precision_weighted", "mean_test_recall_weighted"
    ])
    validation_Results = pd.DataFrame(columns=[
        "Model Name", "params",
        "validation_accuracy", "validation_f1_weighted",
        "validation_precision_weighted", "validation_recall_weighted",
        "validation_average_score"
    ])

    modelling = Classification()
    modelling.set_output_folder(output_folder)

    eval_models = []

    # --- Train on TFIDF+SVD ---
    try:
        Results, validation_Results = modelling.logistic_regression(
            X_train_tfidf_svd, y_train.values, X_test_tfidf_svd, y_test.values,
            Results, validation_Results, model_name="Logistic_TFIDF_SVD"
        ); eval_models.append(("Logistic_TFIDF_SVD", "tfidf_svd"))

        Results, validation_Results = modelling.support_vector_machine(
            X_train_tfidf_svd, y_train.values, X_test_tfidf_svd, y_test.values,
            Results, validation_Results, model_name="SVM_TFIDF_SVD"
        ); eval_models.append(("SVM_TFIDF_SVD", "tfidf_svd"))

        Results, validation_Results = modelling.random_forest_classifier(
            X_train_tfidf_svd, y_train.values, X_test_tfidf_svd, y_test.values,
            Results, validation_Results, model_name="RF_TFIDF_SVD"
        ); eval_models.append(("RF_TFIDF_SVD", "tfidf_svd"))

        Results, validation_Results = modelling.xgboost_classifier(
            X_train_tfidf_svd, y_train.values, X_test_tfidf_svd, y_test.values,
            Results, validation_Results, model_name="XGB_TFIDF_SVD"
        ); eval_models.append(("XGB_TFIDF_SVD", "tfidf_svd"))
    except Exception as e:
        logging.exception("TFIDF_SVD model training failed: %s", e)

    # --- Train on W2V (optional) ---
    if use_w2v and X_train_w2v is not None:
        try:
            Results, validation_Results = modelling.logistic_regression(
                X_train_w2v, y_train.values, X_test_w2v, y_test.values,
                Results, validation_Results, model_name="Logistic_W2V"
            ); eval_models.append(("Logistic_W2V", "w2v"))

            Results, validation_Results = modelling.support_vector_machine(
                X_train_w2v, y_train.values, X_test_w2v, y_test.values,
                Results, validation_Results, model_name="SVM_W2V"
            ); eval_models.append(("SVM_W2V", "w2v"))
        except Exception as e:
            logging.exception("W2V model training failed: %s", e)

    # --- Train on BERT (optional) ---
    if use_bert and X_train_bert is not None:
        try:
            Results, validation_Results = modelling.logistic_regression(
                X_train_bert, y_train.values, X_test_bert, y_test.values,
                Results, validation_Results, model_name="Logistic_BERT"
            ); eval_models.append(("Logistic_BERT", "bert"))

            Results, validation_Results = modelling.support_vector_machine(
                X_train_bert, y_train.values, X_test_bert, y_test.values,
                Results, validation_Results, model_name="SVM_BERT"
            ); eval_models.append(("SVM_BERT", "bert"))
        except Exception as e:
            logging.exception("BERT model training failed: %s", e)

    # Build eval details for confusion matrices and per-class metrics
    eval_details = []
    for model_name, emb_tag in eval_models:
        model_path = os.path.join(output_folder, f"{model_name.replace(' ','_')}_model.pkl")
        if not os.path.exists(model_path):
            continue
        try:
            model = joblib.load(model_path)
            if emb_tag == "tfidf_svd":
                y_pred = model.predict(X_test_tfidf_svd)
            elif emb_tag == "w2v":
                y_pred = model.predict(X_test_w2v)
            else:  # bert
                y_pred = model.predict(X_test_bert)
            eval_details.append({
                "model_name": model_name,
                "y_true": y_test.values,
                "y_pred": y_pred
            })
        except Exception as e:
            logging.warning("Prediction failed for %s: %s", model_name, e)

    # Choose best model by highest CV mean_test_f1_weighted
    best_summary = pd.DataFrame()
    best_name = None
    best_emb_tag = "tfidf_svd"
    try:
        if not Results.empty and "mean_test_f1_weighted" in Results.columns:
            f1_series = pd.to_numeric(Results["mean_test_f1_weighted"], errors="coerce")
            if f1_series.notna().any():
                best_idx = f1_series.idxmax()
                best_row = Results.loc[best_idx]
                best_name = str(best_row.get("Model Name"))
                best_params = str(best_row.get("params"))
                try:
                    best_cv_f1 = float(pd.to_numeric(pd.Series([best_row.get("mean_test_f1_weighted")])).iloc[0])
                except Exception:
                    best_cv_f1 = None
                # find matching validation row for the same model
                val_f1 = None
                try:
                    val_match = validation_Results[validation_Results["Model Name"] == best_name]
                    if not val_match.empty:
                        val_f1 = float(pd.to_numeric(val_match["validation_f1_weighted"], errors="coerce").iloc[0])
                except Exception:
                    pass
                # infer embedding tag from best name
                if best_name and "W2V" in best_name:
                    best_emb_tag = "w2v"
                elif best_name and "BERT" in best_name:
                    best_emb_tag = "bert"
                else:
                    best_emb_tag = "tfidf_svd"

                # Build compact UI summary and include user selections
                best_summary = pd.DataFrame([{
                    "Best Model Name": best_name,
                    "Best Params": best_params,
                    "CV mean_test_f1_weighted": best_cv_f1,
                    "Validation f1_weighted": val_f1,
                    "Total param combinations evaluated": getattr(modelling, "total_param_combinations", None),
                    "Models trained": len(eval_models),
                    "Input Columns": ", ".join(fields),
                    "Target Column": label
                }])
                processing_logger["best_model_name"] = best_name
                processing_logger["best_model_cv_f1_weighted"] = best_cv_f1
    except Exception as e:
        logging.warning("Failed to compute best model summary: %s", e)

    # Save outputs (excel) with confusion matrices and per-class metrics
    outputter = ClassificationOutput()
    os.makedirs(output_folder, exist_ok=True)
    out_file = os.path.join(output_folder, 'classification_results.xlsx')
    outputter.generate_classification_excel_output(
        Results, validation_Results, out_file,
        eval_details=eval_details,
        class_names=class_names
    )
    customer_files = [os.path.basename(out_file)]

    # Also add key artifacts to Downloads: best model .pkl and label encoder .pkl
    if bool(best_name):
        best_model_file = f"{best_name.replace(' ','_')}_model.pkl"
        best_model_path = os.path.join(output_folder, best_model_file)
        if os.path.exists(best_model_path):
            customer_files.append(os.path.basename(best_model_path))
    label_pkl_path = os.path.join(output_folder, label_pkl_file_name)
    if os.path.exists(label_pkl_path):
        customer_files.append(os.path.basename(label_pkl_path))

    # Optionally include embedding artifacts for TFIDF+SVD or W2V (if present)
    emb_candidates = []
    if best_name and "TFIDF" in str(best_name):
        emb_candidates += ["tfidf_vectorizer_ngram1.pkl"]
        # include any saved svd file
        for f in os.listdir(output_folder):
            if f.startswith("tfidf_svd_") and f.endswith(".pkl"):
                emb_candidates.append(f)
    if best_name and "W2V" in str(best_name):
        emb_candidates += ["word2vec_model.pkl"]
    for f in emb_candidates:
        p = os.path.join(output_folder, f)
        if os.path.exists(p):
            customer_files.append(os.path.basename(p))

    # Sample prediction file using the best model’s embedding
    try:
        sample_prediction_file_name = 'sample_predictions.csv'
        if bool(best_name):
            best_model_file = f"{best_name.replace(' ','_')}_model.pkl"
            outputter.generate_test_prediction_excel(
                X_test.reset_index(drop=True),
                best_emb_tag,  # 'tfidf_svd' | 'w2v' | 'bert'
                best_model_file,
                "",
                label_pkl_file_name,
                embeddings,
                [*fields, 'Cleaned_data'],
                label,
                output_folder,
                sample_prediction_file_name
            )
            customer_files.append(sample_prediction_file_name)
    except Exception as e:
        logging.warning("Could not produce sample predictions: %s", e)

    # Create deployment collaterals (script + swayamml.conf) for NLP Classification
    try:
        copy_deployment_collaterals(source_dir='.', target_dir=output_folder, model="NLP Classification")
        # Build swayamml.conf with embedding/model details
        import configparser as _cp
        cfg = _cp.ConfigParser()
        cfg['DEFAULT'] = {}
        if best_name:
            cfg['DEFAULT']['model_pkl_file_name'] = f"{best_name.replace(' ','_')}_model.pkl"
        # Infer embedding_name and corresponding artifacts
        emb_name = 'tfidf_svd' if (best_name and 'TFIDF' in str(best_name)) else ('w2v' if (best_name and 'W2V' in str(best_name)) else '')
        if emb_name:
            cfg['DEFAULT']['embedding_name'] = emb_name
        # Determine embedding artifacts
        if emb_name == 'tfidf_svd':
            cfg['DEFAULT']['embedding_pkl_file_name'] = 'tfidf_vectorizer_ngram1.pkl'
            # try to find persisted svd file
            svd_file = ''
            for f in os.listdir(output_folder):
                if f.startswith('tfidf_svd_') and f.endswith('.pkl'):
                    svd_file = f
                    break
            if svd_file:
                cfg['DEFAULT']['svd_pkl_file_name'] = svd_file
        elif emb_name == 'w2v':
            cfg['DEFAULT']['embedding_pkl_file_name'] = 'word2vec_model.pkl'
        # Label encoder
        cfg['DEFAULT']['label_pkl_file_name'] = label_pkl_file_name
        with open(os.path.join(output_folder, 'swayamml.conf'), 'w') as fh:
            cfg.write(fh)
        # Ensure script + config listed as customer files
        for f in ['deployment_script_classification.py', 'requirements.txt', 'swayamml.conf']:
            pth = os.path.join(output_folder, f)
            if os.path.exists(pth):
                customer_files.append(f)
    except Exception as _e:
        logging.warning(f"Unable to prepare NLP Classification deployment collaterals: {_e}")

    processing_logger["process_complete"] = True
    processing_logger["time_taken_sec"] = time.time() - start_time
    processing_logger["total_param_combinations"] = getattr(modelling, "total_param_combinations", None)
    summary_to_show = best_summary if not best_summary.empty else Results
    # Keep returning processing_logger for logs, but UI will ignore it
    return summary_to_show, customer_files, len(eval_models), processing_logger

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)


