class GameLogic{ constructor(solver){ this.solver=solver; this.originalPuzzle=null; this.currentGrid=null; this.solution=null; this.difficulty='medium'; this.startTime=null; this.elapsedTime=0; this.isPaused=false; this.timerInterval=null; this.errors=0; this.maxErrors=3; this.moveHistory=[]; this.onGameComplete=null; this.onGameOver=null; this.onGameUpdate=null; }
 startNewGame(p){ this.originalPuzzle=this.solver.copyGrid(p.puzzle); this.currentGrid=this.solver.copyGrid(p.puzzle); this.solution=p.solution; this.difficulty=p.difficulty; this.errors=0; this.moveHistory=[]; this.startTimer(); }
 makeMove(r,c,val){ if(!this.currentGrid||this.isPuzzleComplete()) return false; if(this.originalPuzzle[r][c]!==0) return false; const prev=this.currentGrid[r][c]; this.currentGrid[r][c]=val; this.moveHistory.push({row:r,col:c,previousValue:prev,newValue:val,timestamp:Date.now()}); const isCorrect= val===0 || val===this.solution[r][c]; if(!isCorrect && val!==0){ this.errors++; if(this.errors>=this.maxErrors){ this.pauseTimer(); if(this.onGameOver) this.onGameOver(); return false; } }
 if(this.isPuzzleComplete()){ this.pauseTimer(); if(this.onGameComplete) this.onGameComplete(); }
 if(this.onGameUpdate) this.onGameUpdate();
 return { isCorrect, gameComplete: this.isPuzzleComplete() };
 }
 isPuzzleComplete(){ if(!this.currentGrid) return false; for(let r=0;r<9;r++){ for(let c=0;c<9;c++){ if(this.currentGrid[r][c]===0) return false; } } return this.solver.isValidSolution(this.currentGrid); }
 isValidMove(r,c,v){ if(v===0) return true; return this.solver.isValidMove(this.currentGrid,r,c,v); }
 getHint(){ if(!this.currentGrid) return null; return this.solver.getHint(this.currentGrid); }
 solvePuzzle(sol){ if(!this.currentGrid) return; this.currentGrid=this.solver.copyGrid(sol); this.pauseTimer(); }
 resetPuzzle(){ if(!this.originalPuzzle) return; this.currentGrid=this.solver.copyGrid(this.originalPuzzle); this.errors=0; this.moveHistory=[]; this.startTimer(); if(this.onGameUpdate) this.onGameUpdate(); }
 undoMove(){
	if(!this.moveHistory || this.moveHistory.length===0) return null;
	const last=this.moveHistory.pop();
	const {row,col,previousValue,newValue}=last;
	// If the last move was an incorrect non-zero entry, revert the error count (not below 0)
	const wasIncorrect = newValue!==0 && newValue!==this.solution[row][col];
	if(wasIncorrect) this.errors=Math.max(0,this.errors-1);
	// Revert the value
	if(this.originalPuzzle[row][col]===0){
		this.currentGrid[row][col]=previousValue;
	}
	// If timer was paused due to completion, resume so player can continue
	if(this.isPaused && !this.isPuzzleComplete()){
		this.resumeTimer();
	}
	if(this.onGameUpdate) this.onGameUpdate();
	return {row,col,value:previousValue};
 }
 startTimer(){ if(this.timerInterval) clearInterval(this.timerInterval); this.startTime=Date.now(); this.elapsedTime=0; this.isPaused=false; this.timerInterval=setInterval(()=>{ if(!this.isPaused){ this.elapsedTime=Date.now()-this.startTime; if(this.onGameUpdate) this.onGameUpdate(); } },1000); }
 pauseTimer(){ this.isPaused=true; }
 resumeTimer(){ if(this.isPaused&&this.startTime){ this.isPaused=false; this.startTime=Date.now()-this.elapsedTime; } }
 getTimeString(){ const s=Math.floor(this.elapsedTime/1000); const m=Math.floor(s/60); const sec=s%60; return `${m.toString().padStart(2,'0')}:${sec.toString().padStart(2,'0')}`; }
 getElapsedTime(){ return this.elapsedTime; }
 getErrorCount(){ return this.errors; }
 getMaxErrors(){ return this.maxErrors; }
 getDifficulty(){ return this.difficulty; }
 getCurrentGrid(){ return this.currentGrid; }
 hasProgress(){ if(!this.originalPuzzle||!this.currentGrid) return false; for(let r=0;r<9;r++){ for(let c=0;c<9;c++){ if(this.originalPuzzle[r][c]!==this.currentGrid[r][c]) return true; } } return false; }
 getGameState(){ return { currentGrid:this.currentGrid, originalPuzzle:this.originalPuzzle, solution:this.solution, difficulty:this.difficulty, elapsedTime:this.elapsedTime, errors:this.errors, moveHistory:this.moveHistory, startTime:this.startTime }; }
 loadGameState(s,p){ this.currentGrid=s.currentGrid; this.originalPuzzle=s.originalPuzzle; this.solution=s.solution||p.solution; this.difficulty=s.difficulty; this.elapsedTime=s.elapsedTime||0; this.errors=s.errors||0; this.moveHistory=s.moveHistory||[]; if(!this.isPuzzleComplete()){ this.startTime=Date.now()-this.elapsedTime; this.startTimer(); } }
}
if(typeof window!=='undefined'){ window.GameLogic=GameLogic; }
