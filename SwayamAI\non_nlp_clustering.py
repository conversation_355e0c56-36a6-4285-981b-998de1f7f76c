import os
import pandas as pd
import time
import logging
import traceback

from log_util import setup_logger, remove_logger
from preprocess import FeaturePreprocess
from modelling import ClusterModel
from output import ClusterOutput
import numpy as np
from sklearn.preprocessing import normalize

def non_nlp_clustering_process(file, fields, output_folder):
    start_time = time.time()
    feature_importance = None

    processing_logger = {
        "process_complete": False,
        "text_columns_dropped": [],
        "features_used_in_modelling": [],
        "preprocessing_complete": False
    }

    pd.set_option('display.max_columns', None)

    _, file_extension = os.path.splitext(file)
    if file_extension == '.csv':
        df = pd.read_csv(file)
    else:
        df = pd.read_excel(file)

    # Select required fields and drop rows missing those fields, but preserve all original columns
    data_fields = df[list(fields)].copy()
    mask = ~data_fields[list(fields)].isna().any(axis=1)
    # Filter original df to the same rows to keep all original columns aligned
    original_rows = df.loc[mask].reset_index(drop=True)
    data = data_fields.loc[mask].reset_index(drop=True)

    preprocess = FeaturePreprocess()
    # preprocess_features returns (data, textual_cols, datetime_cols, string_to_num_cols, categorical_cols_encoded, numerical_cols_scaled)
    data_processed, textual_cols, datetime_cols, string_to_num_cols, categorical_cols_encoded, numerical_cols_scaled = preprocess.preprocess_features(data)

    # Track original processed columns to later identify newly added cluster columns
    original_processed_cols = list(data_processed.columns)

    # Save encodings/imputer/scaler artifacts
    encoding_pkl_file_name = 'encoding.pkl'
    imputer_pkl_file_name = 'imputer.pkl'
    scaler_pkl_file_name = 'scaler.pkl'
    encoding_pkl_file = os.path.join(output_folder, encoding_pkl_file_name)
    imputer_pkl_file = os.path.join(output_folder, imputer_pkl_file_name)
    scaler_pkl_file = os.path.join(output_folder, scaler_pkl_file_name)
    preprocess.save_encodings(encoding_pkl_file, imputer_pkl_file, scaler_pkl_file)

    # Build numerical feature matrix: use all numeric columns in processed data
    # (includes original numeric and frequency-encoded categoricals)
    data_numeric = data_processed.copy()
    numeric_cols = data_numeric.select_dtypes(include='number').columns.tolist()
    if not numeric_cols:
        # attempt to coerce object columns to numeric (best-effort)
        dn = data_numeric.copy()
        for c in dn.columns:
            if dn[c].dtype == object:
                try:
                    dn[c] = pd.to_numeric(dn[c], errors='coerce')
                except Exception:
                    pass
        data_numeric = dn
        numeric_cols = data_numeric.select_dtypes(include='number').columns.tolist()

    if not numeric_cols:
        raise ValueError("No numeric features available for non-NLP clustering after preprocessing and encoding.")

    X = data_numeric[numeric_cols].values

    Results = pd.DataFrame(columns=["Model Name", "Silhouette Score", "Calinski Harabasz Score", "Davies Bouldin Score", "skew_flag", "Number of Clusters"])

    modelling = ClusterModel()
    try:
        modelling.set_output_folder(output_folder)
    except Exception:
        pass

    min_clusters = 2
    max_clusters = 12

    # Call clustering routines with numeric matrix
    # L2 normalize numeric features for cosine-friendly behavior
    Xn = normalize(np.asarray(X), norm='l2')
    data_clustered, Results = modelling.KMeans(df=data_processed, results=Results, DM="", reduced_embeddings=Xn, min_k=min_clusters, max_k=max_clusters, name_suffix="[NUM]")
    data_clustered, Results = modelling.dbscan(df=data_clustered, results=Results, DM="", reduced_embeddings=Xn, min_samples=5, name_suffix="[NUM]")
    data_clustered, Results = modelling.agglomerative_clustering(df=data_clustered, results=Results, DM="", reduced_embeddings=Xn, name_suffix="[NUM]")
    data_clustered, Results = modelling.affinity_propagation(df=data_clustered, results=Results, DM="", reduced_embeddings=Xn, name_suffix="[NUM]")

    # Results already aligned to Cluster_Modelling's expected schema

    # Drop skewed models if majority are skewed
    if "skew_flag" in Results.columns and len(Results) > 0:
        prop_skew = Results['skew_flag'].value_counts(normalize=True).get(1, 0.0)
        if prop_skew > 0.5:
            Results = Results[Results['skew_flag'] != 1]

    if 'skew_flag' in Results.columns:
        Results = Results.drop(columns=['skew_flag'])

    # Compute a top-3 summary using the same scoring logic as NLP clustering
    def robust_minmax(series: pd.Series, lower_q=0.05, upper_q=0.95):
        s = series.copy()
        lo = s.quantile(lower_q)
        hi = s.quantile(upper_q)
        if pd.isna(lo) or pd.isna(hi) or hi - lo <= 1e-12:
            return pd.Series([0.5] * len(s), index=s.index)
        s = s.clip(lo, hi)
        return (s - lo) / (hi - lo)

    temp_results = Results.dropna(subset=['Silhouette Score', 'Calinski Harabasz Score', 'Davies Bouldin Score']).copy()
    balances = []
    to_drop_idx = []
    for idx, row in temp_results.iterrows():
        model_name = row['Model Name']
        max_prop = 1.0
        try:
            if model_name in data_clustered.columns:
                vc = data_clustered[model_name].value_counts(normalize=True)
                max_prop = float(vc.max()) if not vc.empty else 1.0
        except Exception:
            max_prop = 1.0
        balance = max(0.0, 1.0 - max_prop)
        balances.append(balance)
        n_clusters = int(row.get('Number of Clusters', 0) or 0)
        if max_prop >= 0.9 or n_clusters < 2:
            to_drop_idx.append(idx)
    temp_results['Cluster Balance'] = balances
    if len(to_drop_idx) < len(temp_results):
        temp_results = temp_results.drop(index=to_drop_idx)

    display_summary = pd.DataFrame()
    if not temp_results.empty:
        dav = temp_results['Davies Bouldin Score']
        dav_inv = pd.Series(0.5, index=temp_results.index)
        try:
            if dav.max() > 0:
                dav_inv = 1 - (dav / dav.max())
        except Exception:
            pass
        temp_results['DB Inverted'] = dav_inv
        sil_n = robust_minmax(temp_results['Silhouette Score'])
        cal_n = robust_minmax(temp_results['Calinski Harabasz Score'])
        dbi_n = robust_minmax(temp_results['DB Inverted'])
        bal_n = robust_minmax(temp_results['Cluster Balance'])
        w_sil, w_cal, w_dbi, w_bal = 0.45, 0.25, 0.15, 0.15
        temp_results['Combined Score'] = (w_sil * sil_n + w_cal * cal_n + w_dbi * dbi_n + w_bal * bal_n)
        display_summary = temp_results.sort_values('Combined Score', ascending=False).head(3)

    # Merge cluster columns onto the original rows so Excel shows all original columns
    # Identify cluster columns as the new columns added by clustering (vs the original processed columns)
    model_cols = [c for c in data_clustered.columns if c not in original_processed_cols]
    output_df = pd.concat(
        [
            original_rows.reset_index(drop=True),
            data_clustered[model_cols].reset_index(drop=True) if model_cols else pd.DataFrame(index=original_rows.index),
        ],
        axis=1,
    )

    output = ClusterOutput()

    output_preprocess_file = "clustering_Results.xlsx"
    output_file = "modelling_output.xlsx"
    output_preprocess_file_path = os.path.join(output_folder, output_preprocess_file)
    output_file_path = os.path.join(output_folder, output_file)

    # Build features list as numeric column names actually used
    features = list(numeric_cols)

    # Generate clustering output: 'clusters' has all models; 'Top 3 clusters' has only top-3 models
    output.generate_clustering_output(output_df, Results, output_preprocess_file_path, list(fields), top_models=display_summary if not display_summary.empty else Results.head(3))
    # UI summary: show only top-3
    summary = display_summary if not display_summary.empty else Results.head(3)

    # Get best model name and compute feature importance (use Results if summary is not a DF)
    best_model_name = None
    try:
        if hasattr(display_summary, 'empty') and not display_summary.empty and 'Model Name' in display_summary.columns:
            best_model_name = display_summary.iloc[0].get('Model Name')
    except Exception:
        pass

    model_pkl_filename, feature_importance = None, None
    if best_model_name:
        res = output.get_model_and_feature_importance(best_model_name, X, features, output_folder)
        if isinstance(res, (list, tuple)) and len(res) == 2:
            model_pkl_filename, feature_importance = res  # type: ignore[misc]

    # Return the actual files written to output_folder so downloads work
    customer_files = [os.path.basename(output_preprocess_file_path)]
    # if there is an extra intended output file, include it if it exists
    if os.path.exists(os.path.join(output_folder, output_file)):
        customer_files.append(output_file)

    end_time = time.time()
    processing_logger["process_complete"] = True
    processing_logger["features_used_in_modelling"] = features
    processing_logger["preprocessing_complete"] = True

    # Normalized return order: (summary, customer_files, num_models, processing_logger)
    return summary, customer_files, len(Results), processing_logger

