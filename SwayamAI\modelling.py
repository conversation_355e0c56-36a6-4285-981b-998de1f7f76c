# Core libraries
import os
import re
import logging
import traceback
import numpy as np
import joblib
from sklearn.model_selection import GridSearchCV, StratifiedKFold, KFold
from sklearn.metrics import accuracy_score, f1_score, precision_recall_fscore_support
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
try:
    from xgboost import XGBClassifier  # type: ignore[import-not-found]
except Exception:
    XGBClassifier = None
    logging.warning("xgboost is not installed or failed to import; disabling xgboost_classifier.")
import pandas as pd
from sklearn.cluster import KMeans as SklearnKMeans, AgglomerativeClustering, DBSCAN, AffinityPropagation
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from scipy import sparse as _sparse
class ClusterModel:
    def __init__(self) -> None:
        self.output_folder = None

    def set_output_folder(self, output_folder: str):
        """Set the folder where fitted models and artifacts can be saved."""
        self.output_folder = output_folder
        if self.output_folder and not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder, exist_ok=True)

    def _sanitize_name(self, name: str) -> str:
        """Make a filesystem-safe filename stem from a model name."""
        try:
            s = re.sub(r"[^A-Za-z0-9_.-]+", "_", str(name)).strip("_")
            return s
        except Exception:
            return "model"

    def check_skewness(self, labels):
        return pd.Series(labels).value_counts(normalize=True).to_dict()

    def _calculate_scores(self, data, labels):
        """Helper to calculate clustering scores, returns tuple of scores."""
        try:
            if len(np.unique(labels)) > 1:
                sil = silhouette_score(data, labels)
                cal = calinski_harabasz_score(data, labels)
                dav = davies_bouldin_score(data, labels)
                return sil, cal, dav
        except Exception as e:
            logging.warning(f"Could not calculate scores: {e}")
        return np.nan, np.nan, np.nan

    def _to_dense(self, X):
        return X.toarray() if _sparse.issparse(X) else np.asarray(X)

    def KMeans(self, df, results, DM, reduced_embeddings, min_k, max_k, name_suffix: str = ""):
        """Perform KMeans clustering for a range of k values."""
        logging.info("Running KMeans clustering...")
        re = self._to_dense(reduced_embeddings)
        # ensure numeric ndarray and suitable shape
        try:
            re = np.asarray(re, dtype=float)
        except Exception:
            logging.warning("KMeans: could not coerce reduced_embeddings to float ndarray; attempting nan_to_num fallback")
            try:
                re = np.nan_to_num(np.asarray(re), nan=0.0, posinf=1e6, neginf=-1e6)
            except Exception:
                logging.exception("KMeans: failed to coerce embeddings to numeric array")
                return df, results

        if re.ndim == 1:
            re = re.reshape(-1, 1)

        # replace inf/nan with finite numbers to avoid sklearn errors
        if np.isnan(re).any() or np.isinf(re).any():
            re = np.nan_to_num(re, nan=0.0, posinf=1e6, neginf=-1e6)

        n_samples = re.shape[0]
        max_possible_k = max(1, min(max_k, n_samples))
        for k in range(min_k, max_possible_k + 1):
            try:
                if k <= 0 or k > n_samples:
                    logging.warning(f"Skipping KMeans for k={k} due to dataset size")
                    continue
                logging.debug(f"KMeans: running k={k} on data shape={re.shape} dtype={re.dtype} nan_count={np.isnan(re).sum()} uniq_rows={np.unique(re, axis=0).shape[0] if re.size>0 else 0}")
                model = SklearnKMeans(n_clusters=k, random_state=42, n_init=10)
                labels = model.fit_predict(re)
                sil, cal, dav = self._calculate_scores(re, labels)
                skew = self.check_skewness(labels)
                n_clusters = len(np.unique(labels))
                model_name = f"KMeans (k={k}){(' ' + name_suffix) if name_suffix else ''}"
                results.loc[len(results)] = [model_name, sil, cal, dav, 1 if any(v > 0.9 for v in skew.values()) else 0, n_clusters]
                df[model_name] = labels
                # persist fitted model for deployment (predict on new rows)
                try:
                    if self.output_folder:
                        safe = self._sanitize_name(model_name)
                        joblib.dump(model, os.path.join(self.output_folder, f"{safe}.pkl"))
                except Exception:
                    logging.warning("Failed to persist KMeans model", exc_info=True)
            except Exception as e:
                # Log full traceback and write a fallback results row with failure markers
                logging.exception(f"KMeans failed for k={k}: {e}")
                # Try a compatibility fallback with n_init='auto' (sklearn compatibility)
                try:
                    logging.debug(f"KMeans fallback: retrying k={k} with n_init='auto'")
                    model = SklearnKMeans(n_clusters=k, random_state=42, n_init='auto')
                    labels = model.fit_predict(re)
                    sil, cal, dav = self._calculate_scores(re, labels)
                    skew = self.check_skewness(labels)
                    n_clusters = len(np.unique(labels))
                    model_name = f"KMeans (k={k}){(' ' + name_suffix) if name_suffix else ''}"
                    results.loc[len(results)] = [model_name, sil, cal, dav, 1 if any(v > 0.9 for v in skew.values()) else 0, n_clusters]
                    df[model_name] = labels
                    # persist fitted model for deployment
                    try:
                        if self.output_folder:
                            safe = self._sanitize_name(model_name)
                            joblib.dump(model, os.path.join(self.output_folder, f"{safe}.pkl"))
                    except Exception:
                        logging.warning("Failed to persist KMeans model (fallback)", exc_info=True)
                    continue
                except Exception as e2:
                    logging.exception(f"KMeans fallback also failed for k={k}: {e2}")
                # create fallback labels to keep dataframe shape consistent
                try:
                    fallback_labels = np.full(n_samples, -1, dtype=int)
                    df[f"KMeans (k={k}){(' ' + name_suffix) if name_suffix else ''}"] = fallback_labels
                except Exception:
                    logging.exception("Failed to insert fallback labels for failed KMeans run")
                # append a results row indicating failure (NaNs for scores)
                model_name = f"KMeans (k={k}){(' ' + name_suffix) if name_suffix else ''}"
                results.loc[len(results)] = [model_name, np.nan, np.nan, np.nan, 1, 0]
        return df, results

    def agglomerative_clustering(self, df, results, DM, reduced_embeddings, name_suffix: str = ""):
        """Agglomerative clustering with automatic cluster selection.

        Strategy:
        - L2-normalize rows (guards cosine distance); if cosine fails due to zero vectors, fallback to euclidean.
        - Fit once to obtain full linkage distances, then choose a cut threshold via the largest gap ("elbow")
          in the merge distances (bounded to mid-quantiles to avoid extremes).
        - Re-fit with distance_threshold and n_clusters=None to let the algorithm decide cluster count.
        """
        logging.info("Running Agglomerative clustering (auto threshold)...")

        def _safe_l2_normalize(X: np.ndarray, eps: float = 1e-12) -> np.ndarray:
            X = np.asarray(X, dtype=float)
            if X.ndim == 1:
                X = X.reshape(-1, 1)
            # compute L2 norms per row as 1D vector for cleaner masking
            norms = np.linalg.norm(X, axis=1)  # shape: (n_samples,)
            bad_rows = ~np.isfinite(norms) | (norms == 0)
            # avoid division by zero
            safe_norms = norms.copy()
            safe_norms[bad_rows] = 1.0
            Xn = X / safe_norms[:, None]
            # nudge zero/invalid rows to avoid cosine errors
            if np.any(bad_rows):
                Xn[bad_rows, 0] += eps
            return Xn

        re = self._to_dense(reduced_embeddings)
        try:
            re = np.asarray(re, dtype=float)
        except Exception:
            re = np.nan_to_num(np.asarray(re), nan=0.0, posinf=1e6, neginf=-1e6)
        if re.ndim == 1:
            re = re.reshape(-1, 1)
        if np.isnan(re).any() or np.isinf(re).any():
            re = np.nan_to_num(re, nan=0.0, posinf=1e6, neginf=-1e6)
        re_n = _safe_l2_normalize(re)

        # First, build full tree to get merge distances
        model_metric = 'cosine'
        full_model = None
        try:
            full_model = AgglomerativeClustering(
                linkage='average', metric=model_metric,
                distance_threshold=0.0, n_clusters=None,
                compute_distances=True
            )
            full_model.fit(re_n)
        except Exception as e_cos:
            logging.warning(f"Agglomerative full-tree (cosine) failed: {e_cos} — falling back to euclidean.")
            model_metric = 'euclidean'
            try:
                full_model = AgglomerativeClustering(
                    linkage='average', metric=model_metric,
                    distance_threshold=0.0, n_clusters=None,
                    compute_distances=True
                )
                full_model.fit(re)
            except Exception as e_euc:
                logging.error(f"Agglomerative full-tree fallback failed: {e_euc}")
                return df, results

        # Derive a threshold from linkage distances (largest gap heuristic)
        distances = getattr(full_model, 'distances_', None)
        if distances is None or len(distances) == 0:
            logging.warning("Agglomerative: no distances_ available; using percentile threshold 0.75 by default")
            thresh = None
        else:
            d = np.asarray(distances, dtype=float)
            d_sorted = np.sort(d)
            # restrict to mid-quantiles to avoid degenerate tiny/huge gaps
            lo_q, hi_q = np.quantile(d_sorted, 0.2), np.quantile(d_sorted, 0.9)
            mask = (d_sorted >= lo_q) & (d_sorted <= hi_q)
            cand = d_sorted[mask]
            if cand.size >= 2:
                gaps = np.diff(cand)
                gi = int(np.argmax(gaps))
                # set threshold between the two merges across the biggest gap
                thresh = float((cand[gi] + cand[gi + 1]) / 2.0)
            else:
                # fallback percentile
                thresh = float(np.quantile(d_sorted, 0.75))

        # Second, fit with chosen threshold (lets model decide n_clusters)
        try:
            auto_model = AgglomerativeClustering(
                linkage='average', metric=model_metric,
                distance_threshold=thresh if thresh is not None else float(np.quantile(d_sorted, 0.75)) if distances is not None and len(distances) > 0 else 0.1,
                n_clusters=None
            )
            X_fit = re_n if model_metric == 'cosine' else re
            labels = auto_model.fit_predict(X_fit)
        except Exception as e_auto:
            logging.error(f"Agglomerative auto-threshold fit failed: {e_auto}")
            return df, results

        sil, cal, dav = self._calculate_scores(X_fit, labels)
        skew = self.check_skewness(labels)
        n_clusters = len(np.unique(labels))
        model_name = f"Agglomerative (auto){(' ' + name_suffix) if name_suffix else ''}"
        results.loc[len(results)] = [model_name, sil, cal, dav, 1 if any(v > 0.9 for v in skew.values()) else 0, n_clusters]
        df[model_name] = labels
        return df, results

    def dbscan(self, df, results, DM, reduced_embeddings, min_samples, name_suffix: str = "", metric: str = "cosine"):
        """Perform DBSCAN clustering with a robust eps heuristic and cosine metric by default."""
        logging.info("Running DBSCAN clustering...")
        try:
            re = self._to_dense(reduced_embeddings)
            from sklearn.neighbors import NearestNeighbors
            n_samples = re.shape[0]
            k = max(1, min(min_samples, max(1, n_samples - 1)))
            try:
                nn = NearestNeighbors(n_neighbors=k, metric=metric)
            except Exception:
                # Fallback to euclidean if cosine not supported
                metric = 'euclidean'
                nn = NearestNeighbors(n_neighbors=k, metric=metric)
            nn.fit(re)
            distances, _ = nn.kneighbors(re)
            # Use 95th percentile of k-distances as eps (more robust than mean)
            kth = distances[:, -1] if distances.size > 0 else np.array([0.5])
            eps = float(np.percentile(kth, 95))
            # Clip eps to sensible range depending on metric
            if metric == 'cosine':
                eps = float(np.clip(eps, 1e-6, 0.8))
            else:
                eps = float(np.clip(eps, 1e-6, np.inf))

            model = DBSCAN(eps=eps, min_samples=min_samples, metric=metric)
            labels = model.fit_predict(re)
            sil, cal, dav = self._calculate_scores(re, labels)
            skew = self.check_skewness(labels)
            unique_labels = set(labels)
            n_clusters = len(unique_labels - {-1})  # exclude noise label
            model_name = f"DBSCAN{(' ' + name_suffix) if name_suffix else ''}"
            results.loc[len(results)] = [model_name, sil, cal, dav, 1 if any(v > 0.9 for v in skew.values()) else 0, n_clusters]
            df[model_name] = labels
        except Exception as e:
            logging.error(f"DBSCAN failed: {e}")
        return df, results

    def affinity_propagation(self, df, results, DM, reduced_embeddings, name_suffix: str = ""):
        """Perform AffinityPropagation clustering."""
        logging.info("Running Affinity Propagation clustering...")
        re = self._to_dense(reduced_embeddings)
        try:
            model = AffinityPropagation()
            labels = model.fit_predict(re)
            sil, cal, dav = self._calculate_scores(re, labels)
            skew = self.check_skewness(labels)
            n_clusters = len(np.unique(labels))
            model_name = f"AffinityPropagation{(' ' + name_suffix) if name_suffix else ''}"
            results.loc[len(results)] = [model_name, sil, cal, dav, 1 if any(v > 0.9 for v in skew.values()) else 0, n_clusters]
            df[model_name] = labels
        except Exception as e:
            logging.error(f"AffinityPropagation failed: {e}")
        return df, results

class Classification:
    output_folder = None

    def __init__(self) -> None:
        # track how many hyperparameter combinations were evaluated across all models
        self.total_param_combinations = 0
        pass

    def set_output_folder(self, Output_folder):
        self.output_folder = Output_folder
        if self.output_folder and not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder, exist_ok=True)

    def _evaluate_model_with_grid_search(self, model, param_grid, X, y, X_test, y_test, Results: pd.DataFrame, validation_Results: pd.DataFrame, model_name: str):
        # Use 5-fold stratified CV and multi-metric scoring
        if len(np.unique(y)) < 2:
            # degenerate case: fit once, no CV
            model.fit(X, y)
            best = model
            best_params = getattr(model, 'get_params', lambda: {})()
            preds = best.predict(X_test)
            acc = accuracy_score(y_test, preds)
            prec_w, rec_w, f1_w, _ = precision_recall_fscore_support(y_test, preds, average='weighted', zero_division=0)
            Results.loc[len(Results)] = [model_name, str(best_params), np.nan, np.nan, np.nan, np.nan]
            validation_Results.loc[len(validation_Results)] = [model_name, str(best_params), acc, f1_w, prec_w, rec_w, f1_w]
            combos = 1
        else:
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
            scoring = {
                'accuracy': 'accuracy',
                'f1_weighted': 'f1_weighted',
                'precision_weighted': 'precision_weighted',
                'recall_weighted': 'recall_weighted'
            }
            gs = GridSearchCV(
                model,
                param_grid,
                cv=cv,
                scoring=scoring,
                refit='f1_weighted',
                n_jobs=-1,
                verbose=0,
                error_score='raise'
            )
            gs.fit(X, y)
            best = gs.best_estimator_
            best_params = gs.best_params_
            cvres = gs.cv_results_
            combos = len(cvres['params'])
            bi = gs.best_index_
            mean_acc = cvres['mean_test_accuracy'][bi]
            mean_f1w = cvres['mean_test_f1_weighted'][bi]
            mean_precw = cvres['mean_test_precision_weighted'][bi]
            mean_recw = cvres['mean_test_recall_weighted'][bi]
            Results.loc[len(Results)] = [model_name, str(best_params), mean_acc, mean_f1w, mean_precw, mean_recw]

            preds = best.predict(X_test)
            acc = accuracy_score(y_test, preds)
            prec_w, rec_w, f1_w, _ = precision_recall_fscore_support(y_test, preds, average='weighted', zero_division=0)
            validation_Results.loc[len(validation_Results)] = [model_name, str(best_params), acc, f1_w, prec_w, rec_w, (mean_f1w + f1_w) / 2.0]

        # persist model
        model_file = os.path.join(self.output_folder or '.', f"{model_name.replace(' ','_')}_model.pkl")
        joblib.dump(best, model_file)

        # accumulate total combos evaluated
        try:
            self.total_param_combinations += int(combos)
        except Exception:
            pass
        return Results, validation_Results

    def logistic_regression(self, X, y, X_test, y_test, Results, validation_Results, model_name="LogisticRegression"):
        model = LogisticRegression(max_iter=2000, class_weight='balanced', n_jobs=-1 if hasattr(LogisticRegression(), 'n_jobs') else None)
        param_grid = {
            'C': [0.01, 0.1, 1.0, 10.0],
            'solver': ['liblinear', 'lbfgs'],
            'penalty': ['l2']
        }
        return self._evaluate_model_with_grid_search(model, param_grid, X, y, X_test, y_test, Results, validation_Results, model_name)

    def multinomial_naive_bayes(self, X, y, X_test, y_test, Results, validation_Results, model_name="MultinomialNB"):
        from sklearn.naive_bayes import MultinomialNB
        model = MultinomialNB()
        param_grid = {'alpha': [0.1, 0.5, 1.0]}
        return self._evaluate_model_with_grid_search(model, param_grid, X, y, X_test, y_test, Results, validation_Results, model_name)

    def support_vector_machine(self, X, y, X_test, y_test, Results, validation_Results, model_name="SVM"):
        model = SVC(probability=True, class_weight='balanced')
        param_grid = {
            'C': [0.1, 1.0, 10.0],
            'kernel': ['linear', 'rbf'],
            'gamma': ['scale', 'auto']
        }
        return self._evaluate_model_with_grid_search(model, param_grid, X, y, X_test, y_test, Results, validation_Results, model_name)

    def random_forest_classifier(self, X, y, X_test, y_test, Results, validation_Results, model_name="RandomForest"):
        model = RandomForestClassifier(n_jobs=-1, class_weight='balanced')
        param_grid = {
            'n_estimators': [100, 300],
            'max_depth': [None, 10, 30],
            'max_features': ['sqrt', 'log2', 0.5]
        }
        return self._evaluate_model_with_grid_search(model, param_grid, X, y, X_test, y_test, Results, validation_Results, model_name)

    def xgboost_classifier(self, X, y, X_test, y_test, Results, validation_Results, model_name="XGBoost"):
        if XGBClassifier is None:
            logging.warning("Skipping XGBoost: xgboost package not available.")
            return Results, validation_Results
        model = XGBClassifier(
            objective='multi:softprob',
            eval_metric='mlogloss',
            tree_method='hist',
            verbosity=0
        )
        param_grid = {
            'n_estimators': [200, 500],
            'max_depth': [4, 8],
            'learning_rate': [0.05, 0.1],
            'subsample': [0.7, 1.0],
            'colsample_bytree': [0.7, 1.0]
        }
        return self._evaluate_model_with_grid_search(model, param_grid, X, y, X_test, y_test, Results, validation_Results, model_name)

from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.linear_model import LinearRegression, BayesianRidge
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
try:
    from xgboost import XGBRegressor  # type: ignore[import-not-found]
except Exception:
    XGBRegressor = None

class Regression:
    """Lightweight regression trainer with a few common models.

    Exposes per-model helpers used by Regression.py. Each helper fits a model,
    computes validation metrics (MSE/MAE/R2), saves the model to output_folder,
    and appends a row to Results with columns:
      ["Model Name", "best_params", "validation_mean_squared_error",
       "validation_mean_absolute_error", "validation_r2_score"].
    Returns (Results, Validation_Results) for chaining.
    """
    def __init__(self) -> None:
        self.output_folder = None
        # track how many hyperparameter combinations were evaluated across all models
        self.total_param_combinations = 0
        # capture per-model grid search combinations for output
        self.grid_rows = []

    def set_output_folder(self, output_folder: str):
        self.output_folder = output_folder
        if self.output_folder and not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder, exist_ok=True)

    def _fit_eval_save(self, model, X_train, y_train, X_test, y_test, Results: pd.DataFrame, Validation_Results: dict, model_name: str):
        try:
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            mse = float(mean_squared_error(y_test, y_pred))
            mae = float(mean_absolute_error(y_test, y_pred))
            r2 = float(r2_score(y_test, y_pred))
            # adjusted R2 using number of predictors (from X_test) and samples (len(y_test))
            try:
                n = float(len(y_test))
                p = float(X_test.shape[1]) if hasattr(X_test, 'shape') else float(np.array(X_test).shape[1])
                adj_r2 = 1.0 - (1.0 - r2) * (n - 1.0) / max(n - p - 1.0, 1.0)
                adj_r2 = float(adj_r2)
            except Exception:
                adj_r2 = r2
            params = getattr(model, 'get_params', lambda: {})()
            # Ensure columns exist or append flexibly
            if 'validation_adjusted_r2_score' in getattr(Results, 'columns', []):
                Results.loc[len(Results)] = [model_name, str(params), mse, mae, r2, adj_r2]
            else:
                Results.loc[len(Results)] = [model_name, str(params), mse, mae, r2]
            Validation_Results[model_name] = {"mse": mse, "mae": mae, "r2": r2, "adj_r2": adj_r2}
            # save model
            try:
                model_path = os.path.join(self.output_folder or '.', f"{model_name.replace(' ','_')}_model.pkl")
                joblib.dump(model, model_path)
            except Exception:
                logging.warning("Failed to persist model %s", model_name)
        except Exception as e:
            logging.exception("%s training failed: %s", model_name, e)
            # append NaN row to keep shapes consistent
            Results.loc[len(Results)] = [model_name, None, np.nan, np.nan, np.nan]
        return Results, Validation_Results

    def _evaluate_model_with_grid_search_regression(self, model, param_grid, X_train, y_train, X_test, y_test, Results: pd.DataFrame, Validation_Results: dict, model_name: str):
        """Grid-search for regression with multi-metric scoring.

        - Refit on 'r2' to select best estimator.
        - After selection, compute validation metrics on X_test/y_test and persist model.
        - On any grid error, fall back to simple fit.
        """
        try:
            # Count combinations deterministically from the grid (product of option counts)
            try:
                grid_size = 1
                for k, v in (param_grid or {}).items():
                    grid_size *= max(1, len(v))
                self.total_param_combinations += int(grid_size)
            except Exception:
                pass
            # small, robust CV split
            n = int(len(y_train)) if hasattr(y_train, '__len__') else 0
            n_splits = 5 if n >= 5 else (3 if n >= 3 else 2)
            cv = KFold(n_splits=n_splits, shuffle=True, random_state=42)
            scoring = {
                'neg_mse': 'neg_mean_squared_error',
                'neg_mae': 'neg_mean_absolute_error',
                'r2': 'r2'
            }
            gs = GridSearchCV(
                model,
                param_grid,
                cv=cv,
                scoring=scoring,
                refit='r2',
                n_jobs=-1,
                verbose=0,
                error_score='raise'
            )
            gs.fit(X_train, y_train)
            best = gs.best_estimator_
            best_params = gs.best_params_
            # collect all grid combinations results for reporting
            try:
                cvres = gs.cv_results_
                n_rows = len(cvres.get('params', []))
                for i in range(n_rows):
                    row = {
                        'model_family': model_name,
                        'params': str(cvres['params'][i]),
                        'mean_test_r2': float(cvres.get('mean_test_r2', [float('nan')]*n_rows)[i]) if 'mean_test_r2' in cvres else float('nan'),
                        'std_test_r2': float(cvres.get('std_test_r2', [float('nan')]*n_rows)[i]) if 'std_test_r2' in cvres else float('nan'),
                        'mean_test_mse': float(-cvres.get('mean_test_neg_mean_squared_error', cvres.get('mean_test_neg_mse', [float('nan')]*n_rows))[i]) if ('mean_test_neg_mean_squared_error' in cvres or 'mean_test_neg_mse' in cvres) else float('nan'),
                        'mean_test_mae': float(-cvres.get('mean_test_neg_mean_absolute_error', cvres.get('mean_test_neg_mae', [float('nan')]*n_rows))[i]) if ('mean_test_neg_mean_absolute_error' in cvres or 'mean_test_neg_mae' in cvres) else float('nan'),
                        'rank_test_r2': int(cvres.get('rank_test_r2', [0]*n_rows)[i]) if 'rank_test_r2' in cvres else 0,
                    }
                    self.grid_rows.append(row)
            except Exception:
                pass

            # Evaluate on validation (test) set
            import numpy as _np
            y_pred = best.predict(X_test)
            from sklearn.metrics import mean_squared_error as _mse, mean_absolute_error as _mae, r2_score as _r2
            mse = float(_mse(y_test, y_pred))
            mae = float(_mae(y_test, y_pred))
            r2 = float(_r2(y_test, y_pred))
            try:
                n_val = float(len(y_test))
                p = float(X_test.shape[1]) if hasattr(X_test, 'shape') else float(_np.array(X_test).shape[1])
            except Exception:
                p = float(_np.array(X_test).shape[1])
            try:
                adj_r2 = 1.0 - (1.0 - r2) * (n_val - 1.0) / max(n_val - p - 1.0, 1.0)
                adj_r2 = float(adj_r2)
            except Exception:
                adj_r2 = r2

            # Append a results row (validation metrics)
            if 'validation_adjusted_r2_score' in getattr(Results, 'columns', []):
                Results.loc[len(Results)] = [model_name, str(best_params), mse, mae, r2, adj_r2]
            else:
                Results.loc[len(Results)] = [model_name, str(best_params), mse, mae, r2]
            Validation_Results[model_name] = {"mse": mse, "mae": mae, "r2": r2, "adj_r2": adj_r2}

            # Persist model
            try:
                import joblib
                model_path = os.path.join(self.output_folder or '.', f"{model_name.replace(' ','_')}_model.pkl")
                joblib.dump(best, model_path)
            except Exception:
                logging.warning("Failed to persist model %s", model_name)

            return Results, Validation_Results
        except Exception as e:
            logging.exception("%s grid search failed: %s", model_name, e)
            # fallback to simple fit (combinations already counted above)
            return self._fit_eval_save(model, X_train, y_train, X_test, y_test, Results, Validation_Results, model_name)

    def linear_regression(self, Results, Validation_Results, X_train, y_train, X_test, y_test, model_name="linear_regression"):
        model = LinearRegression()
        # Minimal grid for LR
        param_grid = {
            'fit_intercept': [True, False],
        }
        return self._evaluate_model_with_grid_search_regression(model, param_grid, X_train, y_train, X_test, y_test, Results, Validation_Results, model_name)

    def decision_tree_regressor(self, Results, Validation_Results, X_train, y_train, X_test, y_test, model_name="decision_tree_regressor"):
        model = DecisionTreeRegressor(random_state=42)
        param_grid = {
            'max_depth': [None, 10, 30],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 5]
        }
        return self._evaluate_model_with_grid_search_regression(model, param_grid, X_train, y_train, X_test, y_test, Results, Validation_Results, model_name)

    def random_forest_regressor(self, Results, Validation_Results, X_train, y_train, X_test, y_test, model_name="random_forest_regressor"):
        model = RandomForestRegressor(random_state=42, n_jobs=-1)
        param_grid = {
            'n_estimators': [100, 300],
            'max_depth': [None, 10, 30],
            'max_features': ['sqrt', 0.5]
        }
        return self._evaluate_model_with_grid_search_regression(model, param_grid, X_train, y_train, X_test, y_test, Results, Validation_Results, model_name)

    def xgboost_regressor(self, Results, Validation_Results, X_train, y_train, X_test, y_test, model_name="xgboost_regressor"):
        if XGBRegressor is None:
            logging.warning("Skipping XGBRegressor: xgboost not available")
            # append placeholder row
            Results.loc[len(Results)] = [model_name, None, np.nan, np.nan, np.nan]
            return Results, Validation_Results
        model = XGBRegressor(
            objective='reg:squarederror',
            tree_method='hist',
            random_state=42,
            verbosity=0
        )
        param_grid = {
            'n_estimators': [200, 500],
            'max_depth': [4, 8],
            'learning_rate': [0.05, 0.1],
            'subsample': [0.8, 1.0],
            'colsample_bytree': [0.8, 1.0]
        }
        return self._evaluate_model_with_grid_search_regression(model, param_grid, X_train, y_train, X_test, y_test, Results, Validation_Results, model_name)

    def support_vector_regressor(self, Results, Validation_Results, X_train, y_train, X_test, y_test, model_name="support_vector_regressor"):
        model = SVR()
        param_grid = {
            'kernel': ['rbf', 'linear'],
            'C': [0.1, 1.0, 10.0],
            'gamma': ['scale', 'auto']
        }
        return self._evaluate_model_with_grid_search_regression(model, param_grid, X_train, y_train, X_test, y_test, Results, Validation_Results, model_name)

    def bayesian_ridge_regressor(self, Results, Validation_Results, X_train, y_train, X_test, y_test, model_name="bayesian_ridge_regressor"):
        model = BayesianRidge()
        param_grid = {
            'alpha_1': [1e-6, 1e-5],
            'alpha_2': [1e-6, 1e-5],
            'lambda_1': [1e-6, 1e-5],
            'lambda_2': [1e-6, 1e-5]
        }
        return self._evaluate_model_with_grid_search_regression(model, param_grid, X_train, y_train, X_test, y_test, Results, Validation_Results, model_name)
