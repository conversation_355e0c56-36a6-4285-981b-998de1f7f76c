import os
import sys
import subprocess
import pandas as pd
import numpy as np

from Regression import regression_process


def make_regression_df(n=120, seed=0):
    rng = np.random.default_rng(seed)
    x1 = rng.normal(0, 1, size=n)
    x2 = rng.normal(2, 3, size=n)
    cat = rng.choice(['A','B','C'], size=n)
    y = 2.5 * x1 - 1.2 * x2 + (cat == 'B')*0.8 + rng.normal(0, 0.1, size=n)
    return pd.DataFrame({'x1': x1, 'x2': x2, 'cat': cat, 'y': y})


def test_regression_non_nlp_flow(tmp_path, tmp_output_dir):
    df = make_regression_df()
    inp = tmp_path / 'train.csv'
    df.to_csv(inp, index=False)

    summary, files, n_models, log = regression_process(
        str(inp), fields=['x1','x2','cat'], target='y', output_folder=str(tmp_output_dir), selection_policy='cv'
    )

    assert isinstance(files, list) and files
    # A results workbook should exist
    paths = [p for p in ['regression_results_numerical.xlsx', 'regression_results.xlsx'] if (tmp_output_dir / p).exists()]
    assert paths, f"No results workbook found in {tmp_output_dir}"

    # There should be a model and encoding artifacts
    assert any(str(p).endswith('_model.pkl') for p in (tmp_output_dir.iterdir()))
    # Expect new config name; allow legacy for backward-compat
    required = ['encoding.pkl','imputer.pkl','scaler.pkl']
    for art in required:
        assert (tmp_output_dir / art).exists(), f"Missing {art}"
    assert (tmp_output_dir / 'swayamml.conf').exists(), "Missing swayamml.conf"

    # Run NON-NLP deployment script if present
    dep = tmp_output_dir / 'deployment_script_regression_non_nlp.py'
    if dep.exists():
        test_csv = tmp_path / 'new.csv'
        df.drop(columns=['y']).head(12).to_csv(test_csv, index=False)
        out_csv = tmp_path / 'preds.csv'
        proc = subprocess.run([sys.executable, str(dep), '--input', str(test_csv), '--output', str(out_csv)], capture_output=True, text=True)
        assert proc.returncode == 0, proc.stderr
        out = pd.read_csv(out_csv)
        assert 'predicted_value' in out.columns
