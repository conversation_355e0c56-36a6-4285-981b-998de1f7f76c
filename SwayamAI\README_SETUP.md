# Setup and Run Guide (Windows)

This guide helps anyone set up and run the SwayamAI app on Windows with minimal steps.

## Prerequisites
- Windows 10/11
- Python 3.10 or newer installed from python.org or the Microsoft Store
- Optional: Git (if you cloned this repo)

If `python` isn't in PATH, the script will try `py -3` automatically.

## Quick Start
1) Double‑click `setup.bat` (or run it in PowerShell) to:
   - Create a virtual environment at `.venv`
   - Activate it
   - Install dependencies from `requirements.txt`

2) Create your environment file:
   - Copy `.env.example` to `.env`
   - Edit values if needed (you can keep defaults for local dev). At minimum, set a strong `FLASK_SECRET_KEY` for production.

3) Run the app:
   - Open a new PowerShell window in the project folder
   - Activate the venv (PowerShell): `./.venv/Scripts/Activate.ps1`
   - Start the server: `python app.py`
   - Navigate to http://127.0.0.1:5000

## What the app does
- Provides a simple UI to upload CSV/Excel files and run modelling tasks.
- Saves uploads in `uploads/<job_id>/` and outputs in `outputs/<job_id>/`.

## Environment variables (.env)
The app loads `.env` automatically if `python-dotenv` is installed (it is optional; the app falls back to defaults otherwise). Common keys:

- `FLASK_SECRET_KEY` — secret for Flask sessions (set a strong value in production)
- `FLASK_PORT` — server port (default `5000`)
- `FLASK_DEBUG` — `true`/`false` (default `true` for local dev)
- `SKLEARN_ALLOW_INVALID_RESPONSES` — internal testing toggle (optional)
- `MAX_ENGINEERED_FEATURES` — internal testing toggle (optional)

You can also set proxy or CUDA variables if needed.

## Troubleshooting
- If heavy ML/NLP packages (e.g., `torch`, `transformers`) are slow to install, ensure you have a stable internet connection and enough disk space. These are optional unless you use those features.
- If `openpyxl` or `xlrd` errors occur, ensure `requirements.txt` installation finished successfully.
- If `python` is not found, install Python 3.10+ and re-run `setup.bat`.
- For permission issues, try running PowerShell as Administrator.

## Optional: Update dependencies
Within the activated environment:
- Upgrade pip: `python -m pip install --upgrade pip`
- Reinstall requirements: `python -m pip install -r requirements.txt --upgrade`

## Uninstall / Reset
- To remove the virtual environment, delete the `.venv` folder.
- To clean generated data, delete `uploads/` and `outputs/` folders.

## Production (WSGI)
For production on Windows, run the app behind a WSGI server like Waitress.

PowerShell (Windows):

```powershell
./.venv/Scripts/Activate.ps1
python -m waitress --listen=0.0.0.0:5000 wsgi:application
```

If the activation script is blocked:

```powershell
Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
```

Linux/macOS example (Gunicorn):

```bash
gunicorn -w 2 -b 0.0.0.0:5000 wsgi:application
```
