import os
import html
import uuid
import traceback
import pandas as pd
from flask import Flask, render_template, request, redirect, url_for, send_from_directory, flash
"""
Optional .env support: if python-dotenv is present, load variables from a local .env file
so users can configure the app without setting OS-level environment variables.
"""
try:
    from dotenv import load_dotenv  # type: ignore
    load_dotenv()
except Exception:
    pass

# Processing modules are imported lazily inside the request handler to avoid
# import-time failures for optional heavy ML/NLP packages. See run_job().

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UPLOAD_FOLDER = os.path.join(BASE_DIR, 'uploads')
OUTPUT_FOLDER = os.path.join(BASE_DIR, 'outputs')
ALLOWED_EXT = {'.csv', '.xls', '.xlsx'}

# Max upload size (50 MB)
MAX_UPLOAD_SIZE = 50 * 1024 * 1024


os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

app = Flask(__name__)
# Allow overriding via environment. Defaults keep dev experience simple.
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'dev-ui-secret')
app.config['MAX_CONTENT_LENGTH'] = MAX_UPLOAD_SIZE


@app.context_processor
def inject_globals():
    """Inject small UI helpers into templates."""
    from datetime import datetime
    return { 'current_year': datetime.utcnow().year }


def allowed_file(filename):
    _, ext = os.path.splitext(filename)
    return ext.lower() in ALLOWED_EXT


def normalize_process_return(ret):
    # Normalize to (summary, customer_files, num_models). Hide processing log from UI.
    summary = None
    customer_files = []
    num_models = None

    # dict return
    if isinstance(ret, dict):
        return (
            ret.get('summary'),
            ret.get('customer_files', []),
            ret.get('num_models'),
        )

    if not isinstance(ret, (list, tuple)):
        return (None, [], None)

    # pick DataFrame-like as summary
    for x in ret:
        try:
            import pandas as _pd
            if isinstance(x, _pd.DataFrame):
                summary = x
                break
        except Exception:
            pass

    # pick list as customer_files
    for x in ret:
        if isinstance(x, list):
            customer_files = x
            break

    # pick int as num_models
    for x in ret:
        if isinstance(x, int):
            num_models = x
            break

    if summary is None and len(ret) > 0:
        summary = ret[0]
    if not customer_files and len(ret) > 1 and isinstance(ret[1], list):
        customer_files = ret[1]
    return summary, customer_files, num_models


@app.route('/swayam-ml')
def swayam_ml():
    """Existing ML workflow page (moved from '/' to '/swayam-ml')."""
    use_cases = [
        ('classification', 'Classification ( Textual Data)'),
        ('non_nlp_classification', 'Classification (Numerical/Categorical Data)'),
        ('clustering', 'Clustering (Textual Data)'),
        ('sentiment', 'Sentiment Analysis (Text)'),
        ('non_nlp_clustering', 'Clustering (Numerical/Categorical Data)'),
        ('anomaly_numeric', 'Anomaly Detection (Numerical Data)'),
        ('regression', 'Regression'),
        ('text_similarity', 'Text Similarity'),
        ('affinity', 'Affinity Analysis')
    ]
    return render_template('index.html', use_cases=use_cases)


@app.route('/')
def index():
    """New SwayamAI landing page linking to product areas."""
    sections = [
    { 'slug': 'swayam-ml', 'title': 'SwayamML', 'desc': 'Model Studio for text and tabular data. Upload, configure, and get models and reports.' },
    { 'slug': 'swayam-courses', 'title': 'SwayamCourses', 'desc': 'Curated courses and learning paths for AI/ML and data.' },
    { 'slug': 'swayam-datasets', 'title': 'SwayamDatasets', 'desc': 'Sample and community datasets to experiment and learn.' },
        { 'slug': 'swayam-images', 'title': 'SwayamImages', 'desc': 'Computer vision playground for classification, detection, and simple editing.' },
        { 'slug': 'swayam-rag', 'title': 'SwayamRAG', 'desc': 'Retrieval-Augmented Generation: ingest docs, query, and evaluate answers.' },
        { 'slug': 'swayam-agents', 'title': 'SwayamAgents', 'desc': 'Composable multi-tool agents for workflows and automation.' },
        { 'slug': 'swayam-chat', 'title': 'SwayamChat', 'desc': 'Chat with your data or models with conversation history and exports.' },
        { 'slug': 'swayam-api', 'title': 'SwayamAPI', 'desc': 'REST APIs to integrate Swayam services into your apps.' },
        { 'slug': 'swayam-ed-game', 'title': 'SwayamEdGame', 'desc': 'Learning hub: interactive ML/AI mini-games and lessons.' },
        { 'slug': 'swayam-quiz-puzzles', 'title': 'SwayamQuiz & Puzzles', 'desc': 'Sharpen skills with quizzes and logic puzzles.' },
        { 'slug': 'swayam-building-blocks', 'title': 'SwayamBuildingBlocks', 'desc': 'Re-usable components: embeddings, vector stores, prompts, pipelines.' },
        { 'slug': 'swayam-dashboard', 'title': 'SwayamDashboard', 'desc': 'Unified dashboards for runs, datasets, and models.' },
    ]
    return render_template('home.html', sections=sections)


# Stub routes for upcoming sections (render generic section page)
@app.route('/swayam-images')
def swayam_images():
    return render_template('section.html', title='SwayamImages', desc='Computer vision tools coming soon.', slug='swayam-images')


@app.route('/swayam-rag')
def swayam_rag():
    return render_template('section.html', title='SwayamRAG', desc='RAG pipelines and evaluations coming soon.', slug='swayam-rag')


@app.route('/swayam-agents')
def swayam_agents():
    return render_template('section.html', title='SwayamAgents', desc='Agentic workflows coming soon.', slug='swayam-agents')


@app.route('/swayam-chat')
def swayam_chat():
    return render_template('section.html', title='SwayamChat', desc='Chat interface coming soon.', slug='swayam-chat')


@app.route('/swayam-api')
def swayam_api():
    return render_template('section.html', title='SwayamAPI', desc='Service APIs and docs coming soon.', slug='swayam-api')


@app.route('/swayam-ed-game')
def swayam_ed_game():
    """SwayamEdGame hub: list available games."""
    return render_template('swayam_ai/ed_game/index.html')


@app.route('/swayam-ed-game/sudoku')
def swayam_ed_game_sudoku():
    """Sudoku game page (client-side)."""
    return render_template('swayam_ai/ed_game/sudoku.html')


@app.route('/swayam-quiz-puzzles')
def swayam_quiz_puzzles():
    return render_template('section.html', title='SwayamQuiz & Puzzles', desc='Quizzes and puzzles coming soon.', slug='swayam-quiz-puzzles')


@app.route('/swayam-building-blocks')
def swayam_building_blocks():
    return render_template('section.html', title='SwayamBuildingBlocks', desc='Reusable components coming soon.', slug='swayam-building-blocks')


@app.route('/swayam-dashboard')
def swayam_dashboard():
    return render_template('section.html', title='SwayamDashboard', desc='Dashboards coming soon.', slug='swayam-dashboard')


# New: Courses and Datasets placeholder routes
@app.route('/swayam-courses')
def swayam_courses():
    return render_template('swayam_ai/courses/index.html')


@app.route('/swayam-datasets')
def swayam_datasets():
    return render_template('swayam_ai/datasets/index.html')


@app.route('/upload', methods=['POST'])
def upload():
    use_case = request.form.get('use_case')
    file = request.files.get('file')
    if not file or file.filename == '':
        flash('No file selected')
        return redirect(url_for('index'))
    if not allowed_file(file.filename):
        flash('Unsupported file type. Use CSV or Excel.')
        return redirect(url_for('index'))

    # Server-side size checks: prefer request.content_length, fallback to file stream size
    try:
        if request.content_length and request.content_length > MAX_UPLOAD_SIZE:
            flash('Uploaded file is too large (max 50 MB).')
            return redirect(url_for('index'))
    except Exception:
        pass

    try:
        # try to determine size of the uploaded file without fully reading it into memory
        try:
            file.stream.seek(0, os.SEEK_END)
            fsize = file.stream.tell()
            file.stream.seek(0)
            if fsize > MAX_UPLOAD_SIZE:
                flash('Uploaded file is too large (max 50 MB).')
                return redirect(url_for('index'))
        except Exception:
            # if not seekable, attempt a safe read of a limited amount
            pass
    except Exception:
        pass

    job_id = str(uuid.uuid4())
    job_upload_dir = os.path.join(UPLOAD_FOLDER, job_id)
    os.makedirs(job_upload_dir, exist_ok=True)
    file_path = os.path.join(job_upload_dir, str(file.filename))
    file.save(file_path)

    # Read columns to allow field selection
    try:
        _, ext = os.path.splitext(str(file.filename))
        if ext.lower() == '.csv':
            df = pd.read_csv(file_path, nrows=5)
        else:
            df = pd.read_excel(file_path, nrows=5)
    except Exception as e:
        tb = traceback.format_exc()
        # persist an error file for debugging
        try:
            output_job_dir = os.path.join(OUTPUT_FOLDER, job_id)  # FIX: define output_job_dir
            os.makedirs(output_job_dir, exist_ok=True)
            with open(os.path.join(output_job_dir, 'upload_error.txt'), 'w', encoding='utf-8') as fh:
                fh.write(tb)
        except Exception:
            pass
        return render_template('results.html', error=f'Failed to read uploaded file columns: {e}', traceback=tb, use_case=use_case)

    columns = df.columns.tolist()

    # Friendly labels for use cases shown in the UI
    use_case_labels = {
        'classification': 'Classification ( Textual Data)',
    'non_nlp_classification': 'Classification (Numerical/Categorical Data)',
        'clustering': 'Clustering (Textual Data)',
    'sentiment': 'Sentiment Analysis (Text)',
        'non_nlp_clustering': 'Clustering (Numerical/Categorical Data)',
    'anomaly_numeric': 'Anomaly Detection (Numerical Data)',
    'regression': 'Regression',
    'text_similarity': 'Text Similarity',
    'affinity': 'Affinity Analysis',
    }
    key = use_case or ''
    use_case_label = use_case_labels.get(key, key or 'Unknown')

    return render_template('configure.html', job_id=job_id, use_case=use_case, use_case_label=use_case_label, filename=file.filename, columns=columns)


@app.route('/run', methods=['POST'])
def run_job():
    job_id = request.form.get('job_id')
    use_case = request.form.get('use_case')
    filename = request.form.get('filename')
    fields = request.form.getlist('fields')
    label = request.form.get('label') or request.form.get('target') or ''
    selection_policy = request.form.get('selection_policy')
    anomaly_mode = request.form.get('anomaly_mode') or 'all'
    index_column = request.form.get('index_column') or ''
    sentiment_profile = request.form.get('sentiment_profile') or 'fast_general'
    neutral_band = bool(request.form.get('neutral_band'))
    text_column = request.form.get('text_column') or ''
    input_text = request.form.get('input_text') or ''
    # Affinity-specific
    affinity_algo = request.form.get('affinity_algo') or 'apriori'
    affinity_mode = request.form.get('affinity_mode') or 'single'
    affinity_index_col = request.form.get('affinity_index_col') or ''
    affinity_single_text_col = request.form.get('affinity_single_text_col') or ''
    affinity_delimiter = request.form.get('affinity_delimiter') or ','
    affinity_min_support = float(request.form.get('affinity_min_support') or 0.05)
    affinity_min_confidence = float(request.form.get('affinity_min_confidence') or 0.3)
    affinity_min_lift = request.form.get('affinity_min_lift')
    affinity_min_lift = (float(affinity_min_lift) if affinity_min_lift not in (None, '') else None)
    affinity_max_rules = request.form.get('affinity_max_rules')
    affinity_max_rules = (int(affinity_max_rules) if affinity_max_rules not in (None, '') else None)

    job_upload_dir = os.path.join(UPLOAD_FOLDER, str(job_id))
    if not os.path.exists(job_upload_dir):
        flash('Job not found or expired')
        return redirect(url_for('index'))

    file_path = os.path.join(job_upload_dir, str(filename))
    output_job_dir = os.path.join(OUTPUT_FOLDER, str(job_id))
    # ensure output dir exists early so upload errors can be saved
    os.makedirs(output_job_dir, exist_ok=True)

    try:
        # Lazy import processing functions so the Flask app can start even if heavy
        # ML/NLP packages are not yet installed. A missing dependency will be
        # reported when the user tries to run a job.
        if use_case == 'classification':
            from Classification_Modelling import classification_process
            summary, customer_files, num_models = normalize_process_return(
                classification_process(file_path, fields, label, output_job_dir)
            )
        elif use_case == 'clustering':
            from Cluster_Modelling import clustering_process
            summary, customer_files, num_models = normalize_process_return(
                clustering_process(file_path, fields, output_job_dir)
            )
        elif use_case == 'non_nlp_clustering':
            from non_nlp_clustering import non_nlp_clustering_process
            summary, customer_files, num_models = normalize_process_return(
                non_nlp_clustering_process(file_path, fields, output_job_dir)
            )
        elif use_case == 'non_nlp_classification':
            from non_nlp_classification import non_nlp_classification_process
            summary, customer_files, num_models = normalize_process_return(
                non_nlp_classification_process(file_path, fields, label, output_job_dir)
            )
        elif use_case == 'sentiment':
            from sentiment_analysis import sentiment_analysis_process
            # Use the first selected field as the text column
            text_column = (fields[0] if fields else '')
            summary, customer_files, num_models = normalize_process_return(
                sentiment_analysis_process(file_path, text_column, output_job_dir, profile=sentiment_profile, neutral_band=neutral_band)
            )
        elif use_case == 'regression':
            from Regression import regression_process
            # Pass optional selection_policy through to processing
            summary, customer_files, num_models = normalize_process_return(
                regression_process(file_path, fields, label, output_job_dir, selection_policy=selection_policy)
            )
        elif use_case == 'anomaly_numeric':
            from anomaly_detection_numeric import anomaly_detection_numeric_process
            summary, customer_files, num_models = normalize_process_return(
                anomaly_detection_numeric_process(
                    file_path,
                    fields,
                    output_job_dir,
                    mode=anomaly_mode,
                    index_column=(index_column or None),
                )
            )
        elif use_case == 'text_similarity':
            from text_similarity import text_similarity_process
            # If text_column is still empty, try to infer from selected fields (first selected)
            if not text_column and fields:
                text_column = fields[0]
            summary, customer_files, num_models = normalize_process_return(
                text_similarity_process(
                    file_path,
                    text_column,
                    input_text,
                    output_job_dir,
                    index_col=(index_column or None),
                )
            )
        elif use_case == 'affinity':
            from affinity_analysis import affinity_analysis_process
            # For multi-column mode, use selected fields as item columns; for single, use provided text column
            summary, customer_files, num_models = normalize_process_return(
                affinity_analysis_process(
                    file_path,
                    output_job_dir,
                    algo=affinity_algo,
                    index_col=(affinity_index_col or None),
                    data_mode=affinity_mode,
                    fields=fields,
                    single_text_column=(affinity_single_text_col or None),
                    delimiter=affinity_delimiter,
                    min_support=affinity_min_support,
                    min_confidence=affinity_min_confidence,
                    min_lift=affinity_min_lift,
                    max_rules=affinity_max_rules,
                )
            )
        else:
            flash('Unsupported use case')
            return redirect(url_for('index'))
    except ImportError as e:
        tb = traceback.format_exc()
        return render_template('results.html', error=f"Missing package: {e}. Please install required packages in the Python environment.", traceback=tb, use_case=use_case)
    except Exception as e:
        tb = traceback.format_exc()
        return render_template('results.html', error=str(e), traceback=tb, use_case=use_case)

    # Build downloadable file links
    download_files = []
    for f in customer_files or []:
        # if f is an absolute path, use basename
        fname = os.path.basename(f)
        possible_path = os.path.join(output_job_dir, fname)
        if os.path.exists(possible_path):
            download_files.append(fname)
        else:
            # some functions may return filenames only, check in output folder
            other_path = os.path.join(output_job_dir, f)
            if os.path.exists(other_path):
                download_files.append(os.path.basename(other_path))

    # Prepare summary HTML (be strict to avoid calling methods on None)
    summary_html = None
    best_model_name = None
    best_model_params_compact = None
    # Friendly names for non-NLP classification model codes
    friendly_model_names = {
        'logreg': 'Logistic Regression (logreg)',
        'sgd': 'Stochastic Gradient Descent (sgd)',
        'rf': 'Random Forest (rf)',
        'et': 'Extra Trees (et)',
        'gb': 'Gradient Boosting (gb)',
        'svc': 'Support Vector Classifier (svc)',
        'xgboost': 'XGBoost (xgboost)',
    }
    try:
        if isinstance(summary, pd.DataFrame):
            df_display = summary.copy()
            # Hide verbose parameters from UI if present
            for col in ['params', 'Params', 'hyperparameters', 'est_params']:
                if col in df_display.columns:
                    df_display = df_display.drop(columns=[col])
            # Replace model codes with friendly names for non-NLP classification
            try:
                if use_case == 'non_nlp_classification' and 'model' in df_display.columns:
                    df_display['model'] = df_display['model'].map(lambda x: friendly_model_names.get(str(x), str(x)))
            except Exception:
                pass
            summary_html = df_display.to_html(classes='table table-striped', index=False, escape=False)

            # Extract compact best-model info for non_nlp_classification
            try:
                if use_case == 'non_nlp_classification' and 'is_best' in summary.columns:
                    best_rows = summary[summary['is_best'] == True]
                    if not best_rows.empty:
                        row = best_rows.iloc[0]
                        raw_name = str(row.get('model', ''))
                        best_model_name = friendly_model_names.get(raw_name, raw_name or None)
                        # parse params JSON if present
                        params_json = row.get('params', None)
                        compact = None
                        if isinstance(params_json, str) and params_json:
                            import json as _json
                            try:
                                p = _json.loads(params_json)
                                # pick a few key params if available
                                keys = ['n_estimators', 'max_depth', 'learning_rate', 'C', 'gamma', 'alpha', 'penalty', 'max_features']
                                items = []
                                for k in keys:
                                    if k in p:
                                        items.append(f"{k}={p[k]}")
                                compact = ', '.join(items) if items else None
                            except Exception:
                                compact = None
                        best_model_params_compact = compact
            except Exception:
                pass
        elif summary is None:
            summary_html = None
        elif isinstance(summary, (list, dict)):
            summary_html = pd.DataFrame(summary).to_html(classes='table table-striped', index=False, escape=False)
        else:
            summary_html = str(summary)
    except Exception:
        summary_html = str(summary) if summary is not None else None

    # Extract best model name for NLP classification to display next to deploy cmd
    try:
        if use_case == 'classification' and best_model_name is None:
            # Prefer explicit column from summary DataFrame (Classification_Modelling best summary)
            if isinstance(summary, pd.DataFrame) and not summary.empty and ('Best Model Name' in summary.columns):
                val = summary['Best Model Name'].iloc[0]
                if pd.notnull(val):
                    best_model_name = str(val)
            # Fallback: infer from returned customer_files (best model *_model.pkl is included)
            if not best_model_name:
                for f in (customer_files or []):
                    fname = os.path.basename(str(f))
                    if fname.endswith('_model.pkl'):
                        best_model_name = fname[:-len('_model.pkl')]
                        break
    except Exception:
        pass

    # For Text Similarity, replace table with a short descriptive summary and Top 5
    if use_case == 'text_similarity':
        model_used = 'sentence-transformers/all-MiniLM-L6-v2'
        is_query_mode = bool(input_text)
        file_name = 'text_similarity.csv' if is_query_mode else 'text_similarity_pairs.csv'
        file_hint = f"{file_name} ({'query vs all' if is_query_mode else 'pairwise among rows'})"

        # Build Top 5 list from the saved CSV
        top5_html = ''
        try:
            csv_path = os.path.join(output_job_dir, file_name)
            if os.path.exists(csv_path):
                df_top = pd.read_csv(csv_path)
                if is_query_mode and 'similarity' in df_top.columns:
                    df_top = df_top.sort_values('similarity', ascending=False).head(5)
                    # Determine id column name
                    id_col = None
                    for c in df_top.columns:
                        if c not in ('query_text', 'similarity') and c != (request.form.get('text_column') or ''):
                            # heuristic: prefer given index_column if present
                            if c == (request.form.get('index_column') or ''):
                                id_col = c
                                break
                    if id_col is None:
                        # fallbacks
                        id_col = (request.form.get('index_column') or '')
                        if id_col not in df_top.columns:
                            id_col = 'row_id' if 'row_id' in df_top.columns else df_top.columns[0]
                    text_col = request.form.get('text_column') or ''
                    items = []
                    for _, r in df_top.iterrows():
                        rid = html.escape(str(r.get(id_col, '')))
                        txt = html.escape(str(r.get(text_col, '')))[:160]
                        sim = r.get('similarity', None)
                        sim_str = f"{float(sim):.4f}" if pd.notnull(sim) else ""
                        items.append(f"<li><code>{rid}</code> · sim={sim_str} · {txt}</li>")
                    if items:
                        top5_html = "<div class='mt-2'><div class='fw-semibold small mb-1'>Top 5 most similar</div><ol class='small mb-0'>" + ''.join(items) + "</ol></div>"
                elif not is_query_mode and 'similarity' in df_top.columns:
                    df_top = df_top.sort_values('similarity', ascending=False).head(5)
                    text_col = request.form.get('text_column') or ''
                    # Determine id base
                    id_base = request.form.get('index_column') or 'row_id'
                    id1 = f"{id_base}_1"
                    id2 = f"{id_base}_2"
                    if id1 not in df_top.columns or id2 not in df_top.columns:
                        # fallback to row_id_1/2
                        id1 = 'row_id_1' if 'row_id_1' in df_top.columns else df_top.columns[0]
                        id2 = 'row_id_2' if 'row_id_2' in df_top.columns else df_top.columns[1] if len(df_top.columns)>1 else df_top.columns[0]
                    t1 = f"{text_col}_1"
                    t2 = f"{text_col}_2"
                    items = []
                    for _, r in df_top.iterrows():
                        a = html.escape(str(r.get(id1, '')))
                        b = html.escape(str(r.get(id2, '')))
                        ta = html.escape(str(r.get(t1, '')))[:120]
                        tb = html.escape(str(r.get(t2, '')))[:120]
                        sim = r.get('similarity', None)
                        sim_str = f"{float(sim):.4f}" if pd.notnull(sim) else ""
                        items.append(f"<li><code>{a}</code> ↔ <code>{b}</code> · sim={sim_str}<br/><span class='text-muted'>{ta}</span><br/><span class='text-muted'>{tb}</span></li>")
                    if items:
                        top5_html = "<div class='mt-2'><div class='fw-semibold small mb-1'>Top 5 most similar pairs</div><ol class='small mb-0'>" + ''.join(items) + "</ol></div>"
        except Exception:
            top5_html = ''

        summary_html = (
            f"<div class='small'>"
            f"<p class='mb-1'><strong>Your similarity analysis is ready for review.</strong></p>"
            f"<p class='mb-1'>We computed cosine similarity (range −1 to 1) using the Sentence‑Transformers model <code>{model_used}</code>.</p>"
            f"<p class='mb-1'>Download <code>{file_hint}</code> from the Downloads section. To use the scores:</p>"
            f"<ul class='mb-0'>"
            f"<li>Sort by <em>similarity</em> descending to find the most similar; ascending for least similar.</li>"
            f"<li>Optionally apply a threshold (e.g., ≥ 0.7) to flag strong matches.</li>"
            f"</ul>"
            f"{top5_html}"
            f"</div>"
        )

    # For Affinity Analysis, remove the table from UI (downloads + guidance only)
    if use_case == 'affinity':
        summary_html = None

    # Friendly labels for use cases
    use_case_labels = {
        'classification': 'Classification ( Textual Data)',
    'non_nlp_classification': 'Classification (Numerical/Categorical Data)',
        'clustering': 'Clustering (Textual Data)',
    'sentiment': 'Sentiment Analysis (Text)',
        'non_nlp_clustering': 'Clustering (Numerical/Categorical Data)',
    'anomaly_numeric': 'Anomaly Detection (Numerical Data)',
    'regression': 'Regression',
    'text_similarity': 'Text Similarity',
    'affinity': 'Affinity Analysis (Market Basket)',
    }
    key = use_case or ''
    use_case_label = use_case_labels.get(key, key or 'Unknown')

    return render_template(
        'results.html',
        summary_html=summary_html,
        download_files=download_files,
        job_id=job_id,
        num_models=num_models,
        use_case=use_case,
        use_case_label=use_case_label,
        selected_fields=fields,
        selected_label=label,
    selection_policy=selection_policy,
    anomaly_mode=anomaly_mode,
    index_column=index_column,
    text_column=text_column,
    input_text=input_text,
    best_model_name=best_model_name,
    best_model_params_compact=best_model_params_compact,
        show_processing_log=False  # hide processing log section
        # processing_logger not passed
    )


@app.route('/download/<job_id>/<path:filename>')
def download(job_id, filename):
    job_output_dir = os.path.join(OUTPUT_FOLDER, job_id)
    return send_from_directory(job_output_dir, filename, as_attachment=True)


if __name__ == '__main__':
    # Read debug/port from env with safe defaults
    _dbg = os.getenv('FLASK_DEBUG', 'true').lower() in ('1', 'true', 'yes', 'on')
    _port_str = os.getenv('FLASK_PORT', '5000')
    try:
        _port = int(_port_str)
    except Exception:
        _port = 5000
    app.run(debug=_dbg, port=_port)
