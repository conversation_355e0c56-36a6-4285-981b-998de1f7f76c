<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{% block title %}SwayamAI{% endblock %}</title>

  <!-- Tailwind CDN for utility-first styling (enable class-based dark mode) -->
  <script>tailwind = window.tailwind || {}; tailwind.config = { darkMode: 'class' }</script>
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Bootstrap 5 CSS (CDN) -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Choices.js for enhanced selects -->
  <!-- Choices CSS (CDN full) -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css" />
  <!-- Local Choices styles (bundled) -->
  <link rel="stylesheet" href="{{ url_for('static', filename='vendor/choices/choices.min.css') }}" />
  <!-- Project styles -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/app.css') }}" />

    {% block extra_head %}{% endblock %}

    <style>
      main.container { max-width: 1100px; }

      /* Stronger base text contrast */
      body, main { color: rgba(15, 23, 42, 0.95) !important; }
      .text-slate-800 { color: rgba(15, 23, 42, 0.95) !important; }
      .dark body, .dark main { color: rgba(226, 232, 240, 0.95) !important; }
      .dark .text-slate-200 { color: rgba(226, 232, 240, 0.95) !important; }

      /* Make form labels and helper text more legible */
      .form-label, .form-text, label, .nav-link, .list-group-item, .table th, .table td {
        color: rgba(15, 23, 42, 0.95) !important; /* slate-900 */
      }
      /* Dark mode counterparts */
      .dark .form-label, .dark .form-text, .dark label, .dark .nav-link, .dark .list-group-item, .dark .table th, .dark .table td {
        color: rgba(226, 232, 240, 0.95) !important; /* slate-200 */
      }

      /* Improve contrast for muted text */
      .text-muted { color: #64748b !important; } /* slate-500/600 */
      .dark .text-muted { color: #cbd5e1 !important; } /* slate-300 */

      /* Inputs and selects: ensure readable text in both themes */
      .form-control, .form-select {
        color: rgba(15, 23, 42, 0.98) !important;
        background-color: #ffffff;
        -webkit-text-fill-color: currentColor; /* Chrome/WebKit: prevent invisible text */
        color-scheme: light; /* avoid UA auto-darkening conflicts in Chrome */
      }
      .form-control::placeholder { color: #475569; opacity: 1; }
      .dark .form-control::placeholder { color: #9ca3af; }
      .dark .form-control, .dark .form-select {
        color: rgba(226, 232, 240, 0.98) !important;
        background-color: #1f2937;
        border-color: #475569;
        -webkit-text-fill-color: currentColor; /* Chrome/WebKit */
        color-scheme: dark;
      }
      .form-select option { color: inherit; background-color: #ffffff; }
      .dark .form-select option { color: rgba(226, 232, 240, 0.98); background-color: #1f2937; }
      /* Ensure selected/hovered options remain legible */
      .form-select option:checked, .form-select option:hover {
        background-color: #e7f1ff !important; /* light blue */
        color: #0f172a !important; /* slate-900 */
      }
      .dark .form-select option:checked, .dark .form-select option:hover {
        background-color: #1e3a8a !important; /* indigo-800 */
        color: #e2e8f0 !important; /* slate-200 */
      }
      .form-control:focus, .form-select:focus { box-shadow: 0 0 0 .2rem rgba(13,110,253,.25); }

      /* Choices.js components (multi-select) contrast fixes */
  .choices__inner { color: rgba(15, 23, 42, 0.98); -webkit-text-fill-color: currentColor; }
      .choices__list--dropdown, .choices__list[aria-expanded] { color: rgba(15, 23, 42, 0.98); }
      .dark .choices__inner { background-color: #1f2937; color: rgba(226, 232, 240, 0.98); border-color: #475569; }
      .dark .choices__list--dropdown, .dark .choices__list[aria-expanded] { background-color: #111827; color: rgba(226, 232, 240, 0.98); }
      .choices__item--selectable.is-highlighted { background-color: rgba(13,110,253,.1); }
      .dark .choices__item--selectable.is-highlighted { background-color: rgba(13,110,253,.25); }

  /* Navbar chip (right ribbon) */
  .navbar-chip { display:inline-flex; align-items:center; gap:.45rem; padding:.25rem .5rem; border-radius:999px; background: rgba(255,255,255,.15); color:#fff; backdrop-filter: blur(6px); border: 1px solid rgba(255,255,255,.25); }
  .chip-icon { width: 18px; height: 18px; object-fit: contain; display:inline-block; filter: drop-shadow(0 1px 2px rgba(0,0,0,.25)); }
  .chip-text { font-weight:600; font-size:.85rem; letter-spacing:.25px; }
    </style>
  </head>
  <body class="bg-slate-50 dark:bg-slate-900 text-slate-800 dark:text-slate-200">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg,#6366f1,#8b5cf6); box-shadow: 0 10px 30px rgba(99,102,241,.35);">
      <div class="container-fluid px-3">
  <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index') }}">
          <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" class="bi bi-bricks me-2" viewBox="0 0 16 16">
            <path d="M0 2a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V2zm2 1v2h3V3H2zm5 0v2h3V3H7zm5 0v2h2V3h-2zM2 7v6h3V7H2zm5 0v6h3V7H7zm5 0v6h2V7h-2z"/>
          </svg>
          <span class="fw-semibold">SwayamAI</span>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav" aria-controls="mainNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="mainNav">
          <ul class="navbar-nav ms-auto mb-2 mb-lg-0 align-items-lg-center w-100">
            <li class="nav-item"><a class="nav-link" href="{{ url_for('index') }}">Home</a></li>
            <li class="nav-item"><a class="nav-link" href="{{ url_for('swayam_ml') }}">SwayamML</a></li>
            <li class="nav-item ms-2">
              <button id="darkModeToggle" class="btn btn-sm btn-light text-dark" title="Toggle dark mode" style="border-radius:999px;">
                <i id="darkIcon" class="bi bi-moon-stars-fill"></i>
              </button>
            </li>
          </ul>
        </div>
        <!-- Always-visible right ribbon chip -->
        <div class="ms-auto d-flex align-items-center">
          <span class="navbar-chip">
            <img src="{{ url_for('static', filename='img/community.png') }}?v={{ current_year }}" alt="Community" class="chip-icon"/>
            <span class="chip-text">Building Community</span>
          </span>
        </div>
      </div>
    </nav>

    <main class="container py-5">
      {% with messages = get_flashed_messages() %}
        {% if messages %}
          <div class="mb-4">
            {% for m in messages %}
              <div class="alert alert-info">{{ m }}</div>
            {% endfor %}
          </div>
        {% endif %}
      {% endwith %}

      {% block content %}{% endblock %}
    </main>

    <footer class="text-center text-muted py-4 small">
      <div class="container">© {{ current_year if current_year is defined else '' }} SwayamAI · Built with Flask · Bootstrap + Tailwind</div>
    </footer>

  <!-- Bootstrap 5 JS bundle -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Choices JS (CDN full) -->
  <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
  <!-- Local Choices script (bundled; acts as stub/fallback) -->
  <script src="{{ url_for('static', filename='vendor/choices/choices.min.js') }}"></script>
  <!-- Project UI helpers (dark mode toggle + client-side validation + select enhancement) -->
  <script src="{{ url_for('static', filename='js/ui.js') }}"></script>
  {% block scripts %}{% endblock %}
  </body>
</html>
