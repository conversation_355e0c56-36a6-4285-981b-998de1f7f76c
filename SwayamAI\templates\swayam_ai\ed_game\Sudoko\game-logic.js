/**
 * Game Logic - Core game state and rule management
 */

class GameLogic {
    constructor(solver) {
        this.solver = solver;
        this.originalPuzzle = null;
        this.currentGrid = null;
        this.solution = null;
        this.difficulty = 'medium';
        this.startTime = null;
        this.elapsedTime = 0;
        this.isPaused = false;
        this.timerInterval = null;
        this.errors = 0;
        this.maxErrors = 3;
        this.moveHistory = [];

        // Event callbacks
        this.onGameComplete = null;
        this.onGameOver = null;
        this.onGameUpdate = null;
    }

    startNewGame(puzzleData) {
        this.originalPuzzle = this.solver.copyGrid(puzzleData.puzzle);
        this.currentGrid = this.solver.copyGrid(puzzleData.puzzle);
        this.solution = puzzleData.solution;
        this.difficulty = puzzleData.difficulty;
        this.errors = 0;
        this.moveHistory = [];
        this.startTimer();

        console.log(`Game started: ${this.difficulty} with ${puzzleData.clues} clues`);
    }

    makeMove(row, col, value) {
        if (!this.currentGrid || this.isPuzzleComplete()) {
            return false;
        }

        // Don't allow moves on given cells
        if (this.originalPuzzle[row][col] !== 0) {
            return false;
        }

        // Store previous value for undo
        const previousValue = this.currentGrid[row][col];

        // Make the move
        this.currentGrid[row][col] = value;

        // Record move in history
        this.moveHistory.push({
            row, col, 
            previousValue, 
            newValue: value,
            timestamp: Date.now()
        });

        // Check if move is correct
        const isCorrect = value === 0 || value === this.solution[row][col];

        if (!isCorrect && value !== 0) {
            this.errors++;

            if (this.errors >= this.maxErrors) {
                this.pauseTimer();
                if (this.onGameOver) {
                    this.onGameOver();
                }
                return false;
            }
        }

        // Check for completion
        if (this.isPuzzleComplete()) {
            this.pauseTimer();
            if (this.onGameComplete) {
                this.onGameComplete();
            }
        }

        // Trigger update
        if (this.onGameUpdate) {
            this.onGameUpdate();
        }

        return { isCorrect, gameComplete: this.isPuzzleComplete() };
    }

    isPuzzleComplete() {
        if (!this.currentGrid) return false;

        // Check if all cells are filled
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.currentGrid[row][col] === 0) {
                    return false;
                }
            }
        }

        // Validate solution
        return this.solver.isValidSolution(this.currentGrid);
    }

    isValidMove(row, col, value) {
        if (value === 0) return true; // Erasing is always valid
        return this.solver.isValidMove(this.currentGrid, row, col, value);
    }

    getHint() {
        if (!this.currentGrid) return null;
        return this.solver.getHint(this.currentGrid);
    }

    solvePuzzle(solution) {
        if (!this.currentGrid) return;

        this.currentGrid = this.solver.copyGrid(solution);
        this.pauseTimer();
    }

    resetPuzzle() {
        if (!this.originalPuzzle) return;

        this.currentGrid = this.solver.copyGrid(this.originalPuzzle);
        this.errors = 0;
        this.moveHistory = [];
        this.startTimer();

        if (this.onGameUpdate) {
            this.onGameUpdate();
        }
    }

    startTimer() {
        this.startTime = Date.now();
        this.elapsedTime = 0;
        this.isPaused = false;

        this.timerInterval = setInterval(() => {
            if (!this.isPaused) {
                this.elapsedTime = Date.now() - this.startTime;
                if (this.onGameUpdate) {
                    this.onGameUpdate();
                }
            }
        }, 1000);
    }

    pauseTimer() {
        this.isPaused = true;
    }

    resumeTimer() {
        if (this.isPaused && this.startTime) {
            this.isPaused = false;
            this.startTime = Date.now() - this.elapsedTime;
        }
    }

    getTimeString() {
        const totalSeconds = Math.floor(this.elapsedTime / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    getElapsedTime() {
        return this.elapsedTime;
    }

    getErrorCount() {
        return this.errors;
    }

    getMaxErrors() {
        return this.maxErrors;
    }

    getDifficulty() {
        return this.difficulty;
    }

    getCurrentGrid() {
        return this.currentGrid;
    }

    hasProgress() {
        if (!this.originalPuzzle || !this.currentGrid) return false;

        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.originalPuzzle[row][col] !== this.currentGrid[row][col]) {
                    return true;
                }
            }
        }
        return false;
    }

    getGameState() {
        return {
            currentGrid: this.currentGrid,
            originalPuzzle: this.originalPuzzle,
            solution: this.solution,
            difficulty: this.difficulty,
            elapsedTime: this.elapsedTime,
            errors: this.errors,
            moveHistory: this.moveHistory,
            startTime: this.startTime
        };
    }

    loadGameState(gameState, puzzleData) {
        this.currentGrid = gameState.currentGrid;
        this.originalPuzzle = gameState.originalPuzzle;
        this.solution = gameState.solution || puzzleData.solution;
        this.difficulty = gameState.difficulty;
        this.elapsedTime = gameState.elapsedTime || 0;
        this.errors = gameState.errors || 0;
        this.moveHistory = gameState.moveHistory || [];

        // Resume timer if game is not complete
        if (!this.isPuzzleComplete()) {
            this.startTime = Date.now() - this.elapsedTime;
            this.startTimer();
        }
    }
}

if (typeof window !== 'undefined') {
    window.GameLogic = GameLogic;
}