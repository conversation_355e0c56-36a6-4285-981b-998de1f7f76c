import os
import pandas as pd
import joblib
from sklearn.preprocessing import LabelEncoder
import logging

logging.getLogger(__name__)

class LabelTransform:
    output_folder = None

    def __init__(self) -> None:
        self.label_encoder = LabelEncoder()

    def set_output_folder(self, output_folder):
        self.output_folder = output_folder
        if self.output_folder and not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder, exist_ok=True)

    def encode_labels(self, df: pd.DataFrame, label_col: str, label_pkl_file='label_encoder.pkl'):
        """Fit label encoder on df[label_col] and persist encoder.

        Adds column 'label_col_encoded' (kept for backward compatibility).
        """
        df = df.copy()
        df["label_col_encoded"] = self.label_encoder.fit_transform(df[label_col])
        if self.output_folder:
            output_pkl_file = os.path.join(self.output_folder, label_pkl_file)
        else:
            output_pkl_file = label_pkl_file
        joblib.dump(self.label_encoder, output_pkl_file)
        return df

    def encode_labels_test_data(self, df, label_col):
        df = df.copy()
        df["label_col_encoded"] = self.label_encoder.transform(df[label_col])
        return df

    def decode_labels(self, df, label_col, label_pkl_file):
        # Accept either full path or filename relative to current working dir
        loaded_label_encoder = joblib.load(label_pkl_file)
        df = df.copy()
        df["label_col_decoded"] = loaded_label_encoder.inverse_transform(df[label_col])
        return df