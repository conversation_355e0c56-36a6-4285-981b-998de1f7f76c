{% extends "base.html" %}

{% block title %}SwayamCourses · SwayamAI{% endblock %}

{% block content %}
<section class="sa-hero p-4 p-md-5 mb-4">
  <h1 class="h4 fw-semibold mb-2">SwayamCourses</h1>
  <p class="mb-0 text-muted">Curated learning paths and bite-sized tracks to level up your AI/ML skills. Start with a track, follow the outline, and practice with included datasets.</p>
</section>

<section class="mb-5">
  <div class="d-flex align-items-center justify-content-between mb-3">
  <h2 class="h6 mb-0">Featured Tracks</h2>
  <a class="btn btn-sm sa-btn-primary" href="{{ url_for('swayam_ml') }}">Practice in SwayamML</a>
  </div>
  <div class="grid">
    <div class="card-link">
      <div class="fw-semibold">Intro to Machine Learning</div>
      <div class="small text-muted">Supervised vs. unsupervised, data prep, model selection, evaluation.</div>
      <div class="mt-2"><a href="#" class="small">View outline</a></div>
    </div>
    <div class="card-link">
      <div class="fw-semibold">Practical NLP</div>
      <div class="small text-muted">Text cleaning, embeddings, classification, similarity, and sentiment.</div>
      <div class="mt-2"><a href="#" class="small">View outline</a></div>
    </div>
    <div class="card-link">
      <div class="fw-semibold">Data Visualization Essentials</div>
      <div class="small text-muted">EDA, feature understanding, chart literacy, and storytelling.</div>
      <div class="mt-2"><a href="#" class="small">View outline</a></div>
    </div>
    <div class="card-link">
      <div class="fw-semibold">Time Series 101</div>
      <div class="small text-muted">Decomposition, forecasting basics, seasonality, and evaluation.</div>
      <div class="mt-2"><a href="#" class="small">View outline</a></div>
    </div>
    <div class="card-link">
      <div class="fw-semibold">MLOps Basics</div>
      <div class="small text-muted">Artifacts, metrics, packaging, and simple deployment workflows.</div>
      <div class="mt-2"><a href="#" class="small">View outline</a></div>
    </div>
  </div>
</section>

<section class="mb-5">
  <h2 class="h6 mb-3">Browse by level</h2>
  <div class="row g-3 g-md-4">
    <div class="col-12 col-md-4">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);">
        <div class="fw-semibold mb-2">Beginner</div>
        <ul class="small text-muted mb-0">
          <li>Python for Data</li>
          <li>Intro to ML</li>
          <li>Data Cleaning</li>
        </ul>
      </div>
    </div>
    <div class="col-12 col-md-4">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);">
        <div class="fw-semibold mb-2">Intermediate</div>
        <ul class="small text-muted mb-0">
          <li>Feature Engineering</li>
          <li>Model Selection</li>
          <li>Practical NLP</li>
        </ul>
      </div>
    </div>
    <div class="col-12 col-md-4">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);">
        <div class="fw-semibold mb-2">Advanced</div>
        <ul class="small text-muted mb-0">
          <li>Vector Search & RAG</li>
          <li>Agents & Tool Use</li>
          <li>Deployment Patterns</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<style>
  .grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(260px, 1fr)); gap: 16px; }
  .card-link { text-decoration: none; color: inherit; background: white; border-radius: 16px; padding: 18px; border: 1px solid rgba(0,0,0,.05); box-shadow: 0 4px 14px rgba(2,6,23,.06); }
  .dark .card-link { background: #0f172a; border-color: #334155; }
</style>
{% endblock %}
