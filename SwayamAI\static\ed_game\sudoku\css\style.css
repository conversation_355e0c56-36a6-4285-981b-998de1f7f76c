/* Sudoku embedded styles: copied from collateral with minimal tweaks for base template */
/* Responsive variables */
body.sudoku-page{ --grid-max: 560px; --grid-vw: 42vw; --grid-vh: calc(100vh - 300px); --grid-size: min(var(--grid-max), var(--grid-vw), var(--grid-vh)); --cell-gap: 2px; --cell-font: clamp(14px, 2.2vw, 22px); --btn-size: clamp(40px, 7vw, 56px); --btn-font: clamp(14px, 2.2vw, 20px); --sidebar-w: clamp(140px, 18vw, 200px); }
/* Reset and Base Styles */
* { margin: 0; padding: 0; box-sizing: border-box; }
body.sudoku-page { font-family: 'Arial', sans-serif; background: linear-gradient(135deg, var(--accent, #667eea) 0%, var(--accent2, #764ba2) 100%); color: #333; min-height: 100vh; }
.app-container { max-width: 1200px; margin: 0 auto; padding: 20px; min-height: 100vh; position: relative; }

/* New horizontal layout for stats and difficulty */
.top-content-section {
  background: linear-gradient(135deg, rgba(15,23,42,0.95), rgba(30,41,59,0.95));
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(56,189,248,0.2);
}

.stats-and-difficulty-row {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  margin-bottom: 16px;
  justify-content: space-between;
}

.stats-block {
  flex: 0 0 auto;
  min-width: 300px;
  padding-left: 20px; /* Left indented */
}

.difficulty-block {
  flex: 0 0 auto;
  padding-right: 20px; /* Right indented */
}

.section-heading {
  font-size: 1.2rem;
  font-weight: bold;
  color: #f8fafc;
  margin-bottom: 12px;
  text-shadow: 0 1px 2px rgba(0,0,0,.3);
}

/* Game Info Section */
.game-info-section {
  background: rgba(255,255,255,.95);
  border-radius: 15px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0,0,0,.1);
  backdrop-filter: blur(10px);
}

.game-info-block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.info-sections {
  display: flex;
  gap: 24px;
  align-items: center;
}

.game-info-block .actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.info-group {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0,0,0,.05);
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid rgba(0,0,0,.1);
}

.info-group .label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

.info-group span:last-child {
  font-weight: bold;
  color: #2d3748;
  font-size: 1rem;
  min-width: 45px;
  text-align: center;
}

.stats-cards {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.stats-card {
  background: #0f172a;
  border: 2px solid #1f2937;
  border-radius: 8px;
  padding: 12px 16px;
  text-align: center;
  min-width: 80px;
  box-shadow: 0 2px 4px rgba(0,0,0,.3);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,.4);
  background: #111e33;
  border-color: #38bdf8;
}

.stats-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: #f8fafc;
}

.stats-label {
  font-size: 0.7rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
}

.difficulty-cards {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.difficulty-card {
  background: #0f172a;
  border: 2px solid #1f2937;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.difficulty-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0,0,0,.3);
  background: #111e33;
  border-color: #38bdf8;
}

.difficulty-card.active {
  border-color: #38bdf8;
  box-shadow: 0 0 0 2px rgba(56,189,248,.3);
  background: #111e33;
}

.diff-title {
  font-size: 1rem;
  font-weight: 800;
  color: #e2e8f0;
  margin-bottom: 4px;
}

.diff-sub {
  font-weight: 600;
  color: #7dd3fc;
  margin-bottom: 2px;
  font-size: 0.85rem;
}

.diff-note {
  color: #94a3b8;
  font-size: 0.75rem;
}

.btn-sm {
  padding: 6px 10px;
  font-size: 11px;
}

#reset-stats-btn {
  background: #1f2937;
  color: #94a3b8;
  border: 1px solid #374151;
}

#reset-stats-btn:hover {
  background: #374151;
  color: #f8fafc;
  border-color: #38bdf8;
}

.hidden {
  display: none;
}

.game-instructions {
  text-align: center;
  color: #64748b;
  font-size: 14px;
  margin: 0;
}

/* Game layout - horizontal grid and number selector */
.game-layout {
  background: rgba(255,255,255,.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,.1);
  backdrop-filter: blur(10px);
}

.game-container {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  justify-content: center;
}

.grid-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: var(--grid-size);
  position: relative;
}

/* Number selector panel - now as a fixed side panel */
.number-selector-panel {
  background: rgba(255,255,255,0.98);
  border: 2px solid #667eea;
  border-radius: 12px;
  padding: 16px;
  width: 200px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  backdrop-filter: blur(8px);
}

.selector-title {
  font-size: 1rem;
  font-weight: bold;
  color: #4a5568;
  margin-bottom: 12px;
  text-align: center;
}

.popup-numbers {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.header { display: flex; justify-content: space-between; align-items: center; background: rgba(255,255,255,.95); padding: 20px; border-radius: 15px; margin-bottom: 20px; box-shadow: 0 8px 32px rgba(0,0,0,.1); backdrop-filter: blur(10px); }
.app-title { font-size: 2rem; font-weight: bold; color: #4a5568; }
.app-title .title-link{ color:#4a5568; text-decoration:none; }
.app-title .title-link:hover{ text-decoration:underline; }
.header-controls { display: flex; gap: 16px; align-items: center; }
.header-controls .actions{ display:flex; gap:8px; align-items: center; }
.btn { padding: 8px 12px; border-radius: 10px; cursor: pointer; font-size: 12px; font-weight: 700; transition: all .25s ease; letter-spacing: .4px; border: 2px solid transparent; background: #f8fafc; color: #334155; box-shadow: 0 2px 0 rgba(0,0,0,.06) inset, 0 1px 0 rgba(255,255,255,.6) inset; }
.btn:hover { transform: translateY(-1px); }
.btn-primary { background: linear-gradient(135deg, #5c6cf2, #7358d2); color: #fff; border: none; box-shadow: 0 6px 18px rgba(92,108,242,.35); }
.btn-primary:hover { box-shadow: 0 8px 22px rgba(92,108,242,.45); }
.btn-secondary { background: #f8fbff; color: #334155; border: 2px solid #e2e8f0; }
.btn-secondary:hover { background: #f1f5f9; }
.btn:disabled { opacity: .5; cursor: not-allowed; transform: none; }

/* Number popup */
.number-popup {
  position: absolute;
  background: rgba(255,255,255,0.98);
  border: 2px solid #667eea;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.25);
  padding: 8px;
  z-index: 100;
  backdrop-filter: blur(8px);
  transform: scale(0.8);
  transition: all 0.2s ease;
}
.number-popup.hidden {
  opacity: 0;
  pointer-events: none;
  transform: scale(0.6);
}
.number-popup:not(.hidden) {
  opacity: 1;
  transform: scale(1);
}
.popup-numbers {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 4px;
}
.popup-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #e2e8f0;
  background: #fff;
  color: #4a5568;
  font-size: 14px;
  font-weight: bold;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-btn:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}
.popup-btn.erase-btn {
  background: #fed7d7;
  color: #c53030;
  border-color: #feb2b2;
}
.sudoku-grid { display: grid; grid-template-columns: repeat(9, 1fr); grid-template-rows: repeat(9, 1fr); gap: 0; background: #2d3748; padding: 12px; border-radius: 15px; box-shadow: 0 10px 40px rgba(0,0,0,.2); position: relative; width: var(--grid-size); height: var(--grid-size); }
.sudoku-grid::before, .sudoku-grid::after { content: none; }
.grid-cell { background: #fff; display: flex; align-items: center; justify-content: center; font-size: var(--cell-font); font-weight: bold; cursor: pointer; transition: all .2s ease; position: relative; z-index: 5; aspect-ratio: 1 / 1; border: 0; border-right: 1px solid #e2e8f0; border-bottom: 1px solid #e2e8f0; }
/* Thin outer borders */
.grid-cell:nth-child(-n+9) { border-top: 1px solid #e2e8f0; }
.grid-cell:nth-child(9n+1) { border-left: 1px solid #e2e8f0; }
/* Vertical thick lines at col 4 and 7; avoid double border on previous cols */
.grid-cell:nth-child(9n+3),
.grid-cell:nth-child(9n+6) { border-right: 0; }
.grid-cell:nth-child(9n+4),
.grid-cell:nth-child(9n+7) { border-left: 3px solid var(--accent, #667eea); }
/* Horizontal thick lines at row 4 (cells 28-36) and row 7 (55-63); avoid double on previous rows */
.grid-cell:nth-child(n+19):nth-child(-n+27) { border-bottom: 0; }
.grid-cell:nth-child(n+46):nth-child(-n+54) { border-bottom: 0; }
.grid-cell:nth-child(n+28):nth-child(-n+36),
.grid-cell:nth-child(n+55):nth-child(-n+63) { border-top: 3px solid var(--accent, #667eea); }
.grid-cell:hover { background: #f7fafc; transform: scale(1.05); }
.grid-cell.selected { background: #bee3f8; border-color: #3182ce; box-shadow: 0 0 0 2px rgba(49,130,206,.5); }
.grid-cell.given { background: #f0f9ff; color: #1e40af; font-weight: 900; cursor: default; }
.grid-cell.error { background: #fed7d7; color: #c53030; }
.grid-cell.correct { background: #d4edda; color: #155724; }
.grid-cell.hint { background: #fff3cd; color: #856404; }
.grid-cell.highlighted { background: #f1f5f9; }
.grid-cell.same-number { outline: 2px solid rgba(102,126,234,.5); }
/* Remove old number panel styles since we're using popup now */
.modal { position: fixed; top:0; left:0; width:100%; height:100%; background: rgba(0,0,0,.8); display:flex; align-items:center; justify-content:center; z-index:1000; opacity:1; transition: opacity .3s ease; }
.modal.hidden{ opacity:0; pointer-events:none; }
.modal-content{ background:#fff; padding:32px; border-radius:16px; text-align:center; max-width:400px; width:90%; box-shadow:0 20px 60px rgba(0,0,0,.3); }
.loading { position: fixed; top:0; left:0; width:100%; height:100%; background: rgba(255,255,255,.9); display:flex; flex-direction:column; align-items:center; justify-content:center; z-index:2000; }
.loading.hidden{ display:none; }
.loading-spinner{ width:50px; height:50px; border:5px solid #e2e8f0; border-top:5px solid #667eea; border-radius:50%; animation: spin 1s linear infinite; margin-bottom: 16px; }
@keyframes spin{ from{transform:rotate(0)} to{transform:rotate(360deg)} }
/* Small screens: allow wrap if needed */
@media (max-width: 640px){
	.game-container{ flex-direction: column; align-items: center; }
	.grid-section{ order: 1; }
	.number-selector-panel { width: 100%; max-width: 300px; }
}
@media (max-width: 900px){
	.sudoku-layout{ grid-template-columns: 1fr; }
	.right-pane{ position: static; }
	.stats-and-difficulty-row {
		flex-direction: column;
		gap: 20px;
	}
	.stats-block {
		min-width: auto;
	}
}
@media (max-width: 768px) {
	.difficulty-cards {
		justify-content: center;
	}
	.stats-cards {
		justify-content: center;
	}
}
/* Legacy stats section - removed, using new layout above */

/* Top horizontal statistics */
.top-stats-section{ background: rgba(255,255,255,.95); border-radius:15px; padding:16px 20px; margin: 0 0 20px; box-shadow: 0 8px 32px rgba(0,0,0,.1); backdrop-filter: blur(10px); }
.top-stats-section .stats-heading{ text-align:left; font-size:1.1rem; color:#4a5568; margin-bottom: 12px; font-weight: 800; letter-spacing:.2px; }
.top-stats-cards{ display:flex; align-items:center; gap: 20px; flex-wrap: wrap; }
.top-stats-cards .stats-card{ background:#f8fafc; border: 1px solid #e2e8f0; border-radius:10px; padding:12px 16px; text-align:center; box-shadow: 0 2px 8px rgba(0,0,0,.05); min-width: 120px; }
.top-stats-cards .stats-value{ font-size: 1.4rem; font-weight: 900; color:#334155; }
.top-stats-cards .stats-label{ font-size: .75rem; font-weight:700; color:#64748b; margin-top:2px; letter-spacing:.4px; }
.reset-stats-top{ margin-left: auto; }

/* Pause state overlay */
#pause-overlay{ position:absolute; left:0; top:0; right:0; bottom:0; display:none; align-items:center; justify-content:center; background: rgba(2,6,23,.55); z-index: 10; border-radius: 16px; }
.game-container.is-paused .sudoku-grid,
.game-container.is-paused .number-panel { pointer-events: none; filter: grayscale(.2) brightness(.9); }
.game-container.is-paused #pause-overlay{ display:flex; }
#pause-overlay .pause-box{ background: #0b1220; border:1px solid #1f2937; color:#e2e8f0; padding: 18px 22px; border-radius: 12px; box-shadow: 0 10px 32px rgba(0,0,0,.35); font-weight: 800; letter-spacing:.5px; }

/* Dark theme removed per request */
