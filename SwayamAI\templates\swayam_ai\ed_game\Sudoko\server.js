#!/usr/bin/env node

/**
 * Simple HTTP Server for Local Development
 * Serves the Sudoku app on localhost with proper CORS headers
 * Usage: node server.js [port]
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

class SudokuServer {
    constructor(port = 8000) {
        this.port = port;
        this.publicDir = path.join(__dirname, '..');

        // MIME types for different file extensions
        this.mimeTypes = {
            '.html': 'text/html',
            '.css': 'text/css',
            '.js': 'application/javascript',
            '.json': 'application/json',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.ico': 'image/x-icon',
            '.mp3': 'audio/mpeg',
            '.wav': 'audio/wav',
            '.ogg': 'audio/ogg'
        };
    }

    start() {
        const server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });

        server.listen(this.port, 'localhost', () => {
            console.log('🚀 Sudoku Server Started');
            console.log(`📍 Local: http://localhost:${this.port}`);
            console.log(`📁 Serving: ${this.publicDir}`);
            console.log('🛑 Press Ctrl+C to stop');
        });

        // Graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Shutting down server...');
            server.close(() => {
                console.log('✅ Server stopped');
                process.exit(0);
            });
        });

        return server;
    }

    handleRequest(req, res) {
        const parsedUrl = url.parse(req.url, true);
        let pathname = parsedUrl.pathname;

        // Default to index.html for root requests
        if (pathname === '/') {
            pathname = '/index.html';
        }

        const filePath = path.join(this.publicDir, pathname);
        const extname = path.extname(filePath);

        // Security check: ensure file is within public directory
        if (!filePath.startsWith(this.publicDir)) {
            this.sendError(res, 403, 'Forbidden');
            return;
        }

        // Check if file exists
        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                this.sendError(res, 404, 'File not found');
                return;
            }

            // Read and serve file
            fs.readFile(filePath, (err, data) => {
                if (err) {
                    this.sendError(res, 500, 'Internal server error');
                    return;
                }

                const mimeType = this.mimeTypes[extname] || 'application/octet-stream';

                // Set headers
                res.writeHead(200, {
                    'Content-Type': mimeType,
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Cache-Control': extname === '.html' ? 'no-cache' : 'max-age=3600'
                });

                res.end(data);

                // Log request
                console.log(`${new Date().toISOString()} - ${req.method} ${pathname} - 200`);
            });
        });
    }

    sendError(res, statusCode, message) {
        res.writeHead(statusCode, { 'Content-Type': 'text/plain' });
        res.end(message);
        console.log(`${new Date().toISOString()} - ERROR ${statusCode}: ${message}`);
    }
}

// Start server if run directly
if (require.main === module) {
    const port = parseInt(process.argv[2]) || 8000;
    const server = new SudokuServer(port);
    server.start();
}

module.exports = SudokuServer;