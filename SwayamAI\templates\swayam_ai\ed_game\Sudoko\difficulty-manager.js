/**
 * Difficulty Manager - Manages puzzle difficulty and loading
 */

class DifficultyManager {
    constructor() {
        this.puzzleDatabase = {
            easy: [],
            medium: [],
            hard: []
        };
        this.currentIndices = {
            easy: 0,
            medium: 0,
            hard: 0
        };
        this.isInitialized = false;
    }

    async init() {
        console.log('🎯 Initializing Difficulty Manager...');

        try {
            // Try to load puzzle data from files
            await this.loadPuzzleDatabase();

            // If no data loaded, generate sample puzzles
            if (!this.hasValidDatabase()) {
                console.log('📝 No puzzle database found, generating sample puzzles...');
                await this.generateSamplePuzzles();
            }

            this.isInitialized = true;
            console.log('✅ Difficulty Manager initialized');

        } catch (error) {
            console.error('❌ Failed to initialize Difficulty Manager:', error);
            // Generate minimal fallback puzzles
            this.generateFallbackPuzzles();
            this.isInitialized = true;
        }
    }

    async loadPuzzleDatabase() {
        const difficulties = ['easy', 'medium', 'hard'];

        for (const difficulty of difficulties) {
            try {
                const response = await fetch(`data/puzzles/${difficulty}.json`);
                if (response.ok) {
                    const data = await response.json();
                    this.puzzleDatabase[difficulty] = data.puzzles || [];
                    console.log(`📚 Loaded ${this.puzzleDatabase[difficulty].length} ${difficulty} puzzles`);
                }
            } catch (error) {
                console.warn(`⚠️ Could not load ${difficulty} puzzles:`, error);
            }
        }
    }

    hasValidDatabase() {
        return Object.values(this.puzzleDatabase).some(puzzles => puzzles.length > 0);
    }

    async generateSamplePuzzles() {
        // Generate a small set of sample puzzles for each difficulty
        const generator = new SudokuGenerator();

        const sampleCounts = { easy: 20, medium: 20, hard: 15 };

        for (const [difficulty, count] of Object.entries(sampleCounts)) {
            console.log(`🔧 Generating ${count} sample ${difficulty} puzzles...`);

            for (let i = 0; i < count; i++) {
                try {
                    const puzzle = generator.generatePuzzle(difficulty);
                    this.puzzleDatabase[difficulty].push(puzzle);
                } catch (error) {
                    console.warn(`Failed to generate ${difficulty} puzzle ${i + 1}:`, error);
                }
            }

            console.log(`✅ Generated ${this.puzzleDatabase[difficulty].length} ${difficulty} puzzles`);
        }
    }

    generateFallbackPuzzles() {
        console.log('🆘 Generating minimal fallback puzzles...');

        // Create very basic hardcoded puzzles as absolute fallback
        const fallbackPuzzles = {
            easy: [
                {
                    puzzle: [
                        [5,3,0,0,7,0,0,0,0],
                        [6,0,0,1,9,5,0,0,0],
                        [0,9,8,0,0,0,0,6,0],
                        [8,0,0,0,6,0,0,0,3],
                        [4,0,0,8,0,3,0,0,1],
                        [7,0,0,0,2,0,0,0,6],
                        [0,6,0,0,0,0,2,8,0],
                        [0,0,0,4,1,9,0,0,5],
                        [0,0,0,0,8,0,0,7,9]
                    ],
                    solution: [
                        [5,3,4,6,7,8,9,1,2],
                        [6,7,2,1,9,5,3,4,8],
                        [1,9,8,3,4,2,5,6,7],
                        [8,5,9,7,6,1,4,2,3],
                        [4,2,6,8,5,3,7,9,1],
                        [7,1,3,9,2,4,8,5,6],
                        [9,6,1,5,3,7,2,8,4],
                        [2,8,7,4,1,9,6,3,5],
                        [3,4,5,2,8,6,1,7,9]
                    ],
                    difficulty: 'easy',
                    clues: 42,
                    id: 'fallback-easy-1'
                }
            ],
            medium: [
                {
                    puzzle: [
                        [0,0,0,6,0,0,4,0,0],
                        [7,0,0,0,0,3,6,0,0],
                        [0,0,0,0,9,1,0,8,0],
                        [0,0,0,0,0,0,0,0,0],
                        [0,5,0,1,8,0,0,0,3],
                        [0,0,0,3,0,6,0,4,5],
                        [0,4,0,2,0,0,0,6,0],
                        [9,0,3,0,0,0,0,0,0],
                        [0,2,0,0,0,0,1,0,0]
                    ],
                    solution: [
                        [1,3,2,6,7,8,4,5,9],
                        [7,8,4,5,2,3,6,9,1],
                        [5,6,9,4,9,1,3,8,2],
                        [4,9,6,8,5,7,2,1,3],
                        [2,5,7,1,8,9,6,3,4],
                        [8,1,3,3,4,6,9,7,5],
                        [3,4,1,2,3,5,7,6,8],
                        [9,7,3,6,1,4,5,2,1],
                        [6,2,5,7,6,2,1,4,3]
                    ],
                    difficulty: 'medium',
                    clues: 28,
                    id: 'fallback-medium-1'
                }
            ],
            hard: [
                {
                    puzzle: [
                        [0,0,0,0,0,0,0,1,0],
                        [4,0,0,0,0,0,0,0,0],
                        [0,2,0,0,0,0,0,0,0],
                        [0,0,0,0,5,0,4,0,7],
                        [0,0,8,0,0,0,3,0,0],
                        [0,0,1,0,9,0,0,0,0],
                        [3,0,0,4,0,0,2,0,0],
                        [0,5,0,1,0,0,0,0,0],
                        [0,0,0,8,0,6,0,0,0]
                    ],
                    solution: [
                        [6,9,3,7,8,4,5,1,2],
                        [4,8,7,5,1,2,9,3,6],
                        [1,2,5,9,6,3,8,7,4],
                        [9,3,2,6,5,1,4,8,7],
                        [5,6,8,2,4,7,3,9,1],
                        [7,4,1,3,9,8,6,2,5],
                        [3,1,9,4,7,5,2,6,8],
                        [8,5,6,1,2,9,7,4,3],
                        [2,7,4,8,3,6,1,5,9]
                    ],
                    difficulty: 'hard',
                    clues: 24,
                    id: 'fallback-hard-1'
                }
            ]
        };

        this.puzzleDatabase = fallbackPuzzles;
        console.log('🆘 Fallback puzzles ready');
    }

    getPuzzle(difficulty = 'medium') {
        if (!this.isInitialized) {
            throw new Error('Difficulty Manager not initialized');
        }

        const puzzles = this.puzzleDatabase[difficulty];
        if (!puzzles || puzzles.length === 0) {
            throw new Error(`No ${difficulty} puzzles available`);
        }

        // Get current puzzle and increment index
        const currentIndex = this.currentIndices[difficulty];
        const puzzle = puzzles[currentIndex];

        // Move to next puzzle (wrap around if at end)
        this.currentIndices[difficulty] = (currentIndex + 1) % puzzles.length;

        return puzzle;
    }

    getRandomPuzzle(difficulty = 'medium') {
        if (!this.isInitialized) {
            throw new Error('Difficulty Manager not initialized');
        }

        const puzzles = this.puzzleDatabase[difficulty];
        if (!puzzles || puzzles.length === 0) {
            throw new Error(`No ${difficulty} puzzles available`);
        }

        const randomIndex = Math.floor(Math.random() * puzzles.length);
        return puzzles[randomIndex];
    }

    getPuzzleCount(difficulty) {
        return this.puzzleDatabase[difficulty]?.length || 0;
    }

    getTotalPuzzleCount() {
        return Object.values(this.puzzleDatabase).reduce((total, puzzles) => total + puzzles.length, 0);
    }

    getDifficultyStats() {
        const stats = {};

        for (const [difficulty, puzzles] of Object.entries(this.puzzleDatabase)) {
            if (puzzles.length > 0) {
                const clues = puzzles.map(p => p.clues);
                stats[difficulty] = {
                    count: puzzles.length,
                    minClues: Math.min(...clues),
                    maxClues: Math.max(...clues),
                    avgClues: (clues.reduce((a, b) => a + b, 0) / clues.length).toFixed(1)
                };
            }
        }

        return stats;
    }

    validatePuzzle(puzzle) {
        const required = ['puzzle', 'solution', 'difficulty', 'clues'];
        return required.every(field => puzzle.hasOwnProperty(field));
    }

    addCustomPuzzle(puzzle, difficulty) {
        if (!this.validatePuzzle(puzzle)) {
            throw new Error('Invalid puzzle format');
        }

        puzzle.difficulty = difficulty;
        puzzle.id = puzzle.id || `custom-${Date.now()}`;

        this.puzzleDatabase[difficulty].push(puzzle);
        console.log(`Added custom ${difficulty} puzzle: ${puzzle.id}`);
    }

    reset() {
        this.currentIndices = {
            easy: 0,
            medium: 0,
            hard: 0
        };
    }
}

if (typeof window !== 'undefined') {
    window.DifficultyManager = DifficultyManager;
}