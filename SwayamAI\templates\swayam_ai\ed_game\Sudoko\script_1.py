# Create the main HTML structure for the Sudoku app
html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sudoku - Open Source Puzzle Game</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/themes.css">
    <meta name="description" content="Free open-source Sudoku game with 1000+ puzzles and 3 difficulty levels">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <h1 class="app-title">Sudoku</h1>
            <div class="header-controls">
                <button id="new-game-btn" class="btn btn-primary">New Game</button>
                <button id="solve-btn" class="btn btn-secondary">Solve</button>
                <button id="hint-btn" class="btn btn-secondary">Hint</button>
                <button id="reset-btn" class="btn btn-secondary">Reset</button>
            </div>
        </header>

        <!-- Game Info Panel -->
        <div class="game-info">
            <div class="info-item">
                <label>Difficulty:</label>
                <select id="difficulty-select">
                    <option value="easy">Easy</option>
                    <option value="medium">Medium</option>
                    <option value="hard">Hard</option>
                </select>
            </div>
            <div class="info-item">
                <label>Timer:</label>
                <span id="timer">00:00</span>
            </div>
            <div class="info-item">
                <label>Errors:</label>
                <span id="error-count">0/3</span>
            </div>
        </div>

        <!-- Main Game Area -->
        <main class="game-container">
            <!-- Sudoku Grid -->
            <div class="sudoku-grid" id="sudoku-grid">
                <!-- Grid cells will be generated by JavaScript -->
            </div>

            <!-- Number Input Panel -->
            <div class="number-panel">
                <button class="number-btn" data-number="1">1</button>
                <button class="number-btn" data-number="2">2</button>
                <button class="number-btn" data-number="3">3</button>
                <button class="number-btn" data-number="4">4</button>
                <button class="number-btn" data-number="5">5</button>
                <button class="number-btn" data-number="6">6</button>
                <button class="number-btn" data-number="7">7</button>
                <button class="number-btn" data-number="8">8</button>
                <button class="number-btn" data-number="9">9</button>
                <button class="number-btn erase-btn" data-number="0">⌫</button>
            </div>
        </main>

        <!-- Game Status Modal -->
        <div id="game-modal" class="modal hidden">
            <div class="modal-content">
                <h2 id="modal-title">Congratulations!</h2>
                <p id="modal-message">You solved the puzzle!</p>
                <div class="modal-stats">
                    <div>Time: <span id="final-time">00:00</span></div>
                    <div>Errors: <span id="final-errors">0</span></div>
                </div>
                <div class="modal-actions">
                    <button id="new-game-modal-btn" class="btn btn-primary">New Game</button>
                    <button id="close-modal-btn" class="btn btn-secondary">Close</button>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loading" class="loading hidden">
            <div class="loading-spinner"></div>
            <p>Loading puzzle...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/sudoku-solver.js"></script>
    <script src="js/sudoku-generator.js"></script>
    <script src="js/difficulty-manager.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/game-logic.js"></script>
    <script src="js/ui-controller.js"></script>
    <script src="js/main.js"></script>
</body>
</html>'''

# Save HTML file
with open('index.html', 'w', encoding='utf-8') as f:
    f.write(html_content)

print("✅ Created index.html")
print("📝 Features included:")
print("   - Responsive header with game controls")
print("   - Game info panel with difficulty, timer, and error tracking")
print("   - 9x9 Sudoku grid (generated by JavaScript)")
print("   - Number input panel with erase function")
print("   - Modal for game completion")
print("   - Loading indicator")
print("   - Proper script loading order")