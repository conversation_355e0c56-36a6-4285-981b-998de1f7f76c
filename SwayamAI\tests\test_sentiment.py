import os
import sys
import subprocess
import pandas as pd

from sentiment_analysis import sentiment_analysis_process


def make_sentiment_df(n=40):
    texts = []
    for i in range(n):
        if i % 2 == 0:
            texts.append(f"I like this {i}")
        else:
            texts.append(f"I dislike this {i}")
    return pd.DataFrame({'text': texts})


def test_sentiment_fast_general(tmp_path, tmp_output_dir, patch_transformers):
    df = make_sentiment_df()
    inp = tmp_path / 's.csv'
    df.to_csv(inp, index=False)

    summary, files, n_models = sentiment_analysis_process(
        str(inp), text_column='text', output_folder=str(tmp_output_dir), profile='fast_general', neutral_band=True
    )

    assert hasattr(summary, 'columns') and not summary.empty
    # outputs
    assert (tmp_output_dir / 'sentiments.csv').exists()
    assert (tmp_output_dir / 'sentiments_summary.xlsx').exists()
