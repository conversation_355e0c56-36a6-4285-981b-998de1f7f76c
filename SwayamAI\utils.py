import os
import shutil
import configparser
import logging
import traceback
import pickle
import uuid
import pandas as pd
import portalocker
import time
from flask.sessions import SessionMixin
from flask import request

class FileSystemSession(dict, SessionMixin):
    def __init__(self, data=None):
        super().__init__(data or {})

class FileSystemSessionInterface:
    def __init__(self, session_dir):
        self.session_dir = session_dir
        if not os.path.exists(session_dir):
            os.makedirs(session_dir)

    def _session_path(self, session_id):
        return os.path.join(self.session_dir, session_id)

    def open_session(self, app, request):
        logging.info('open session with lock.')
        session_id = request.cookies.get(app.session_cookie_name)
        if not session_id:
            return FileSystemSession({})
        session_file = self._session_path(session_id)
        if os.path.exists(session_file):
            with open(session_file, 'rb') as f:
                try:
                    portalocker.lock(f, portalocker.LOCK_SH)
                    data = pickle.load(f)
                finally:
                    try:
                        portalocker.unlock(f)
                    except Exception:
                        pass
                return FileSystemSession(data)
        return FileSystemSession({})

    def save_session(self, app, session, response):
        logging.info('save session with lock.')
        # get session id from cookie or create a new one
        session_id = request.cookies.get(app.session_cookie_name)
        if not session_id:
            session_id = str(uuid.uuid4())
            response.set_cookie(app.session_cookie_name, session_id)

        session_file = self._session_path(session_id)

        # ensure directory exists
        os.makedirs(os.path.dirname(session_file), exist_ok=True)

        with open(session_file, 'wb') as f:
            try:
                portalocker.lock(f, portalocker.LOCK_EX)
                pickle.dump(dict(session), f)
            finally:
                try:
                    portalocker.unlock(f)
                except Exception:
                    pass

def preprocess_docs(docs, tokenizer, threshold, token_max_length):
    """
    Preprocess the documents by tokenizing and filtering based on a threshold.
    Returns (long_docs, short_docs, long_indices, short_indices)
    """
    long_docs = []
    short_docs = []
    long_docs_indices = []
    short_docs_indices = []
    for i, doc in enumerate(docs):
        if doc is not None and str(doc).strip() != "":
            tokens = tokenizer.tokenize(str(doc))
            if len(tokens) >= threshold:
                truncated_doc = tokenizer.convert_tokens_to_string(tokens[:token_max_length])
                long_docs.append(truncated_doc)
                long_docs_indices.append(i)
            else:
                short_docs.append(doc)
                short_docs_indices.append(i)
        else:
            short_docs.append(doc)
            short_docs_indices.append(i)
    return long_docs, short_docs, long_docs_indices, short_docs_indices

def safe_summarizer(text, tokenizer, summarizer, threshold=100, max_length=1024, min_length=100):
    """
    Safely summarize the text using the provided summarizer.
    """
    if not text:
        return ""
    text = str(text)
    tokens = tokenizer.tokenize(text)
    if len(tokens) > threshold:
        try:
            summary = summarizer(text, max_length=max_length, min_length=min_length, do_sample=False)
            # summarizer typically returns a list of dicts with 'summary_text'
            if isinstance(summary, (list, tuple)) and len(summary) > 0 and isinstance(summary[0], dict):
                return summary[0].get('summary_text', text)
            return str(summary)
        except Exception as e:
            logging.error(f"Error during summarization: {e}\n{traceback.format_exc()}")
            return text
    else:
        return text

def get_new_column_name(df, name='Summary'):
    """
    Generate a new column name by appending a number if the name already exists.
    """
    if name not in df.columns:
        return name
    i = 1
    new_name = f"{name}_{i}"
    while new_name in df.columns:
        i += 1
        new_name = f"{name}_{i}"
    return new_name

def get_regression_type(job_folder):
    """
    Determine the regression type based on the job folder's swayamml.conf.
    """
    config = configparser.ConfigParser()
    config_file = os.path.join(job_folder, 'swayamml.conf')
    if not os.path.exists(config_file):
        return 'default'
    config.read(config_file)
    # use fallback to be robust across Python versions
    return config.get('DEFAULT', 'regression_type', fallback='default')

def check_missing_data(file_path, field_name):
    """
    Check if the specified field has missing data in the CSV or Excel file.
    Returns (has_missing: bool, indices: list[int])
    """
    if file_path.endswith('.csv'):
        df = pd.read_csv(file_path)
    elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
        df = pd.read_excel(file_path)
    else:
        raise ValueError("Unsupported file format. Please provide a CSV or Excel file.")

    if field_name not in df.columns:
        raise KeyError(f"Field '{field_name}' not found in the file.")

    col = df[field_name]
    # treat NaN or empty/whitespace-only strings as missing
    missing_mask = col.isnull() | (col.astype(str).str.strip() == '')
    missing_indices = missing_mask[missing_mask].index.tolist()
    return (len(missing_indices) > 0), missing_indices

def copy_deployment_collaterals(source_dir, target_dir, model):
    """
    Copy deployment script and requirements for a given model from source_dir to target_dir.
    """
    collaterals = {
        "NLP Classification": {
            "deployment_scripts": "deployment_script_classification.py",
            "requirements": "requirements.txt"
        },
        "NON NLP Classification": {
            "deployment_scripts": "deployment_script_non_nlp.py",
            "requirements": "requirements.txt"
        },
        "NLP Regression": {
            "deployment_scripts": "deployment_script_regression.py",
            "requirements": "requirements.txt"
        },
        "NON NLP Regression": {
            "deployment_scripts": "deployment_script_regression_non_nlp.py",
            "requirements": "requirements.txt"
        },
        "Mixed Regression": {
            "deployment_scripts": "deployment_script_regression.py",
            "requirements": "requirements.txt"
        },
        "Unsupervised Anomaly Detection": {
            "deployment_scripts": "deployment_script_unsupervised_anomaly_detection.py",
            "requirements": "requirements.txt"
        },
        "Supervised Anomaly Detection": {
            "deployment_scripts": "deployment_script_supervised_anomaly_detection.py",
            "requirements": "requirements.txt"
        },
        "DL Classification": {
            "deployment_scripts": "deployment_script_dl_classification.py",
            "requirements": "requirements.txt"
        },
        "NLP Clustering": {
            "deployment_scripts": "deployment_script_clustering.py",
            "requirements": "requirements.txt"
        },
        "Forecasting": {
            "deployment_scripts": "deployment_script_forecasting.py",
            "requirements": "requirements.txt"
        },
    }

    if model not in collaterals:
        raise ValueError(f"Unsupported model type: {model}. Supported models are: {', '.join(collaterals.keys())}")

    os.makedirs(target_dir, exist_ok=True)

    deployment_script = collaterals[model]["deployment_scripts"]
    src_deployment_script = os.path.join(source_dir, deployment_script)
    if not os.path.exists(src_deployment_script):
        raise FileNotFoundError(f"Deployment script not found: {src_deployment_script}")
    dest_deployment_script = os.path.join(target_dir, os.path.basename(deployment_script))
    shutil.copy(src_deployment_script, dest_deployment_script)

    requirements = collaterals[model]["requirements"]
    src_requirements = os.path.join(source_dir, requirements)
    if not os.path.exists(src_requirements):
        raise FileNotFoundError(f"Requirements file not found: {src_requirements}")
    dest_requirements = os.path.join(target_dir, os.path.basename(requirements))
    shutil.copy(src_requirements, dest_requirements)