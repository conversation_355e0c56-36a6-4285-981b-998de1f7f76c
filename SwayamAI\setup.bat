@echo off
setlocal enableextensions

REM Simple setup script for Windows to create a virtual environment and install deps
REM It uses the "py -3" launcher if available, otherwise falls back to "python".

set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

set "VENV_DIR=.venv"
set "PYTHON_CMD=python"

REM Prefer the Windows Python launcher if present
py -3 --version >nul 2>&1
if %errorlevel%==0 (
    set "PYTHON_CMD=py -3"
)

echo === Checking Python ...
%PYTHON_CMD% --version || (
    echo [ERROR] Python is not available in PATH. Install Python 3.10+ and try again.
    goto :end
)

echo === Creating virtual environment at %VENV_DIR% ...
if not exist "%VENV_DIR%\Scripts\python.exe" (
    %PYTHON_CMD% -m venv "%VENV_DIR%" || (
        echo [ERROR] Failed to create virtual environment.
        goto :end
    )
) else (
    echo Virtual environment already exists. Skipping creation.
)

echo === Activating virtual environment ...
call "%VENV_DIR%\Scripts\activate.bat" || (
    echo [ERROR] Failed to activate virtual environment.
    goto :end
)

echo === Upgrading pip ...
python -m pip install --upgrade pip

echo === Installing requirements from requirements.txt ...
if exist requirements.txt (
    python -m pip install -r requirements.txt || (
        echo [ERROR] Failed to install required packages.
        goto :end
    )
) else (
    echo [WARN] requirements.txt not found. Skipping dependency install.
)

echo.
echo Setup complete.
echo Next steps:
echo   1. Ensure you have a .env file (copy .env.example to .env and edit if needed)
echo   2. Activate the environment when you open a new shell:
echo        PowerShell: .\.venv\Scripts\Activate.ps1
echo        CMD      : .venv\Scripts\activate
echo   3. Run the app:
echo        python app.py

:end
endlocal
exit /b 0
