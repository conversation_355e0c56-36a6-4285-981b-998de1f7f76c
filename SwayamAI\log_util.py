import os
import logging
from datetime import datetime
from typing import Tuple, Union


def setup_logger(logger_name: str, output_folder: str = None, level: int = logging.INFO) -> Tuple[logging.Logger, str]:
    """Create or get a named logger with a file and stream handler.

    Returns (logger, log_file_path).
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)
    # we attach handlers directly to this logger and prevent propagation
    logger.propagate = False

    if output_folder:
        os.makedirs(output_folder, exist_ok=True)
        log_file_path = os.path.join(output_folder, f"{logger_name}_{timestamp}.log")
    else:
        log_file_path = f"{logger_name}_{timestamp}.log"

    # Use absolute path to avoid duplicate FileHandler when comparing baseFilename
    log_file_path = os.path.abspath(log_file_path)

    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Add a FileHandler if one with the same filename is not already attached
    file_handler = None
    if not any(isinstance(h, logging.FileHandler) and os.path.abspath(h.baseFilename) == log_file_path for h in logger.handlers):
        file_handler = logging.FileHandler(log_file_path)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    else:
        # find existing handler so we can return it if needed
        for h in logger.handlers:
            if isinstance(h, logging.FileHandler) and os.path.abspath(h.baseFilename) == log_file_path:
                file_handler = h
                break

    # Add a StreamHandler if not already present
    stream_handler = None
    if not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
        stream_handler = logging.StreamHandler()
        stream_handler.setLevel(level)
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)
    else:
        for h in logger.handlers:
            if isinstance(h, logging.StreamHandler):
                stream_handler = h
                break

    return logger, log_file_path


def remove_logger(logger: Union[logging.Logger, str]) -> None:
    """Remove and close all handlers from the given logger or logger name."""
    if isinstance(logger, str):
        logger = logging.getLogger(logger)
    handlers = logger.handlers[:]
    for handler in handlers:
        try:
            logger.removeHandler(handler)
        except Exception:
            pass
        try:
            handler.close()
        except Exception:
            pass
