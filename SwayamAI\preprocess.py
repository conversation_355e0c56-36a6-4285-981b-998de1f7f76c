import os
import re
import string
import logging
from typing import List, Tu<PERSON>, Dict, Optional

import pandas as pd
import numpy as np

from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer

# avoid downloading at import time; require environment to have corpora installed.
try:
    from nltk.corpus import stopwords as nltk_stopwords
    STOPWORDS = set(nltk_stopwords.words('english'))
except Exception:
    try:
        from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS
        STOPWORDS = set(ENGLISH_STOP_WORDS)
    except Exception:
        STOPWORDS = set()

# Do not mutate stdlib punctuation; define a local policy
CUSTOM_PUNCT = string.punctuation.replace('?', '')

class TextPreprocess:
    def __init__(self, stopwords_to_retain: Optional[set] = None) -> None:
        retained = stopwords_to_retain or set()
        self.stopwords = STOPWORDS - retained
        self.lemmatizer = WordNetLemmatizer()
        self.blacklist = set()
        self._lemmatize_enabled = True
        logging.getLogger(__name__)

    # Accept both historical signatures:
    # (data, column_name, selected_columns) and (data, selected_columns, column_name)
    def preprocess_text(self, data: pd.DataFrame, arg2, arg3) -> pd.DataFrame:
        if isinstance(arg2, list) and isinstance(arg3, str):
            selected_columns: List[str] = list(arg2)
            column_name: str = arg3
        else:
            column_name = str(arg2)
            selected_columns = list(arg3) if isinstance(arg3, (list, tuple, set)) else [str(arg3)]

        logging.info(f'Initial data shape: {data.shape}')
        data = data.copy()
        # Avoid None in to_replace list for type-checkers; pandas handles None->NaN automatically
        data[column_name] = data[column_name].replace(['nan', ''], np.nan)
        data['Cleaned_data'] = data[column_name].apply(self.preprocess_text_steps)
        selected_columns = list(selected_columns) + ['Cleaned_data']
        logging.info(f'Data shape after cleaning: {data.shape}')
        data.dropna(subset=selected_columns, inplace=True)
        logging.info(f'Data shape after dropping NaNs: {data.shape}')
        return data

    def preprocess_text_steps(self, text):
        if pd.isna(text):
            return ''
        if not isinstance(text, str):
            text = str(text)
        text = self.remove_urls(text)
        text = self.expand_contractions(text)
        text = self.remove_numbers_and_single_chars(text)
        text = self.remove_punctuation(text)
        text = text.lower()
        tokens = self.tokenize(text)
        tokens = self.remove_stopwords(tokens)
        tokens = self.lemmatize_tokens(tokens)
        tokens = self.remove_blacklisted(tokens)
        tokens = [t for t in tokens if t.strip()]
        return ' '.join(tokens)

    def expand_contractions(self, text: str) -> str:
        # best-effort: use contractions package if present, else simple replace for common cases
        try:
            import contractions
            return str(contractions.fix(text))
        except Exception:
            text = re.sub(r"\bcan't\b", "cannot", text, flags=re.I)
            text = re.sub(r"\bn't\b", " not", text, flags=re.I)
            text = re.sub(r"\bI'm\b", "I am", text, flags=re.I)
            return text

    def remove_punctuation(self, text: str) -> str:
        # preserve '?' per original customization
        table = str.maketrans('', '', CUSTOM_PUNCT)
        return text.translate(table)

    def remove_numbers_and_single_chars(self, text: str) -> str:
        # remove standalone single letters and numbers
        text = re.sub(r'\b[a-zA-Z]\b', ' ', text)
        text = re.sub(r'\d+', ' ', text)
        return text

    def remove_urls(self, text):
        return re.sub(r'http\S+|www\.\S+', '', text)

    def tokenize(self, text: str):
        try:
            return word_tokenize(text)
        except Exception:
            # fallback simple split
            return text.split()

    def remove_stopwords(self, tokens):
        return [t for t in tokens if t.lower() not in self.stopwords]

    def lemmatize_tokens(self, tokens):
        if not self._lemmatize_enabled:
            return tokens
        try:
            return [self.lemmatizer.lemmatize(t) for t in tokens]
        except Exception as e:
            # If WordNet is not available or NLTK raises, disable lemmatization for this session
            try:
                logging.warning(f"Lemmatization disabled due to NLTK/WordNet error: {e}")
            except Exception:
                pass
            self._lemmatize_enabled = False
            return tokens

    def remove_blacklisted(self, tokens):
        if not self.blacklist:
            return tokens
        return [t for t in tokens if t not in self.blacklist]

    def remove_empty_text(self, text):
        return '' if not text else text

class FeaturePreprocess:
    def __init__(self) -> None:
        self.imputer: Optional[SimpleImputer] = None
        self.scaler: Optional[MinMaxScaler] = None
        self.categorical_mappings: Dict[str, Dict] = {}
        self.numeric_feature_names: List[str] = []

    def preprocess_features(self, train_data: pd.DataFrame) -> Tuple[pd.DataFrame, List[str], List[str], List[str], Dict[str, Dict], Dict[str, float]]:
        data = train_data.copy()
        # First, create useful numeric features from common patterns
        try:
            data = self._engineer_common_numeric_features(data)
        except Exception:
            # continue even if feature engineering fails
            pass

        textual_cols = [c for c in data.columns if data[c].dtype == object]
        # best-effort datetime detection using dtype.kind
        datetime_cols = [c for c in data.columns if str(getattr(data[c].dtype, 'kind', '')) in ('M',)]
        # reserved for API compatibility
        string_to_num_cols: List[str] = []

        # remember which columns are numeric after engineering
        original_numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()

        # frequency encode categorical columns
        cat_cols_encoded: Dict[str, Dict] = {}
        for col in data.select_dtypes(include=['object', 'category']).columns:
            mapping = self.frequency_encode(data, col)
            cat_cols_encoded[col] = mapping
            self.categorical_mappings[col] = mapping
            data[col] = data[col].map(mapping).fillna(0)

        # numeric impute & scale ONLY on original numeric columns (not encoded categoricals)
        num_cols = list(original_numeric_cols)
        if num_cols:
            self.imputer = SimpleImputer(strategy='median')
            data[num_cols] = self.imputer.fit_transform(data[num_cols])
            self.scaler = MinMaxScaler()
            data[num_cols] = self.scaler.fit_transform(data[num_cols])
            self.numeric_feature_names = list(num_cols)

        numerical_cols_scaled = {c: 1.0 for c in num_cols}
        return data, textual_cols, datetime_cols, string_to_num_cols, cat_cols_encoded, numerical_cols_scaled

    def _engineer_common_numeric_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create numeric features from common column patterns: dates, duration, listed_in.
        - date-like strings: add <col>_year, <col>_month when parseable
        - duration: add duration_minutes and duration_seasons when present
        - listed_in: add listed_in_count (number of categories)
        """
        df = data.copy()
        # Parse date-like object columns into year/month
        for col in list(df.columns):
            s = df[col]
            if s.dtype == object:
                try:
                    dt = pd.to_datetime(s, errors='coerce')
                except Exception:
                    dt = pd.to_datetime(s, errors='coerce')
                valid_ratio = float((~dt.isna()).mean()) if len(dt) else 0.0
                if valid_ratio >= 0.6:
                    try:
                        df[f"{col}_year"] = dt.dt.year.astype('Int64')
                        df[f"{col}_month"] = dt.dt.month.astype('Int64')
                    except Exception:
                        pass

        # Parse duration
        if 'duration' in df.columns:
            mins, seasons = self._parse_duration_series(df['duration'])
            try:
                df['duration_minutes'] = mins
                df['duration_seasons'] = seasons
            except Exception:
                pass

        # listed_in count
        if 'listed_in' in df.columns and df['listed_in'].dtype == object:
            try:
                df['listed_in_count'] = df['listed_in'].fillna('').astype(str).apply(lambda x: 0 if x.strip()=='' else x.count(',') + 1)
            except Exception:
                pass

        return df

    def _parse_duration_series(self, s: pd.Series) -> Tuple[pd.Series, pd.Series]:
        """Return (minutes, seasons) parsed from a duration string series."""
        # Use top-level 're' import for flags
        s_str = s.fillna('').astype(str)
        # minutes like '90 min'
        mins = s_str.str.extract(r'(\d+)\s*min', flags=re.IGNORECASE)[0]
        # seasons like '2 Seasons' or '1 Season'
        seas = s_str.str.extract(r'(\d+)\s*Season', flags=re.IGNORECASE)[0]
        # convert to numeric (Int64 nullable), fill NA with 0
        mins = pd.to_numeric(mins, errors='coerce').fillna(0).astype('Int64')
        seas = pd.to_numeric(seas, errors='coerce').fillna(0).astype('Int64')
        return mins, seas

    def datetime_col_to_month(self, data: pd.DataFrame):
        for c in data.select_dtypes(include=[np.datetime64]).columns:
            data[c + '_month'] = data[c].dt.month
        return data

    def string_to_numeric(self, data: pd.DataFrame, exclude: Optional[List[str]] = None):
        _exclude_set = set(exclude or [])
        for c in data.columns:
            if c in _exclude_set:
                continue
            if data[c].dtype == object:
                try:
                    data[c] = pd.to_numeric(data[c], errors='coerce')
                except Exception:
                    pass
        return data

    def encode_categorical_col(self, data: pd.DataFrame, categorical_columns: List[str]):
        for col in categorical_columns:
            mapping = self.frequency_encode(data, col)
            self.categorical_mappings[col] = mapping
            data[col] = data[col].map(mapping).fillna(0)
        return data

    def frequency_encode(self, df: pd.DataFrame, column: str):
        freq = df[column].value_counts(dropna=False)
        mapping = (freq / len(df)).to_dict()
        return mapping

    def save_encodings(self, file_path_encodings, file_path_imputer=None, file_path_scaler=None):
        import joblib
        joblib.dump(self.categorical_mappings, file_path_encodings)
        if self.imputer and file_path_imputer:
            joblib.dump(self.imputer, file_path_imputer)
        if self.scaler and file_path_scaler:
            joblib.dump(self.scaler, file_path_scaler)

    def impute_and_scale_col(self, data: pd.DataFrame):
        # Align numeric columns to training set and apply imputer+scaler
        if self.imputer:
            cols = self.numeric_feature_names or list(data.select_dtypes(include=[np.number]).columns)
            X = data.reindex(columns=cols, fill_value=0)
            X_imputed = self.imputer.transform(X.to_numpy())
            import pandas as _pd, numpy as _np
            X_imputed = _np.asarray(X_imputed)
            data.loc[:, cols] = _pd.DataFrame(X_imputed, index=data.index, columns=cols)
        if self.scaler:
            cols = self.numeric_feature_names or list(data.select_dtypes(include=[np.number]).columns)
            X = data.reindex(columns=cols, fill_value=0)
            X_scaled = self.scaler.transform(X.to_numpy())
            import pandas as _pd, numpy as _np
            X_scaled = _np.asarray(X_scaled)
            data.loc[:, cols] = _pd.DataFrame(X_scaled, index=data.index, columns=cols)
        return data

    def preprocess_text_data(self, test_data: pd.DataFrame, datetime_cols, string_to_num_cols, textual_cols, numerical_cols_scaled):
        data = test_data.copy()
        # First, apply saved categorical encodings (on original categorical columns)
        for col, mapping in (self.categorical_mappings or {}).items():
            if col in data.columns:
                data[col] = data[col].map(mapping).fillna(0)
        # Then, safely coerce remaining object columns to numeric, excluding textual columns
        data = self.string_to_numeric(data, exclude=list(textual_cols or []))
        # Finally, impute and scale ONLY original numeric columns
        data = self.impute_and_scale_col(data)
        return data

    # Backward compatibility alias
    def preprocess_test_data(self, test_data: pd.DataFrame, numerical_cols_scaled=None, datetime_cols=None, string_to_num_cols=None, textual_cols=None, *_, **__):
        return self.preprocess_text_data(
            test_data,
            datetime_cols=datetime_cols,
            string_to_num_cols=string_to_num_cols,
            textual_cols=textual_cols,
            numerical_cols_scaled=numerical_cols_scaled,
        )








