class SudokuStorage{ constructor(){ this.GAME_STATE_KEY='sudoku_game_state'; this.STATISTICS_KEY='sudoku_statistics'; this.SETTINGS_KEY='sudoku_settings'; this.AUTO_SOLVED_KEY='sudoku_auto_solved'; }
 saveGameState(s){ try{ localStorage.setItem(this.GAME_STATE_KEY,JSON.stringify(s)); return true; }catch(_e){ return false; } }
 loadGameState(){ try{ const d=localStorage.getItem(this.GAME_STATE_KEY); return d?JSON.parse(d):null; }catch(_e){ return null; } }
 clearGameState(){ try{ localStorage.removeItem(this.GAME_STATE_KEY); return true; }catch(_e){ return false; } }
 saveGameCompletion(c){ try{ const stats=this.loadStatistics(); stats.completions=stats.completions||[]; stats.completions.push(c); const dif=c.difficulty; stats.byDifficulty[dif]=stats.byDifficulty[dif]||{played:0,completed:0,bestTime:null,averageTime:0,totalTime:0}; const ds=stats.byDifficulty[dif]; ds.completed++; ds.totalTime+=c.time; ds.averageTime=ds.totalTime/ds.completed; if(!ds.bestTime||c.time<ds.bestTime) ds.bestTime=c.time; if(stats.completions.length>100) stats.completions=stats.completions.slice(-100); this.saveStatistics(stats); return true; }catch(_e){ return false; } }
 loadStatistics(){ try{ const d=localStorage.getItem(this.STATISTICS_KEY); return d?JSON.parse(d):this.getDefaultStatistics(); }catch(_e){ return this.getDefaultStatistics(); } }
 saveStatistics(s){ try{ localStorage.setItem(this.STATISTICS_KEY,JSON.stringify(s)); return true; }catch(_e){ return false; } }
 getDefaultStatistics(){ return { totalGamesStarted:0,totalGamesCompleted:0, byDifficulty:{easy:{played:0,completed:0,bestTime:null,averageTime:0,totalTime:0}, medium:{played:0,completed:0,bestTime:null,averageTime:0,totalTime:0}, hard:{played:0,completed:0,bestTime:null,averageTime:0,totalTime:0}}, completions:[] }; }
 incrementGamesStarted(d){ try{ const s=this.loadStatistics(); s.totalGamesStarted++; s.byDifficulty[d].played++; this.saveStatistics(s); }catch(_e){} }
 saveSettings(s){ try{ localStorage.setItem(this.SETTINGS_KEY,JSON.stringify(s)); return true; }catch(_e){ return false; } }
 loadSettings(){ try{ const d=localStorage.getItem(this.SETTINGS_KEY); return d?JSON.parse(d):this.getDefaultSettings(); }catch(_e){ return this.getDefaultSettings(); } }
 getDefaultSettings(){ return { theme:'default', soundEnabled:false, autoSave:true, showTimer:true, showErrors:true, highlightRelated:true, highlightSameNumbers:true }; }
 setAutoSolvedFlag(v){ try{ localStorage.setItem(this.AUTO_SOLVED_KEY, v?'1':'0'); return true; }catch(_e){ return false; } }
 getAutoSolvedFlag(){ try{ return localStorage.getItem(this.AUTO_SOLVED_KEY)==='1'; }catch(_e){ return false; } }
}
if(typeof window!=='undefined'){ window.SudokuStorage=SudokuStorage; }
