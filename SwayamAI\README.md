# SwayamAI

This repository contains multiple modelling scripts (classification, clustering, regression) and helper modules.

Added a minimal Flask UI for running these scripts interactively:

- `app.py` - Flask application that lets you choose a use case, upload data, select fields, and run the appropriate processing function.
- `templates/` - HTML templates used by the Flask app.

## Quick Start (Windows)

1) Run `setup.bat` (double‑click or run from PowerShell) to create a virtual environment and install dependencies from `requirements.txt`.
2) Copy `.env.example` to `.env` and edit values if needed.
3) In a new PowerShell window, activate the environment and run the app:

```powershell
# For PowerShell
./.venv/Scripts/Activate.ps1
python app.py
```

Open http://127.0.0.1:5000 in your browser.

Notes:
- The UI invokes functions in `Classification_Modelling.py`, `Cluster_Modelling.py`, `non_nlp_clustering.py`, and `Regression.py`.
- Dependencies for various workflows are listed in `requirements.txt`. Some heavy, optional packages (e.g., torch, transformers) are only needed for related features.
- Uploaded files are stored under `uploads/<job_id>/`. Processing outputs are under `outputs/<job_id>/`.

See `README_SETUP.md` for a detailed step‑by‑step guide and troubleshooting.

## Production (WSGI)

Use a WSGI server instead of the Flask built-in dev server.

- Windows-friendly: Waitress

```powershell
./.venv/Scripts/Activate.ps1
python -m waitress --listen=0.0.0.0:5000 wsgi:application
```

If activation is blocked by policy in that PowerShell session:

```powershell
Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
```

- Linux/macOS: any WSGI server (e.g., gunicorn)

```bash
gunicorn -w 2 -b 0.0.0.0:5000 wsgi:application
```