import argparse
import os
import os.path as op
import sys
import json
import pandas as pd
import numpy as np
import joblib


def _load(file_path: str) -> pd.DataFrame:
    _, ext = op.splitext(file_path.lower())
    if ext == '.csv':
        return pd.read_csv(file_path)
    else:
        return pd.read_excel(file_path)


def _get_expected_n_features(imputer) -> int:
    # SimpleImputer exposes n_features_in_ (sklearn >=1.0) and statistics_.
    n = getattr(imputer, 'n_features_in_', None)
    if n is None:
        stats = getattr(imputer, 'statistics_', None)
        if stats is not None:
            n = int(stats.shape[0])
    if not n:
        raise SystemExit('Unable to infer expected feature count from imputer.')
    return int(n)


def _load_training_columns(artifacts_dir: str, expected_n: int | None = None) -> list[str] | None:
    """Try to recover the feature column names used during training.

    Order matters. We try the following, in order:
    - feature_columns.json (a list of column names)
    - feature_columns.txt (one name per line)
    - anomalies.csv (take all columns before the 'is_anomaly' column)
    Returns None if not found.
    """
    json_path = op.join(artifacts_dir, 'feature_columns.json')
    txt_path = op.join(artifacts_dir, 'feature_columns.txt')
    if op.exists(json_path):
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                cols = json.load(f)
            if isinstance(cols, list) and all(isinstance(c, str) for c in cols):
                return cols
        except Exception:
            pass
    if op.exists(txt_path):
        try:
            with open(txt_path, 'r', encoding='utf-8') as f:
                cols = [line.strip() for line in f if line.strip()]
            if cols:
                return cols
        except Exception:
            pass
    # Try to infer from anomalies.csv produced during training
    csv_path = op.join(artifacts_dir, 'anomalies.csv')
    if op.exists(csv_path):
        try:
            # Read a small sample to infer dtypes
            sample_df = pd.read_csv(csv_path, nrows=100)
            cols = list(sample_df.columns)
            if 'is_anomaly' in cols:
                idx = cols.index('is_anomaly')
                pre = cols[:idx]
                # Keep only numeric columns as features (exclude id-like columns)
                num_cols = [c for c in pre if pd.api.types.is_numeric_dtype(sample_df[c])]
                if expected_n and len(num_cols) != expected_n:
                    # If there's an off-by-one due to an ID column, try best-effort trim
                    if len(pre) > len(num_cols) and len(num_cols) > expected_n:
                        num_cols = num_cols[-expected_n:]
                    elif len(num_cols) > expected_n:
                        num_cols = num_cols[:expected_n]
                    elif len(num_cols) < expected_n:
                        # We'll let the caller pad as needed
                        pass
                if num_cols:
                    return num_cols
        except Exception:
            pass
    return None


def _align_features(df: pd.DataFrame, feature_names: list[str]) -> pd.DataFrame:
    """Build a numeric DataFrame with columns in feature_names order.
    Missing columns are created with NaN and values are coerced to numeric.
    """
    data = {}
    for col in feature_names:
        if col in df.columns:
            data[col] = pd.to_numeric(df[col], errors='coerce')
        else:
            # Create missing column with NaNs; imputer will handle it
            data[col] = np.nan
    return pd.DataFrame(data, index=df.index)


def main(argv=None):
    p = argparse.ArgumentParser(description='Run anomaly detection (numeric) using saved artifacts')
    # Support both flags and positional args
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/Excel with numeric columns')
    p.add_argument('positional_output', nargs='?', help='Path to write predictions CSV/XLSX')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/Excel with numeric columns')
    p.add_argument('--output', dest='flag_output', help='Path to write predictions CSV/XLSX')
    p.add_argument('--allow-mismatch', action='store_true',
                   help='If feature count differs from training, select first N numeric columns or pad with NaN to match expected feature count.')
    p.add_argument('--features', default=None,
                   help='Comma-separated list of feature column names in the exact order used for training. Overrides auto-detection.')
    args = p.parse_args(argv)

    inp = args.flag_input or args.positional_input
    if not inp:
        p.error('An input file is required. Provide --input or a positional input path.')
    outp = args.flag_output or args.positional_output
    if not outp:
        base, ext = op.splitext(inp)
        outp = f"{base}_anomalies.csv" if ext.lower() == '.csv' else f"{base}_anomalies.xlsx"

    df = _load(inp)
    # We'll align to the training feature schema when possible

    here = op.dirname(op.abspath(__file__))
    imputer = joblib.load(op.join(here, 'imputer.pkl'))
    scaler = joblib.load(op.join(here, 'scaler.pkl'))
    model = joblib.load(op.join(here, 'anomaly_model.pkl'))

    expected_n = _get_expected_n_features(imputer)
    # Derive training feature schema
    training_cols = None
    if args.features:
        training_cols = [c.strip() for c in args.features.split(',') if c.strip()]
    if not training_cols:
        training_cols = _load_training_columns(here, expected_n)

    if training_cols:
        feat_df = _align_features(df, training_cols)
        # Sanity: if training_cols length mismatches the imputer, trim or pad
        if feat_df.shape[1] != expected_n:
            if feat_df.shape[1] > expected_n:
                feat_df = feat_df.iloc[:, :expected_n]
            else:
                # pad extra unnamed columns
                for i in range(expected_n - feat_df.shape[1]):
                    feat_df[f'_missing_{i}'] = np.nan
    else:
        # Fall back to using numeric columns from the input
        num_df = df.select_dtypes(include=[np.number]).copy()
        if num_df.shape[1] == 0:
            raise SystemExit('No numeric columns found in input file.')
        if num_df.shape[1] != expected_n:
            if args.allow_mismatch:
                if num_df.shape[1] > expected_n:
                    print(f"[warn] Input has {num_df.shape[1]} numeric columns; expected {expected_n}. Using the first {expected_n} columns.", file=sys.stderr)
                    feat_df = num_df.iloc[:, :expected_n]
                else:
                    print(f"[warn] Input has {num_df.shape[1]} numeric columns; expected {expected_n}. Padding with NaNs to reach {expected_n}.", file=sys.stderr)
                    feat_df = num_df.copy()
                    for i in range(expected_n - num_df.shape[1]):
                        feat_df[f'_missing_{i}'] = np.nan
            else:
                raise SystemExit(
                    f"Feature mismatch: input has {num_df.shape[1]} numeric columns but the trained imputer expects {expected_n}. "
                    f"Place a feature_columns.json (list of column names) or anomalies.csv next to the artifacts to guide alignment, "
                    f"or rerun with --allow-mismatch to auto-truncate/pad."
                )
        else:
            feat_df = num_df

    X = scaler.transform(imputer.transform(feat_df.values))
    labels = model.predict(X)
    # Compute scores for the current X (avoid using training-time decision_scores_)
    scores = None
    try:
        # PyOD estimators usually implement decision_function for scoring new data
        scores = model.decision_function(X)
    except Exception:
        try:
            # Some estimators provide score_samples
            scores = model.score_samples(X)
        except Exception:
            scores = None
    if scores is None:
        scores = np.zeros(len(labels))

    # Include all original columns in the output, then append anomaly flags/scores
    out = df.copy()
    out['is_anomaly'] = labels
    out['anomaly_score'] = scores
    if outp.lower().endswith('.xlsx'):
        with pd.ExcelWriter(outp, engine='openpyxl') as w:
            out.to_excel(w, index=False)
    else:
        out.to_csv(outp, index=False)

    print('Wrote', outp)

if __name__ == '__main__':
    sys.exit(main())
