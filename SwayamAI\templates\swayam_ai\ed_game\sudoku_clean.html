{% extends "base.html" %}
{% block title %}Sudoku · SwayamEdGame{% endblock %}
{% block extra_head %}
<link rel="stylesheet" href="{{ url_for('static', filename='ed_game/sudoku/css/style.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='ed_game/sudoku/css/themes.css') }}">
<style>
  /* Scope gradient bg to page only */
  body { background: none; }
  body.sudoku-page { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
</style>
{% endblock %}
{% block content %}
<div class="sudoku-mount">
  <div class="app-container">
    <header class="header">
      <div class="d-flex align-items-center gap-3">
        <h1 class="app-title mb-0">
          <a href="{{ url_for('swayam_ed_game') }}" class="title-link d-flex align-items-center gap-2" title="Back to games">
            <i class="bi bi-grid-3x3-gap-fill"></i>
            Sudoku
          </a>
        </h1>
      </div>
      <div class="header-controls">
        <div class="info-sections">
          <span class="info-group"><span class="label">Time:</span> <span id="timer">00:00</span></span>
          <span class="info-group"><span class="label">Errors:</span> <span id="error-count">0/3</span></span>
        </div>
        <div class="actions">
          <button id="undo-btn" class="btn btn-secondary" title="Undo (Ctrl+Z)">Undo</button>
          <button id="hint-btn" class="btn btn-secondary">Hint</button>
          <button id="solve-btn" class="btn btn-secondary">Solve</button>
          <button id="reset-btn" class="btn btn-secondary">Reset</button>
          <button id="pause-btn" class="btn btn-secondary" title="Pause/Resume">Pause</button>
        </div>
      </div>
    </header>

    <section class="top-content-section">
      <div class="stats-and-difficulty">
        <div class="stats-section">
          <h3 class="stats-heading">Your Statistics</h3>
          <div class="stats-cards">
            <div class="stats-card"><div class="stats-value" id="stat-games-played">0</div><div class="stats-label">GAMES PLAYED</div></div>
            <div class="stats-card"><div class="stats-value" id="stat-games-won">0</div><div class="stats-label">GAMES WON</div></div>
            <div class="stats-card"><div class="stats-value" id="stat-best-time">00:00</div><div class="stats-label">BEST TIME</div></div>
            <button id="reset-stats-btn" class="btn btn-secondary">Reset Stats</button>
          </div>
        </div>
        <div class="difficulty-section">
          <h3 class="difficulty-heading">Choose Difficulty</h3>
          <div class="difficulty-cards">
            <div class="difficulty-card" data-diff="easy">
              <div class="diff-title">Easy</div>
              <div class="diff-sub">45–50 clues</div>
              <div class="diff-note">Beginner level</div>
            </div>
            <div class="difficulty-card" data-diff="medium">
              <div class="diff-title">Medium</div>
              <div class="diff-sub">35–40 clues</div>
              <div class="diff-note">Moderate challenge</div>
            </div>
            <div class="difficulty-card" data-diff="hard">
              <div class="diff-title">Hard</div>
              <div class="diff-sub">25–30 clues</div>
              <div class="diff-note">Expert level</div>
            </div>
          </div>
        </div>
      </div>
      <p class="text-muted game-instructions">
        Clues are the pre-filled numbers you can't change. You have 3 errors max; wrong entries turn red and count against the limit.
      </p>
    </section>

    <div class="game-layout">
      <div class="grid-instruction">
        Select any cell to see the number selector panel
      </div>
      <main class="game-container">
        <div class="grid-section">
          <div class="sudoku-grid" id="sudoku-grid"></div>
          <div id="pause-overlay"><div class="pause-box">Game Paused</div></div>
        </div>
        <div id="number-popup" class="number-selector-panel">
          <h4 class="selector-title">Number Selector</h4>
          <div class="popup-numbers">
            <button class="popup-btn" data-number="1">1</button>
            <button class="popup-btn" data-number="2">2</button>
            <button class="popup-btn" data-number="3">3</button>
            <button class="popup-btn" data-number="4">4</button>
            <button class="popup-btn" data-number="5">5</button>
            <button class="popup-btn" data-number="6">6</button>
            <button class="popup-btn" data-number="7">7</button>
            <button class="popup-btn" data-number="8">8</button>
            <button class="popup-btn" data-number="9">9</button>
            <button class="popup-btn erase-btn" data-number="0">⌫</button>
          </div>
        </div>
      </main>
    </div>

    <div id="game-modal" class="modal hidden">
      <div class="modal-content">
        <h2 id="modal-title">Congratulations!</h2>
        <p id="modal-message">You solved the puzzle!</p>
        <div class="modal-stats">
          <div>Time: <span id="final-time">00:00</span></div>
          <div>Errors: <span id="final-errors">0</span></div>
        </div>
        <div class="modal-actions">
          <button id="new-game-btn" class="btn btn-primary">New Game</button>
          <button id="close-modal-btn" class="btn btn-secondary">Close</button>
        </div>
      </div>
    </div>

    <div id="loading" class="loading hidden">
      <div class="loading-spinner"></div>
      <p>Loading puzzle...</p>
    </div>
  </div>
</div>
{% endblock %}
{% block scripts %}
<script>
  // Add a body class to scope Sudoku styles
  document.body.classList.add('sudoku-page');
  // Base path for static assets (used by DifficultyManager fetch calls)
  window.SUDOKU_ASSET_BASE = "{{ url_for('static', filename='ed_game/sudoku/') }}";
</script>
<script src="{{ url_for('static', filename='ed_game/sudoku/js/sudoku-solver.js') }}"></script>
<script src="{{ url_for('static', filename='ed_game/sudoku/js/sudoku-generator.js') }}"></script>
<script src="{{ url_for('static', filename='ed_game/sudoku/js/difficulty-manager.js') }}"></script>
<script src="{{ url_for('static', filename='ed_game/sudoku/js/storage.js') }}"></script>
<script src="{{ url_for('static', filename='ed_game/sudoku/js/game-logic.js') }}"></script>
<script src="{{ url_for('static', filename='ed_game/sudoku/js/ui-controller.js') }}"></script>
<script src="{{ url_for('static', filename='ed_game/sudoku/js/main.js') }}"></script>
<script>
  // Remove on page unload
  window.addEventListener('beforeunload', () => {
    document.body.classList.remove('sudoku-page');
  });
</script>
{% endblock %}
