{% extends "base.html" %}

{% block title %}SwayamDatasets · SwayamAI{% endblock %}

{% block content %}
<section class="sa-hero p-4 p-md-5 mb-4">
  <h1 class="h4 fw-semibold mb-2">SwayamDatasets</h1>
  <p class="mb-0 text-muted">Ready-to-use datasets for practice, demos, and benchmarking. Download, preview, and use directly in SwayamML.</p>
</section>

<section class="mb-5">
  <div class="d-flex align-items-center justify-content-between mb-3">
  <h2 class="h6 mb-0">Featured</h2>
  <a class="btn btn-sm sa-btn-primary" href="{{ url_for('swayam_ml') }}">Open SwayamML</a>
  </div>
  <div class="grid">
    <div class="card-link">
      <div class="fw-semibold">Titanic</div>
      <div class="small text-muted">Binary classification: survival prediction from passenger data.</div>
      <div class="mt-2"><a class="small" href="#">Download CSV</a></div>
    </div>
    <div class="card-link">
      <div class="fw-semibold">Housing</div>
      <div class="small text-muted">Regression: predict price/score from tabular features.</div>
      <div class="mt-2"><a class="small" href="#">Download CSV</a></div>
    </div>
    <div class="card-link">
      <div class="fw-semibold">Tweets</div>
      <div class="small text-muted">Text classification: sentiment/labeling for short texts.</div>
      <div class="mt-2"><a class="small" href="#">Download CSV</a></div>
    </div>
    <div class="card-link">
      <div class="fw-semibold">Transactions</div>
      <div class="small text-muted">Affinity analysis: basket data for association rules.</div>
      <div class="mt-2"><a class="small" href="#">Download CSV</a></div>
    </div>
  </div>
</section>

<section class="mb-5">
  <h2 class="h6 mb-3">Browse categories</h2>
  <div class="row g-3 g-md-4">
    <div class="col-12 col-md-3">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);">
        <div class="fw-semibold mb-2">Classification</div>
        <div class="small text-muted">Churn, fraud, spam/ham, multi-class labels</div>
      </div>
    </div>
    <div class="col-12 col-md-3">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);">
        <div class="fw-semibold mb-2">Regression</div>
        <div class="small text-muted">Prices, scores, time-to-event</div>
      </div>
    </div>
    <div class="col-12 col-md-3">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);">
        <div class="fw-semibold mb-2">NLP</div>
        <div class="small text-muted">Sentiment, topics, similarity</div>
      </div>
    </div>
    <div class="col-12 col-md-3">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);">
        <div class="fw-semibold mb-2">Clustering</div>
        <div class="small text-muted">Segmentation, embeddings</div>
      </div>
    </div>
  </div>
</section>

<style>
  .grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(260px, 1fr)); gap: 16px; }
  .card-link { text-decoration: none; color: inherit; background: white; border-radius: 16px; padding: 18px; border: 1px solid rgba(0,0,0,.05); box-shadow: 0 4px 14px rgba(2,6,23,.06); }
  .dark .card-link { background: #0f172a; border-color: #334155; }
</style>
{% endblock %}
