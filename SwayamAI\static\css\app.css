/* SwayamAI UI refresh — soft cards, gradients, and compact spacing */
:root{
  --sa-bg: #f5f7fb;
  --sa-surface: #ffffff;
  --sa-text: #0f172a; /* slate-900 */
  --sa-muted: #475569; /* slate-600 */
  --sa-primary: #6366f1; /* indigo-500 */
  --sa-primary-2: #8b5cf6; /* violet-500 */
  --sa-accent: #22d3ee; /* cyan-400 */
  --sa-shadow-light: 0 12px 24px rgba(15,23,42,.06), 0 4px 8px rgba(2,6,23,.06);
  --sa-shadow-inset: inset 0 1px 0 rgba(255,255,255,.6), inset 0 -1px 0 rgba(2,6,23,.05);
}

.dark:root{
  --sa-bg: #0b1220;
  --sa-surface: #111827;
  --sa-text: #e2e8f0; /* slate-200 */
  --sa-muted: #94a3b8; /* slate-400 */
  --sa-primary: #7c83ff;
  --sa-primary-2: #a78bfa;
  --sa-accent: #22d3ee;
  --sa-shadow-light: 0 12px 28px rgba(0,0,0,.45), 0 6px 12px rgba(0,0,0,.35);
  --sa-shadow-inset: inset 0 1px 0 rgba(255,255,255,.04), inset 0 -1px 0 rgba(0,0,0,.35);
}

body{
  background: var(--sa-bg) !important;
  color: var(--sa-text) !important;
}

.sa-hero{
  background: radial-gradient(1200px 480px at 10% -20%, rgba(99,102,241,.25), transparent 60%),
              radial-gradient(1200px 520px at 90% 0%, rgba(139,92,246,.18), transparent 60%),
              linear-gradient(180deg, var(--sa-surface), var(--sa-surface));
  border-radius: 24px;
  box-shadow: var(--sa-shadow-light);
  /* reduce visual height (override Bootstrap p-4 p-md-5 mb-4) */
  padding: .75rem 1rem !important;
  margin-bottom: .75rem !important;
}

.sa-card{
  background: var(--sa-surface);
  border-radius: 18px;
  box-shadow: var(--sa-shadow-light);
  border: 1px solid rgba(148,163,184,.18);
}

.sa-card-soft{
  background: linear-gradient(180deg, rgba(255,255,255,.7), rgba(255,255,255,.55));
  backdrop-filter: blur(6px);
}
.dark .sa-card-soft{
  background: linear-gradient(180deg, rgba(17,24,39,.85), rgba(17,24,39,.75));
}

.sa-pill{
  display:inline-flex; align-items:center; gap:.4rem;
  background: linear-gradient(135deg, var(--sa-primary), var(--sa-primary-2));
  color:#fff; border-radius:999px; padding:.35rem .65rem; font-weight:600; font-size:.8rem;
  box-shadow: 0 6px 14px rgba(99,102,241,.35);
}

.sa-btn-primary{
  background: linear-gradient(135deg, var(--sa-primary), var(--sa-primary-2));
  color:#fff; border:none;
  box-shadow: 0 8px 18px rgba(99,102,241,.35);
}
.sa-btn-primary:hover{ filter: brightness(1.06); color:#fff; }

.sa-badge{
  background: rgba(99,102,241,.12); color: var(--sa-primary); font-weight:600;
  border: 1px solid rgba(99,102,241,.25); border-radius: 999px; padding:.25rem .5rem; font-size:.75rem;
}

.sa-icon-3d{
  width:40px; height:40px; border-radius:12px;
  background: linear-gradient(145deg, rgba(99,102,241,.95), rgba(139,92,246,.95));
  box-shadow: var(--sa-shadow-light), var(--sa-shadow-inset);
  color:#fff; display:grid; place-items:center; font-size:1.1rem;
}

.sa-grid{
  display:grid; grid-template-columns: repeat(auto-fill, minmax(260px,1fr)); gap:18px;
}

/* File input compact styling inside table/cards */
.uc-file.form-control.form-control-sm{
  padding:.35rem .5rem; height:calc(1.625rem + 2px); font-size:.875rem;
}

/* Table nicer contrast */
table.table{--bs-table-bg:transparent}
table.table thead th{color: var(--sa-muted)}

/* Choices.js color alignment */
.choices__inner{background:var(--sa-surface); box-shadow: var(--sa-shadow-inset);}
.choices__list--dropdown{box-shadow: var(--sa-shadow-light)}

/* Footer subtle */
footer{opacity:.9}

/* Optional: tweak SA icon svg paths */
.sa-icon-3d svg{width:22px; height:22px}
.sa-icon-3d svg .g{stroke:#fff; stroke-width:1.6; stroke-linecap:round; stroke-linejoin:round; fill:none}
.sa-icon-3d svg .f{fill:#fff;}
