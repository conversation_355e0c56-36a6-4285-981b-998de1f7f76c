import os
import pandas as pd
import numpy as np
import time
import logging
import traceback
from log_util import setup_logger, remove_logger
from preprocess import TextPreprocess
from embeddings import TextEmbeddings
from modelling import ClusterModel
from output import ClusterOutput
from sklearn.impute import SimpleImputer
from sklearn.feature_selection import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from scipy import sparse
from sklearn.preprocessing import normalize

def apply_variance_threshold(X, threshold=0.001):
    """Apply variance threshold feature selection to array-like X."""
    selector = VarianceThreshold(threshold=threshold)
    try:
        reduced_data = selector.fit_transform(X)
    except ValueError as e:
        # handle case where no feature meets threshold by returning original X
        logging.warning(f"VarianceThreshold raised ValueError: {e}. Returning original data.")
        reduced_data = X
    return reduced_data

def clustering_process(file, fields, output_folder):
    start_time = time.time()
    logging.info(f"Starting clustering process for file: {file}")

    processing_logger = {
        "process_complete": False,
        "less_than_30sample_before_preprocessing": False,
        "Less_than_30sample_after_preprocessing": False,
        "preprocessing_complete": False
    }

    def apply_clustering_methods(df, results, reduced_embeddings, clusterer, name_suffix=""):
        """Call ClusterModel methods (KMeans / agglomerative / dbscan / affinity) on reduced_embeddings for all dataset sizes."""
        # Run all supported algorithms; each method has internal safeguards for small n and shape
        df, results = clusterer.KMeans(df, results, DM="", reduced_embeddings=reduced_embeddings, min_k=min_clusters, max_k=max_clusters, name_suffix=name_suffix)
        df, results = clusterer.agglomerative_clustering(df, results, DM="", reduced_embeddings=reduced_embeddings, name_suffix=name_suffix)
        df, results = clusterer.dbscan(df, results, DM="", reduced_embeddings=reduced_embeddings, min_samples=5, name_suffix=name_suffix)
        df, results = clusterer.affinity_propagation(df, results, DM="", reduced_embeddings=reduced_embeddings, name_suffix=name_suffix)
        return df, results

    pd.set_option('display.max_columns', None)
    # ensure output folder exists so we can always write debug files
    try:
        os.makedirs(output_folder, exist_ok=True)
    except Exception:
        logging.warning(f"Could not create output folder: {output_folder}")

    # Wrap the main processing to capture unexpected exceptions and return a useful summary
    _, file_extension = os.path.splitext(file)
    if file_extension == '.csv':
        Data = pd.read_csv(file)
    elif file_extension in ('.xlsx', '.xls'):
        Data = pd.read_excel(file)
    else:
        logging.error(f"Unsupported file format: {file_extension}")
        summary = pd.DataFrame({"error": [f"Unsupported file format: {file_extension}"]})
        return summary, [], 0, processing_logger

    # create concatenated field
    Data["concatenated_fields"] = Data[fields].astype(str).agg(" ".join, axis=1)

    if len(Data) < 30:
        processing_logger["less_than_30sample_before_preprocessing"] = True
        logging.warning("Less than 30 samples before preprocessing. Exiting process.")
        summary = pd.DataFrame({"message": ["Less than 30 samples before preprocessing"]})
        return summary, [], 0, processing_logger

    # Text preprocessing - use TextPreprocess.preprocess_text to create 'Cleaned_data'
    preprocess = TextPreprocess()
    selected_columns = []
    Data = preprocess.preprocess_text(Data, "concatenated_fields", selected_columns)

    if Data.empty:
        processing_logger["Data is empty after preprocessing"] = True
        logging.warning("Data is empty after preprocessing. Exiting process.")
        summary = pd.DataFrame({"error": ["Data is empty after preprocessing"]})
        return summary, [], 0, processing_logger

    if len(Data) < 30:
        processing_logger["Less_than_30sample_after_preprocessing"] = True
        logging.warning("Less than 30 samples after preprocessing. Exiting process.")
        summary = pd.DataFrame({"message": ["Less than 30 samples after preprocessing"]})
        return summary, [], 0, processing_logger

    processing_logger["preprocessing_complete"] = True

    Results = pd.DataFrame(columns=["Model Name", "Silhouette Score", "Calinski Harabasz Score", "Davies Bouldin Score", "skew_flag", "Number of Clusters"])

    embeddings = TextEmbeddings()
    embeddings.set_output_folder(output_folder)

    batch_size = 1000
    clusterer = ClusterModel()
    try:
        clusterer.set_output_folder(output_folder)
    except Exception:
        pass
    min_clusters = 2
    max_clusters = 15

    # If very large, compute TFIDF in batches and run clustering on reduced features
    if len(Data) > batch_size:
        logging.info(f"Data has more than {batch_size} rows, processing in batches.")
        X_Vector_TFIDF = embeddings.embeddings_tfidf(Data['Cleaned_data'].values)
        # Keep sparse, reduce with SVD, then L2-normalize rows
        try:
            X_reduced = embeddings.svd_fit(X_Vector_TFIDF, n_components=300)
        except Exception as e:
            logging.warning(f"SVD fit failed on batch TF-IDF: {e}; falling back to variance threshold only")
            X_tmp = apply_variance_threshold(X_Vector_TFIDF, threshold=0.001)
            X_reduced = np.asarray(X_tmp)
        X_reduced = normalize(np.asarray(X_reduced), norm='l2')
        Data, Results = apply_clustering_methods(Data, Results, X_reduced, clusterer)

    # Dense TF-IDF for full data path
    vectorized_docs = embeddings.embeddings_tfidf(Data['Cleaned_data'].values)

    # If embeddings() returned a list/ndarray of row vectors (dense or sparse rows),
    # stack into a single matrix. If elements are sparse, vstack into a CSR matrix.
    if isinstance(vectorized_docs, list) or (isinstance(vectorized_docs, np.ndarray) and getattr(vectorized_docs, 'dtype', None) == object):
        # detect whether elements are sparse
        first_elem = None
        try:
            first_elem = vectorized_docs[0]
        except Exception:
            first_elem = None

        if first_elem is not None and sparse.issparse(first_elem):
            # stack list of sparse row vectors into a single sparse matrix
            try:
                vectorized_docs = sparse.vstack(vectorized_docs).tocsr()
            except Exception as e:
                logging.error(f"Failed to stack sparse TF-IDF rows: {e}")
                raise
        else:
            # try to stack sequences into a dense 2D array
            try:
                vectorized_docs = np.vstack(tuple(vectorized_docs))
            except Exception:
                # fallback to converting directly to ndarray (may still be object dtype)
                vectorized_docs = np.asarray(vectorized_docs)

    # Now vectorized_docs is either a scipy sparse matrix or a dense ndarray
    if sparse.issparse(vectorized_docs):
        # replace NaNs in the sparse data buffer with 0.0 (SimpleImputer doesn't accept sparse)
    # Assume TF-IDF outputs finite values; skip direct sparse buffer manipulation
        # Dimensionality reduction for high-dim sparse TF-IDF
        try:
            vectorized_docs = embeddings.svd_fit(vectorized_docs, n_components=300)
        except Exception as e:
            logging.warning(f"SVD fit failed: {e}; proceeding without SVD on TF-IDF")
            # if SVD fails, continue with sparse matrix
            pass
    else:
        # ensure numeric dense array
        arr = np.asarray(vectorized_docs)
        if arr.dtype == object:
            # try coercing elements to numeric rows
            try:
                arr = arr.astype(float)
            except Exception:
                # Attempt row-wise coercion, otherwise return a clear error summary
                try:
                    arr = np.array([np.asarray(r, dtype=float) for r in arr])
                except Exception as e:
                    logging.error(f"vectorized_docs contains non-numeric values that cannot be coerced to float: {e}")
                    summary = pd.DataFrame({"error": ["vectorized_docs contains non-numeric values that cannot be coerced to float"]})
                    return summary, [], 0, processing_logger
        vectorized_docs = arr

        # impute numeric NaNs in dense arrays
        if np.isnan(vectorized_docs).any():
            imputer = SimpleImputer()
            try:
                vectorized_docs = imputer.fit_transform(vectorized_docs)
            except Exception as e:
                logging.error(f"Imputation failed on dense TF-IDF: {e}")
                summary = pd.DataFrame({"error": [f"Imputation failed: {e}"]})
                return summary, [], 0, processing_logger
            if np.isnan(vectorized_docs).any():
                logging.error("NaN values found after imputation. Exiting process.")
                summary = pd.DataFrame({"error": ["NaN values remain after imputation"]})
                return summary, [], 0, processing_logger

    # Optional: apply variance threshold post-SVD/dense to remove near-constant features
    vectorized_docs = apply_variance_threshold(vectorized_docs, threshold=0.001)

    # Ensure vectorized_docs is numeric (or sparse) before calling np.isnan
    if sparse.issparse(vectorized_docs):
        # Skip NaN buffer edits for sparse; upstream TF-IDF shouldn't produce NaNs
        pass
    else:
        arr = np.asarray(vectorized_docs)
        # If object dtype, attempt safe coercion to float
        if arr.dtype == object:
            try:
                arr = arr.astype(float)
            except Exception:
                try:
                    # try converting each row/element to numeric array
                    arr = np.array([np.asarray(r, dtype=float) for r in arr])
                except Exception as e:
                    logging.error(f"vectorized_docs contains non-numeric values that cannot be coerced to float: {e}")
                    return None, None, 0, processing_logger
        # Now arr should be numeric; impute any remaining NaNs
        if np.isnan(arr).any():
            imputer = SimpleImputer()
            try:
                arr = imputer.fit_transform(arr)
            except Exception as e:
                logging.error(f"Imputation failed on dense TF-IDF: {e}")
                summary = pd.DataFrame({"error": [f"Imputation failed: {e}"]})
                return summary, [], 0, processing_logger
            if np.isnan(arr).any():
                logging.error("NaN values found after imputation. Exiting process.")
                summary = pd.DataFrame({"error": ["NaN values remain after imputation"]})
                return summary, [], 0, processing_logger
        vectorized_docs = arr

    # Final L2 normalization to stabilize distance-based clustering
    try:
        vectorized_docs = normalize(np.asarray(vectorized_docs), norm='l2')
    except Exception as e:
        logging.warning(f"L2 normalization failed: {e}")

    # TF-IDF embeddings and clustering
    logging.info("Applying clustering methods to TF-IDF embeddings.")
    Data, Results = apply_clustering_methods(Data, Results, vectorized_docs, clusterer, name_suffix='[TFIDF]')

    # BERT embeddings and clustering
    logging.info("Applying clustering methods to BERT embeddings.")
    try:
        bert_embeddings = embeddings.embeddings_bert(Data['Cleaned_data'].values)
        bert_embeddings = normalize(np.asarray(bert_embeddings), norm='l2')
        Data, Results = apply_clustering_methods(Data, Results, bert_embeddings, clusterer, name_suffix='[BERT]')
    except Exception as e:
        logging.warning(f"BERT embeddings failed or not available: {e}")

    # Word2Vec embeddings and clustering
    try:
        logging.info("Applying clustering methods to Word2Vec embeddings.")
        w2v_embeddings = embeddings.embeddings_word2vec(Data['Cleaned_data'].values)
        w2v_embeddings = normalize(np.asarray(w2v_embeddings), norm='l2')
        Data, Results = apply_clustering_methods(Data, Results, w2v_embeddings, clusterer, name_suffix='[W2V]')
    except Exception as e:
        logging.warning(f"Word2Vec embeddings failed or not available: {e}")

    # compute skew flag counts (as proportion of models marked skew_flag == 1)
    if "skew_flag" in Results.columns and len(Results) > 0:
        skew_flag_count = Results['skew_flag'].value_counts(normalize=True)
        proportion_skew = float(skew_flag_count.get(1, 0.0))
        # If more than half of models are skewed, drop those models
        if proportion_skew > 0.5:
            models_to_drop = Results[Results['skew_flag'] == 1].shape[0]
            Results = Results[Results['skew_flag'] != 1]
        else:
            models_to_drop = 0
    else:
        models_to_drop = 0

    if 'skew_flag' in Results.columns:
        Results = Results.drop(columns=['skew_flag'])

    total_models_evaluated = len(Results)
    logging.info(f"Total models evaluated: {total_models_evaluated}")

    # Normalize scores and select best models with robust scaling and balance penalty
    def robust_minmax(series: pd.Series, lower_q=0.05, upper_q=0.95):
        s = series.copy()
        lo = s.quantile(lower_q)
        hi = s.quantile(upper_q)
        if pd.isna(lo) or pd.isna(hi) or hi - lo <= 1e-12:
            return pd.Series([0.5] * len(s), index=s.index)
        s = s.clip(lo, hi)
        return (s - lo) / (hi - lo)

    temp_results = Results.dropna(subset=['Silhouette Score', 'Calinski Harabasz Score', 'Davies Bouldin Score']).copy()

    # Compute cluster balance (1 - max cluster proportion) and filter highly skewed models
    balances = []
    to_drop_idx = []
    for idx, row in temp_results.iterrows():
        model_name = row['Model Name']
        max_prop = 1.0
        try:
            if model_name in Data.columns:
                vc = Data[model_name].value_counts(normalize=True)
                max_prop = float(vc.max()) if not vc.empty else 1.0
        except Exception:
            max_prop = 1.0
        balance = max(0.0, 1.0 - max_prop)
        balances.append(balance)
        # Mark for drop if >90% in one cluster or fewer than 2 clusters
        n_clusters = int(row.get('Number of Clusters', 0) or 0)
        if max_prop >= 0.9 or n_clusters < 2:
            to_drop_idx.append(idx)
    temp_results['Cluster Balance'] = balances

    # If dropping leaves nothing, keep all but rely on balance to penalize
    if len(to_drop_idx) < len(temp_results):
        temp_results = temp_results.drop(index=to_drop_idx)

    summary = pd.DataFrame()
    if not temp_results.empty:
        dav = temp_results['Davies Bouldin Score']
        dav_inv = pd.Series(0.5, index=temp_results.index)
        try:
            if dav.max() > 0:
                dav_inv = 1 - (dav / dav.max())
        except Exception:
            pass
        temp_results['DB Inverted'] = dav_inv

        sil_n = robust_minmax(temp_results['Silhouette Score'])
        cal_n = robust_minmax(temp_results['Calinski Harabasz Score'])
        dbi_n = robust_minmax(temp_results['DB Inverted'])
        bal_n = robust_minmax(temp_results['Cluster Balance'])

        # Weighted blend; weights sum to 1.0
        w_sil, w_cal, w_dbi, w_bal = 0.45, 0.25, 0.15, 0.15
        temp_results['Combined Score'] = (
            w_sil * sil_n +
            w_cal * cal_n +
            w_dbi * dbi_n +
            w_bal * bal_n
        )

        summary = temp_results.sort_values('Combined Score', ascending=False).head(3)

    # Prepare a UI-friendly summary: hide internal-only columns
    # Build UI-friendly table: drop internal columns and add warnings for low silhouette
    if not summary.empty:
        display_summary = summary.drop(columns=['DB Inverted', 'Combined Score', 'Cluster Balance'], errors='ignore').copy()
        try:
            # Add a warning column only for rows with low silhouette
            warn_msg = 'Low silhouette (<0.5) — consider more cleaning'
            display_summary['Warning'] = ''
            mask_low_sil = display_summary['Silhouette Score'] < 0.5
            display_summary.loc[mask_low_sil, 'Warning'] = warn_msg
        except Exception:
            # If metric missing, skip warnings gracefully
            pass
    else:
        display_summary = summary

    output = ClusterOutput()
    output_result_file = os.path.join(output_folder, "clustering_Results.xlsx")
    
    try:
        # Build an output DataFrame with all original columns + only the cluster columns
        # Identify cluster columns strictly by matching Results['Model Name'] to Data columns
        model_names = set()
        if 'Model Name' in Results.columns:
            model_names = set(str(x) for x in Results['Model Name'].tolist())
        cluster_cols = [c for c in Data.columns if c in model_names]
        # Keep original (input) columns: anything not in cluster_cols
        base_cols = [c for c in Data.columns if c not in set(cluster_cols)]
        output_df = Data[base_cols + cluster_cols].copy()
        # Write all models in 'clusters' and only top-3 models in 'Top 3 clusters'
        output.generate_clustering_output(output_df, Results, output_result_file, fields, top_models=display_summary)
    except Exception as e:
        logging.error(f"Failed to write clustering results Excel: {e}")

    preprocessed_file_path = os.path.join(output_folder, "clustering_Preprocessed_Data.csv")
    try:
        Data.to_csv(preprocessed_file_path, index=False)
    except Exception as e:
        logging.error(f"Failed to write preprocessed CSV: {e}")

    customer_files = []
    for p in (output_result_file, preprocessed_file_path):
        if os.path.exists(p):
            customer_files.append(p)

    # Defer adding embedding/model artifacts until after config is composed, so we can include only what's needed

    # Deployment artifacts generation disabled (only results workbooks/CSVs are produced)

    end_time = time.time()
    processing_logger["process_complete"] = True
    logging.info(f"Clustering process finished in {end_time - start_time:.2f} seconds.")
    
    # Return top 3 models as summary (UI-friendly), but the total count of models evaluated
    return display_summary, customer_files, total_models_evaluated, processing_logger
    # end of clustering_process
