import os
import sys
import subprocess
import pandas as pd

from Cluster_Modelling import clustering_process


def make_text_df(n=60):
    # two themes to enable clustering metrics
    texts = []
    for i in range(n):
        if i % 2 == 0:
            texts.append(f"apple orange fruit {i}")
        else:
            texts.append(f"laptop phone device {i}")
    return pd.DataFrame({'text': texts})


def test_clustering_text_end_to_end(tmp_path, tmp_output_dir, patch_transformers):
    df = make_text_df(60)
    inp = tmp_path / 't.csv'
    df.to_csv(inp, index=False)

    summary, files, n_models, log = clustering_process(
        str(inp), fields=['text'], output_folder=str(tmp_output_dir)
    )

    # Should return a summary (possibly top-3 rows)
    assert hasattr(summary, 'columns')
    # workbook should exist
    xlsx = tmp_output_dir / 'clustering_Results.xlsx'
    assert xlsx.exists()

    # If deployment artifacts present, attempt clustering exporter script
    dep = tmp_output_dir / 'deployment_script_clustering.py'
    if dep.exists():
        out_csv = tmp_path / 'assignments.csv'
        proc = subprocess.run([sys.executable, str(dep), '--input', str(inp), '--output', str(out_csv)], capture_output=True, text=True)
        assert proc.returncode == 0, proc.stderr
        assert out_csv.exists()
