import os
import math
import pandas as pd
from typing import List, Optional, Tuple


def _read_input(file_path: str) -> pd.DataFrame:
    if file_path.lower().endswith('.csv'):
        return pd.read_csv(file_path)
    return pd.read_excel(file_path)


def _transactions_from_single_column(df: pd.DataFrame, column: str, delimiter: str = ',') -> List[List[str]]:
    col = df[column].fillna('').astype(str)
    items_list: List[List[str]] = []
    for val in col:
        if not val:
            items_list.append([])
            continue
        parts = [p.strip() for p in str(val).split(delimiter)]
        parts = [p for p in parts if p]
        items_list.append(parts)
    return items_list


def _one_hot_from_transactions(transactions: List[List[str]]) -> pd.DataFrame:
    # Build item universe
    from collections import defaultdict
    item_index = {}
    rows = []
    for basket in transactions:
        rows.append(set(basket))
        for item in basket:
            if item not in item_index:
                item_index[item] = len(item_index)
    # Construct sparse-like boolean DataFrame
    if not item_index:
        return pd.DataFrame()
    data = []
    for basket in rows:
        row = [False] * len(item_index)
        for item in basket:
            row[item_index[item]] = True
        data.append(row)
    columns = [None] * len(item_index)
    for k, idx in item_index.items():
        columns[idx] = k
    return pd.DataFrame(data, columns=columns, dtype=bool)


def _one_hot_from_columns(df: pd.DataFrame, cols: List[str]) -> pd.DataFrame:
    X = df[cols].copy()
    # Heuristic: if columns contain many distinct non-empty strings (not 0/1), treat the cell values as items
    try:
        sample = X.head(200)
        # Count distinct non-empty strings across all selected columns
        vals = pd.unique(sample.astype(str).stack().to_numpy())
        vals = [v.strip() for v in vals if isinstance(v, str)]
        non_empty = [v for v in vals if v not in ('', '0', '1', 'True', 'False', 'true', 'false', 'nan', 'NaN', 'None')]
        indicator_like = (len(non_empty) <= 4)  # mostly empty/0/1/true/false
    except Exception:
        indicator_like = True

    if not indicator_like:
        # Wide-format with item names spread across columns: build transactions from cell values
        transactions: List[List[str]] = []
        for _, row in X.iterrows():
            items = []
            for v in row.tolist():
                if pd.isna(v):
                    continue
                s = str(v).strip()
                if not s or s in ('nan', 'None'):
                    continue
                items.append(s)
            # de-duplicate while preserving order
            seen = set()
            uniq = []
            for it in items:
                if it not in seen:
                    seen.add(it)
                    uniq.append(it)
            transactions.append(uniq)
        return _one_hot_from_transactions(transactions)

    # Indicator-like case: Consider non-zero/non-empty as True per column
    for c in cols:
        s = X[c]
        if s.dtype == bool:
            continue
        try:
            num = pd.to_numeric(s, errors='coerce')
            X[c] = (num.fillna(0) > 0)
        except Exception:
            X[c] = s.astype(str).str.strip().ne('')
    return X.astype(bool)


def _mine_itemsets_and_rules(
    one_hot: pd.DataFrame,
    algo: str = 'apriori',
    min_support: float = 0.05,
    min_confidence: float = 0.3,
    min_lift: Optional[float] = None,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    try:
        from mlxtend.frequent_patterns import apriori, fpgrowth, association_rules
    except ImportError as e:
        raise ImportError("mlxtend is required for Affinity Analysis. Please install it (pip install mlxtend).") from e

    if one_hot is None or one_hot.empty:
        return pd.DataFrame(), pd.DataFrame()

    algo = (algo or 'apriori').lower()
    if algo not in ('apriori', 'fpgrowth', 'fp-growth', 'fp_grow', 'fp'):
        algo = 'apriori'

    if algo == 'apriori':
        freq = apriori(one_hot, min_support=min_support, use_colnames=True)
    else:
        freq = fpgrowth(one_hot, min_support=min_support, use_colnames=True)

    if freq is None or freq.empty:
        return freq, pd.DataFrame()

    rules = association_rules(freq, metric='confidence', min_threshold=min_confidence)
    # Order and tidy
    if not rules.empty:
        sel_cols = ['antecedents', 'consequents', 'support', 'confidence', 'lift', 'leverage', 'conviction']
        for c in sel_cols:
            if c not in rules.columns:
                rules[c] = None
        rules = rules[sel_cols]
        # Optional filter by lift
        try:
            if min_lift is not None:
                rules = rules[rules['lift'] >= float(min_lift)]
        except Exception:
            pass
        # Convert frozensets to sorted, human-readable strings
        def _fmt(fs):
            try:
                return ', '.join(sorted(list(fs)))
            except Exception:
                return str(fs)
        rules['antecedents'] = rules['antecedents'].apply(_fmt)
        rules['consequents'] = rules['consequents'].apply(_fmt)
        # Sort by lift desc then confidence desc
        try:
            rules = rules.sort_values(['lift', 'confidence'], ascending=[False, False])
        except Exception:
            pass
    return freq, rules


def affinity_analysis_process(
    file_path: str,
    output_folder: str,
    *,
    algo: str = 'apriori',
    index_col: Optional[str] = None,
    data_mode: str = 'single',  # 'single' or 'multi'
    fields: Optional[List[str]] = None,
    single_text_column: Optional[str] = None,
    delimiter: str = ',',
    min_support: float = 0.05,
    min_confidence: float = 0.3,
    min_lift: Optional[float] = None,
    max_rules: Optional[int] = None,
):
    """
    Run affinity analysis (market basket) using Apriori or FP‑Growth.

    Returns (summary_df, customer_files, num_models=None)
    """
    os.makedirs(output_folder, exist_ok=True)
    df = _read_input(file_path)

    # Build one-hot encoded basket x item matrix
    one_hot = pd.DataFrame()
    basket_ids: List = []
    mode = (data_mode or 'single').lower()
    if mode == 'single':
        if not single_text_column or single_text_column not in df.columns:
            raise ValueError("For single-column mode, please select the column containing item lists (or item name if data is long-format).")
        if index_col and index_col in df.columns:
            # Long-format support: group by index_col and aggregate items
            items_series = df[[index_col, single_text_column]].dropna()
            items_series[single_text_column] = items_series[single_text_column].astype(str).str.strip()
            # Split any delimited values inside the item column before grouping
            delim = (delimiter or ',')
            def _split_val(v: str):
                return [p.strip() for p in str(v).split(delim)] if (delim in str(v)) else [str(v)]
            grouped = items_series.groupby(index_col)[single_text_column].apply(
                lambda s: sorted(set([p for v in s.tolist() for p in _split_val(v) if p]))
            )
            basket_ids = list(grouped.index)
            transactions = list(grouped.values)
            one_hot = _one_hot_from_transactions(transactions)
        else:
            # Row-per-basket with delimited items
            transactions = _transactions_from_single_column(df, single_text_column, delimiter=delimiter or ',')
            basket_ids = list(range(1, len(transactions) + 1))
            one_hot = _one_hot_from_transactions(transactions)
    else:
        cols = list(fields or [])
        missing = [c for c in cols if c not in df.columns]
        if missing:
            raise KeyError(f"Selected item columns not found: {', '.join(missing)}")
        # Auto-detect if fields not provided: use object columns by default
        if not cols:
            try:
                cols = [c for c in df.columns if df[c].dtype == object]
                if not cols:
                    cols = list(df.columns)
            except Exception:
                cols = list(df.columns)
        one_hot = _one_hot_from_columns(df, cols)
        if index_col and index_col in df.columns:
            basket_ids = df[index_col].tolist()
        else:
            basket_ids = list(range(1, len(one_hot) + 1))

    freq, rules = _mine_itemsets_and_rules(
        one_hot, algo=algo, min_support=min_support, min_confidence=min_confidence, min_lift=min_lift
    )

    customer_files: List[str] = []
    # Save frequent itemsets
    itemsets_file_csv = os.path.join(output_folder, 'affinity_itemsets.csv')
    try:
        freq.to_csv(itemsets_file_csv, index=False)
        customer_files.append(os.path.basename(itemsets_file_csv))
    except Exception:
        pass

    # Prepare renamed view for rules output (capitalized headers)
    def _rename_rules_cols(df_rules: pd.DataFrame) -> pd.DataFrame:
        if df_rules is None or df_rules.empty:
            return df_rules
        out = df_rules.copy()
        colmap = {
            'antecedents': 'Antecedent',
            'consequents': 'Consequent',
            'support': 'Support',
            'confidence': 'Confidence',
            'lift': 'Lift',
            'leverage': 'Leverage',
            'conviction': 'Conviction',
        }
        out = out.rename(columns=colmap)
        ordered = ['Antecedent', 'Consequent', 'Support', 'Confidence', 'Lift', 'Leverage', 'Conviction']
        existing = [c for c in ordered if c in out.columns]
        return out[existing]

    # Apply max_rules cap for output
    rules_len = 0 if rules is None else len(rules)
    if rules is not None and max_rules is not None and rules_len > int(max_rules):
        try:
            rules = rules.head(int(max_rules))
            rules_len = len(rules)
        except Exception:
            pass

    use_sqlite = rules_len > 10000
    if rules is not None and not rules.empty:
        rules_out = _rename_rules_cols(rules)
        if use_sqlite:
            try:
                import sqlite3
                db_path = os.path.join(output_folder, 'affinity_rules.db')
                with sqlite3.connect(db_path) as conn:
                    rules_out.to_sql('rules', conn, if_exists='replace', index=False)
                customer_files.append(os.path.basename(db_path))
            except Exception:
                pass
            # Also write top 10k CSV for quick preview
            try:
                top_path = os.path.join(output_folder, 'affinity_rules_top10k.csv')
                _rename_rules_cols(rules.head(10000)).to_csv(top_path, index=False)
                customer_files.append(os.path.basename(top_path))
            except Exception:
                pass
        else:
            rules_csv = os.path.join(output_folder, 'affinity_rules.csv')
            try:
                rules_out.to_csv(rules_csv, index=False)
                customer_files.append(os.path.basename(rules_csv))
            except Exception:
                pass

        # Excel workbook with both sheets (best-effort)
        try:
            xlsx_path = os.path.join(output_folder, 'affinity_results.xlsx')
            with pd.ExcelWriter(xlsx_path, engine='openpyxl') as writer:
                try:
                    freq.to_excel(writer, sheet_name='itemsets', index=False)
                except Exception:
                    pd.DataFrame(freq).to_excel(writer, sheet_name='itemsets', index=False)
                try:
                    rules_out.to_excel(writer, sheet_name='rules', index=False)
                except Exception:
                    pd.DataFrame(rules_out).to_excel(writer, sheet_name='rules', index=False)
            customer_files.append(os.path.basename(xlsx_path))
        except Exception:
            pass

    # Emit basket mapping CSV for drilldown (basket_id -> items)
    try:
        items_per_basket: List[str] = []
        if mode == 'single':
            # transactions already parsed
            items_per_basket = [delimiter.join(b) for b in transactions]
        else:
            # derive from one_hot
            items_per_basket = [
                delimiter.join([str(c) for c, val in row.items() if bool(val)])
                for _, row in one_hot.iterrows()
            ]
        id_col_name = str(index_col) if (index_col and index_col in df.columns) else 'row_id'
        map_df = pd.DataFrame({id_col_name: basket_ids, 'items': items_per_basket})
        map_path = os.path.join(output_folder, 'affinity_baskets.csv')
        map_df.to_csv(map_path, index=False)
        customer_files.append(os.path.basename(map_path))
    except Exception:
        pass

    # Provide concise summary: top 15 rules by lift
    summary = pd.DataFrame()
    try:
        if rules is not None and not rules.empty:
            summary = _rename_rules_cols(rules)
            # already sorted; just keep first 15
            summary = summary.head(15)
    except Exception:
        summary = pd.DataFrame()

    return summary, customer_files, None
