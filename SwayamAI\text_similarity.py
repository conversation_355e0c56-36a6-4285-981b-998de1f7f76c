import os
import pandas as pd
import numpy as np
from typing import Op<PERSON>, <PERSON><PERSON>, List


def _read_table(file_path: str) -> pd.DataFrame:
    ext = os.path.splitext(str(file_path))[1].lower()
    if ext == '.csv':
        return pd.read_csv(file_path)
    elif ext in ('.xls', '.xlsx'):
        return pd.read_excel(file_path)
    else:
        raise ValueError(f"Unsupported file type: {ext}")


def text_similarity_process(
    file_path: str,
    text_column: str,
    input_text: str,
    output_dir: str,
    index_col: Optional[str] = None,
    model_name: str = 'sentence-transformers/all-MiniLM-L6-v2',
) -> Tuple[pd.DataFrame, List[str], Optional[int]]:
    """
    Compute cosine similarity between a single input text and each row in one text column.

    Inputs
    - file_path: CSV/XLSX path
    - text_column: column name in the dataset containing texts to compare
    - input_text: the query text to compare against every row
    - output_dir: where to save artifacts
    - index_col: optional identifier column to carry to the output
    - model_name: sentence-transformers hub name

    Outputs
    - summary_df: head of results for UI (first 100 rows)
    - files: list of filenames created (CSV)
    - num_models: None (not applicable)
    """
    os.makedirs(output_dir, exist_ok=True)

    # Read data
    df = _read_table(file_path)
    if text_column not in df.columns:
        raise ValueError(f"Selected column not found: {text_column}. Available: {list(df.columns)}")

    # Prepare text arrays (ensure string; keep NaN as empty for embedding)
    texts = df[text_column].astype(str).fillna("").tolist()
    query = (input_text or "").strip()

    # Lazy import heavy deps
    try:
        from sentence_transformers import SentenceTransformer
    except Exception as e:
        raise ImportError("sentence-transformers is required for Text Similarity. Please install it.") from e

    # Load model
    model = SentenceTransformer(model_name)

    # Encode with L2 normalization to use dot-product as cosine similarity
    emb_texts = model.encode(texts, batch_size=64, convert_to_numpy=True, normalize_embeddings=True)

    # Decide mode based on input_text
    if query:
        # Query vs all rows
        emb_query = model.encode([query], batch_size=1, convert_to_numpy=True, normalize_embeddings=True)[0]
        sims = emb_texts @ emb_query

        # Build result DataFrame
        out_cols = {}
        if index_col and index_col in df.columns:
            id_name = index_col
            out_cols[id_name] = df[index_col].values
        else:
            # Generate random numeric identifiers (unique permutation of 1..N)
            n = len(df)
            rnd_ids = np.random.permutation(np.arange(1, n + 1))
            id_name = 'row_id'
            out_cols[id_name] = rnd_ids
        out_cols[text_column] = df[text_column].astype(str).values
        out_cols['query_text'] = query
        out_cols['similarity'] = sims
        result_df = pd.DataFrame(out_cols)

        out_name = 'text_similarity.csv'
        out_path = os.path.join(output_dir, out_name)
        result_df.to_csv(out_path, index=False)
        preview = result_df.head(100)
        return preview, [out_name], None
    else:
        # Pairwise among all rows (i<j) with safety cap on row count
        n_total = len(df)
        max_rows = int(os.environ.get('TEXT_SIM_MAX_ROWS_PAIRWISE', '2000'))
        if n_total > max_rows:
            df_pair = df.iloc[:max_rows].copy()
            emb_pair = emb_texts[:max_rows]
        else:
            df_pair = df
            emb_pair = emb_texts

        n = len(df_pair)
        # Select identifiers
        if index_col and index_col in df_pair.columns:
            ids = df_pair[index_col].values
            id_name = index_col
        else:
            ids = np.random.permutation(np.arange(1, n + 1))
            id_name = 'row_id'

        # Pairwise cosine similarity matrix
        S = emb_pair @ emb_pair.T
        # Use upper triangle without diagonal
        iu, ju = np.triu_indices(n, k=1)
        sims = S[iu, ju]

        # Build long-form pairs DataFrame
        res = pd.DataFrame({
            f'{id_name}_1': ids[iu],
            f'{id_name}_2': ids[ju],
            f'{text_column}_1': df_pair[text_column].astype(str).values[iu],
            f'{text_column}_2': df_pair[text_column].astype(str).values[ju],
            'similarity': sims,
        })

        out_name = 'text_similarity_pairs.csv'
        out_path = os.path.join(output_dir, out_name)
        res.to_csv(out_path, index=False)
        preview = res.head(100)
        return preview, [out_name], None
