{% extends "base.html" %}

{% block title %}Configure · {{ use_case_label or use_case }}{% endblock %}

{% block content %}
<div class="sa-card p-4 p-md-5">
  <div class="mb-3">
  <h2 class="h5 mb-1">Configure: <span class="text-primary">{{ use_case_label or (use_case|capitalize) }}</span></h2>
    <p class="text-muted mb-0">File: <strong>{{ filename }}</strong></p>
  </div>

  <form method="post" action="/run" class="row g-3">
    <input type="hidden" name="job_id" value="{{ job_id }}" />
    <input type="hidden" name="use_case" value="{{ use_case }}" />
    <input type="hidden" name="filename" value="{{ filename }}" />

    {% if use_case != 'text_similarity' %}
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <label for="fields" class="form-label mb-0">Select fields</label>
          <div class="btn-group btn-group-sm" role="group" aria-label="select-actions">
            <button type="button" id="selectAllFields" class="btn btn-outline-secondary">Select all</button>
            <button type="button" id="clearAllFields" class="btn btn-outline-secondary">Clear</button>
          </div>
        </div>
        <select name="fields" id="fields" multiple size="8" class="form-select">
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
        <div class="form-text">Use Ctrl/Cmd to select multiple fields. Or use the buttons to select/clear all.</div>
        {% if use_case == 'affinity' %}
          <div id="affinity-fields-note" class="small text-muted mt-1">Used only in Multiple columns mode. Ignored in Single column mode.</div>
        {% endif %}
        {% if use_case == 'sentiment' %}
          <div id="sentiment-one-field-hint" class="small mt-1 text-danger">For Sentiment Analysis, select exactly one text field.</div>
        {% endif %}
      </div>
    {% endif %}

    {% if use_case == 'affinity' %}
      <div class="col-12">
        <div class="alert alert-info small">
          Choose input mode and parameters. In single-column mode, pick a column that contains a list of items per basket (e.g., "Bread, Milk"). You can also handle long-format data by setting Index column to your basket ID and Items column to the product name column (one row per item). In multi-column mode, select item indicator columns above.
        </div>
      </div>
      <div class="col-12 col-md-4">
        <label for="affinity_algo" class="form-label">Algorithm</label>
        <select name="affinity_algo" id="affinity_algo" class="form-select">
          <option value="apriori" selected>Apriori</option>
          <option value="fpgrowth">FP‑Growth</option>
        </select>
      </div>
      <div class="col-12 col-md-4">
        <label for="affinity_mode" class="form-label">Input mode</label>
        <select name="affinity_mode" id="affinity_mode" class="form-select">
          <option value="single" selected>Single column (items list)</option>
          <option value="multi">Multiple columns (one-hot/indicators)</option>
        </select>
      </div>
      <div class="col-12 col-md-4">
        <label for="affinity_index_col" class="form-label">Index column (optional)</label>
        <select name="affinity_index_col" id="affinity_index_col" class="form-select">
          <option value="">None</option>
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
        <div class="form-text">Used only for output reference; not required.</div>
      </div>

      <div class="col-12 col-md-6" id="affinity-single-col-wrap">
        <label for="affinity_single_text_col" class="form-label">Items column</label>
        <select name="affinity_single_text_col" id="affinity_single_text_col" class="form-select">
          <option value="">-- select --</option>
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
        <div class="form-text">Each row should contain a comma-separated list of items.</div>
      </div>
      <div class="col-12 col-md-3">
        <label for="affinity_delimiter" class="form-label">Delimiter</label>
        <input type="text" name="affinity_delimiter" id="affinity_delimiter" class="form-control" value="," maxlength="3" />
      </div>
      <div class="col-6 col-md-3">
        <label for="affinity_min_support" class="form-label">Min support</label>
        <input type="number" step="0.001" min="0" max="1" name="affinity_min_support" id="affinity_min_support" class="form-control" value="0.05" />
      </div>
      <div class="col-6 col-md-3">
        <label for="affinity_min_confidence" class="form-label">Min confidence</label>
        <input type="number" step="0.01" min="0" max="1" name="affinity_min_confidence" id="affinity_min_confidence" class="form-control" value="0.3" />
      </div>
      <div class="col-6 col-md-3">
        <label for="affinity_min_lift" class="form-label">Min lift (optional)</label>
        <input type="number" step="0.01" min="0" name="affinity_min_lift" id="affinity_min_lift" class="form-control" placeholder="e.g., 1.2" />
      </div>
      <div class="col-6 col-md-3">
        <label for="affinity_max_rules" class="form-label">Max rules (optional)</label>
        <input type="number" step="1" min="1" name="affinity_max_rules" id="affinity_max_rules" class="form-control" placeholder="e.g., 20000" />
      </div>
      <div class="col-12">
        <div class="alert alert-secondary small">
          Outputs generated:
          <ul class="mb-0 ps-3">
            <li><strong>affinity_itemsets.csv</strong> (frequent itemsets with <em>Support</em>).</li>
            <li><strong>affinity_rules.csv</strong> (or <strong>affinity_rules.db</strong> if &gt; 10k rules, plus <strong>affinity_rules_top10k.csv</strong>): rules as <em>Antecedent</em> → <em>Consequent</em> with <em>Support</em>, <em>Confidence</em>, <em>Lift</em>, <em>Leverage</em>, <em>Conviction</em>.</li>
            <li><strong>affinity_results.xlsx</strong> (Excel with Itemsets and Rules sheets).</li>
            <li><strong>affinity_baskets.csv</strong> (basket → items mapping using your Index column or a generated row_id).</li>
          </ul>
        </div>
      </div>
    {% endif %}

    {% if use_case == 'classification' %}
      <div class="col-12 col-md-6">
        <label for="label" class="form-label">Label column</label>
        <select name="label" id="label" class="form-select">
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
      </div>
    {% elif use_case == 'non_nlp_classification' %}
      <div class="col-12 col-md-6">
        <label for="label" class="form-label">Target/Label column</label>
        <select name="label" id="label" class="form-select">
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
        <div class="form-text">Choose the column to predict. Features will be the selected fields above.</div>
      </div>
    {% elif use_case == 'regression' %}
      <div class="col-12 col-md-6">
        <label for="label" class="form-label">Target column</label>
        <select name="label" id="label" class="form-select">
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="col-12 col-md-6">
        <label for="selection_policy" class="form-label">Model selection policy</label>
        <select name="selection_policy" id="selection_policy" class="form-select">
          <option value="test" selected>Best on hold-out test (Adjusted R2)</option>
          <option value="cv">Best by CV mean R2 (GridSearchCV)</option>
        </select>
        <div class="form-text">Pick how to choose the final model family: by hold-out test (default) or by cross-validation.</div>
      </div>
    {% endif %}

    {% if use_case == 'sentiment' %}
      <div class="col-12 col-md-6">
        <label for="sentiment_profile" class="form-label">Model profile</label>
        <select name="sentiment_profile" id="sentiment_profile" class="form-select">
          <option value="fast_general" selected>Fast general (DistilBERT)</option>
          <option value="accurate_general">Accurate general (RoBERTa/BERT)</option>
          <option value="financial">Financial domain (FinBERT)</option>
          <option value="multilingual">Multilingual (XLM-R)</option>
        </select>
        <div class="form-text">Choose speed vs. accuracy or a domain/multilingual model.</div>
      </div>
      <div class="col-12 col-md-6 d-flex align-items-end">
        <div class="form-check mt-4">
          <input class="form-check-input" type="checkbox" value="1" id="neutral_band" name="neutral_band">
          <label class="form-check-label" for="neutral_band">
            Add neutral where confidence is low (binary models)
          </label>
        </div>
      </div>
      <div class="col-12">
        <div class="alert alert-info small">
          For Sentiment Analysis, select one text field in the "Select fields" list above.
          Input must be text. Output will contain the original text, a sentiment label, and a confidence score.
        </div>
      </div>
    {% endif %}

    {% if use_case == 'anomaly_numeric' %}
      <div class="col-12 col-md-6">
        <label for="anomaly_mode" class="form-label">Detection mode</label>
        <select name="anomaly_mode" id="anomaly_mode" class="form-select">
          <option value="all" selected>Use all selected numeric columns together (multivariate)</option>
          <option value="per_column">Detect per numeric column (univariate)</option>
        </select>
        <div class="form-text">Choose whether to treat all numeric columns jointly or run detection per column.</div>
      </div>
      <div class="col-12 col-md-6">
        <label for="index_column" class="form-label">Index column (optional)</label>
        <select name="index_column" id="index_column" class="form-select">
          <option value="">None (use row number)</option>
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
        <div class="form-text">Pick a column to use as the row identifier in outputs (e.g., ID, reference number).</div>
      </div>
      <div class="col-12">
        <div class="alert alert-info small">Output will include anomalies CSV and an Excel summary. In multivariate mode, artifacts (imputer, scaler, model) are saved for deployment.</div>
      </div>
    {% endif %}

    {% if use_case == 'text_similarity' %}
      <div class="col-12 col-md-6">
        <label for="text_column" class="form-label">Text column</label>
        <select name="text_column" id="text_column" class="form-select">
          <option value="">-- select --</option>
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="col-12 col-md-6">
        <label for="index_column" class="form-label">Index column (optional)</label>
        <select name="index_column" id="index_column" class="form-select">
          <option value="">None (use random row_id)</option>
          {% for c in columns %}
            <option value="{{ c }}">{{ c }}</option>
          {% endfor %}
        </select>
        <div class="form-text">Reference column to include in output, e.g., ID or ticket number. If none, a random row_id will be generated.</div>
      </div>
      <div class="col-12">
        <label for="input_text" class="form-label">Input text to compare (optional)</label>
        <textarea id="input_text" name="input_text" rows="4" class="form-control" placeholder="Type or paste your text here..."></textarea>
        <div class="form-text">If provided, we compare this text against each row. If left empty, we compute pairwise similarities among rows in the selected column.</div>
      </div>
      <div class="col-12">
  <div class="alert alert-info small">Cosine similarity with Sentence-Transformers. If input text is provided, output is <code>text_similarity.csv</code>. Otherwise, pairwise output is <code>text_similarity_pairs.csv</code> (may cap very large datasets).</div>
      </div>
    {% endif %}

    <div class="col-12 d-flex gap-2 justify-content-end">
      <a href="{{ url_for('swayam_ml') }}" class="btn btn-outline-secondary"><i class="bi bi-arrow-left"></i> Back</a>
      <button type="submit" id="run-button" class="btn sa-btn-primary"><i class="bi bi-play-fill me-1"></i> Run</button>
    </div>
  </form>
</div>

<script>
  (function(){
    const form = document.querySelector('form');
    const runButton = document.getElementById('run-button');
    const useCase = '{{ use_case }}';
    const fieldsEl = document.getElementById('fields');
  const hint = document.getElementById('sentiment-one-field-hint');
  const textColumnSel = document.getElementById('text_column');
  const inputText = document.getElementById('input_text');
  // Affinity controls
  const affMode = document.getElementById('affinity_mode');
  const affSingleWrap = document.getElementById('affinity-single-col-wrap');
  const affItemsSel = document.getElementById('affinity_single_text_col');
  const affFieldsNote = document.getElementById('affinity-fields-note');
  const btnSelectAll = document.getElementById('selectAllFields');
  const btnClearAll = document.getElementById('clearAllFields');

  function ensureEnabledLater(el){
    if (!el) return;
    // Some scripts/styles may reapply disabled; force-enable shortly after
    setTimeout(()=>{ try { el.disabled = false; el.removeAttribute('disabled'); el.setAttribute('aria-disabled','false'); el.classList.remove('disabled'); } catch(e){} }, 0);
    setTimeout(()=>{ try { el.disabled = false; el.removeAttribute('disabled'); el.setAttribute('aria-disabled','false'); el.classList.remove('disabled'); } catch(e){} }, 50);
  }

    function selectedCount() {
      if (!fieldsEl) return 0;
      // Works for native select; if enhanced by Choices, the original select still has selectedOptions
      return fieldsEl.selectedOptions ? fieldsEl.selectedOptions.length : (Array.from(fieldsEl.options).filter(o => o.selected).length);
    }

    function validateSentiment() {
      if (useCase !== 'sentiment') return true;
      const ok = selectedCount() === 1;
      if (hint) {
        hint.classList.toggle('text-danger', !ok);
        hint.classList.toggle('text-muted', ok);
        hint.textContent = ok ? '1 field selected.' : 'For Sentiment Analysis, select exactly one text field.';
      }
      if (runButton) runButton.disabled = !ok;
      return ok;
    }

    function validateSimilarity(){
      if (useCase !== 'text_similarity') return true;
      const ok = !!(textColumnSel && textColumnSel.value);
      if (runButton) runButton.disabled = !ok;
      return ok;
    }

    function toggleAffinityUI(){
      if (useCase !== 'affinity') return;
      const mode = affMode ? affMode.value : 'single';
      if (affSingleWrap) affSingleWrap.style.display = (mode === 'single') ? '' : 'none';
      // Disable multi-select fields in single-column mode (ignored by backend)
    if (fieldsEl) {
        const single = (mode === 'single');
        if (single) {
      if (window.swayamDisableFields) { try { window.swayamDisableFields(); } catch(e){} }
      fieldsEl.disabled = true;
      fieldsEl.setAttribute('aria-disabled', 'true');
      fieldsEl.classList.add('disabled');
          if (btnSelectAll) btnSelectAll.disabled = true;
          if (btnClearAll) btnClearAll.disabled = true;
        } else {
          // Aggressively remove any disabled state left by the browser
      if (window.swayamEnableFields) { try { window.swayamEnableFields(); } catch(e){} }
          fieldsEl.disabled = false;
          fieldsEl.removeAttribute('disabled');
          fieldsEl.setAttribute('aria-disabled', 'false');
          fieldsEl.classList.remove('disabled');
          if (btnSelectAll) btnSelectAll.disabled = false;
          if (btnClearAll) btnClearAll.disabled = false;
          ensureEnabledLater(fieldsEl);
        }
        if (affFieldsNote){
          affFieldsNote.textContent = single
            ? 'Ignored in Single column mode.'
            : 'Used in Multiple columns mode to indicate items (1/true = present).';
        }
      }
    }

    function validateAffinity(){
      if (useCase !== 'affinity') return true;
      const mode = affMode ? affMode.value : 'single';
      let ok = true;
      if (mode === 'single'){
        ok = !!(affItemsSel && affItemsSel.value);
      } else {
        // require at least one selected field for multi-column mode
        const cnt = fieldsEl && (fieldsEl.selectedOptions ? fieldsEl.selectedOptions.length : Array.from(fieldsEl.options||[]).filter(o=>o.selected).length);
        ok = (cnt && cnt > 0);
      }
      if (runButton) runButton.disabled = !ok;
      return ok;
    }

    // Initial state
    validateSentiment();
    validateSimilarity();
    toggleAffinityUI();
    validateAffinity();
  if (fieldsEl) {
      fieldsEl.addEventListener('change', ()=>{ validateSentiment(); validateSimilarity(); validateAffinity(); });
      fieldsEl.addEventListener('input', ()=>{ validateSentiment(); validateSimilarity(); validateAffinity(); });
    }
  if (textColumnSel) textColumnSel.addEventListener('change', validateSimilarity);
  if (inputText) inputText.addEventListener('input', validateSimilarity);
  if (affMode) {
    affMode.addEventListener('change', ()=>{ toggleAffinityUI(); validateAffinity(); });
    affMode.addEventListener('input', ()=>{ toggleAffinityUI(); validateAffinity(); });
  }

  // Wire Select all / Clear actions
  if (btnSelectAll && fieldsEl) {
    btnSelectAll.addEventListener('click', function(){
      if (fieldsEl.disabled) return;
      Array.from(fieldsEl.options || []).forEach(o => o.selected = true);
      fieldsEl.dispatchEvent(new Event('change'));
    });
  }
  if (btnClearAll && fieldsEl) {
    btnClearAll.addEventListener('click', function(){
      if (fieldsEl.disabled) return;
      Array.from(fieldsEl.options || []).forEach(o => o.selected = false);
      fieldsEl.dispatchEvent(new Event('change'));
    });
  }
  if (affItemsSel) affItemsSel.addEventListener('change', validateAffinity);

    form.addEventListener('submit', function(e) {
      if (useCase === 'sentiment' && !validateSentiment()) {
        // block submit if invalid
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
  if (useCase === 'text_similarity' && !validateSimilarity()){
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      if (useCase === 'affinity' && !validateAffinity()){
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      // proceed with spinner
      runButton.disabled = true;
      runButton.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Running...`;
    });
  })();
</script>
{% endblock %}
