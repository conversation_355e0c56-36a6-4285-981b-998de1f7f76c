"""
WSGI entrypoint for production servers.

This module exposes the Flask application object for WSGI servers like waitress (Windows) or gunicorn/uwsgi (Linux).
"""
import os
from app import app as app  # Flask instance defined in app.py

# Expose a conventional name many servers look for
application = app

if __name__ == "__main__":
    # Allow local run of this module if needed
    debug = os.getenv("FLASK_DEBUG", "false").lower() in ("1", "true", "yes", "on")
    try:
        port = int(os.getenv("FLASK_PORT", "5000"))
    except Exception:
        port = 5000
    app.run(debug=debug, port=port)
