"""
Deployment script for NLP Clustering (predict on new rows using a persisted estimator).

Usage (PowerShell):
    # Backward-compatible flags
    python deployment_script_clustering_predict.py --input data.xlsx --output assignments.xlsx

    # Or positional input (output will default to data_assignments.csv/xlsx)
    python deployment_script_clustering_predict.py data.csv

Expects in the same folder:
    - swayamml.conf with keys:
      model_pkl_file_name (persisted KMeans "Model Name".pkl)
      embedding_name (tfidf_svd|w2v)
      embedding_pkl_file_name (tfidf_vectorizer_ngram1.pkl or word2vec_model.pkl)
      svd_pkl_file_name (only for tfidf_svd)

Notes:
- Currently supports KMeans (predict API). Other algorithms may not support predict.
- The text field used at training is "Cleaned_data"; if missing, we join all object columns.
"""
import os
import sys
import argparse
import configparser
import joblib
import numpy as np
import pandas as pd
import re


def load_config(base_dir: str) -> dict:
    cfg_path = os.path.join(base_dir, 'swayamml.conf')
    if not os.path.exists(cfg_path):
        raise FileNotFoundError(f"Config not found: {cfg_path}")
    cp = configparser.ConfigParser()
    cp.read(cfg_path)
    d = cp['DEFAULT'] if 'DEFAULT' in cp else {}
    return {
        'model_pkl': d.get('model_pkl_file_name', ''),
        'embedding_name': d.get('embedding_name', ''),
        'embedding_pkl': d.get('embedding_pkl_file_name', ''),
        'svd_pkl': d.get('svd_pkl_file_name', ''),
        'model_name': d.get('model_name', ''),
        'results_file_name': d.get('results_file_name', 'clustering_Results.xlsx'),
    }


def _safe_name(name: str) -> str:
    return re.sub(r"[^A-Za-z0-9_.-]+", "_", str(name)).strip("_")


def _resolve(base_dir: str, path: str) -> str:
    if not path:
        return ''
    p = str(path)
    return os.path.normpath(p) if os.path.isabs(p) else os.path.normpath(os.path.join(base_dir, p))


def _get_model_expected_dim(model) -> int | None:
    try:
        if hasattr(model, 'n_features_in_'):
            return int(model.n_features_in_)
    except Exception:
        pass
    try:
        if hasattr(model, 'cluster_centers_'):
            return int(model.cluster_centers_.shape[1])
    except Exception:
        pass
    return None


def _svd_components(svd) -> int | None:
    try:
        if hasattr(svd, 'n_components') and isinstance(svd.n_components, (int,)):
            return int(svd.n_components)
    except Exception:
        pass
    try:
        if hasattr(svd, 'n_components_'):
            return int(svd.n_components_)
    except Exception:
        pass
    try:
        if hasattr(svd, 'components_'):
            return int(getattr(svd.components_, 'shape', [None, None])[0])
    except Exception:
        pass
    return None


def _try_match_svd_by_dim(base_dir: str, expected_dim: int, debug: bool = False) -> str | None:
    try:
        files = os.listdir(base_dir)
    except Exception:
        files = []
    candidates = [f for f in files if f.startswith('tfidf_svd_') and f.endswith('.pkl')]
    for f in candidates:
        try:
            obj = joblib.load(os.path.join(base_dir, f))
            comp = _svd_components(obj)
            if debug:
                print('[DEBUG] SVD candidate', f, 'components =', comp)
            if comp == expected_dim:
                return f
        except Exception:
            continue
    return None


def _auto_infer_predictor(base_dir: str, cfg: dict) -> dict | None:
    """Try to infer a deployable KMeans model and embedding artifacts when config is incomplete.
    Prefers TF‑IDF, else W2V. Returns an updated cfg dict or None if not found.
    """
    # Scan for candidate model pkls in folder
    try:
        files = os.listdir(base_dir)
    except Exception:
        files = []
    tfidf_models = [f for f in files if f.lower().endswith('.pkl') and ('kmeans' in f.lower()) and ('tfidf' in f.lower() or 'tf-idf' in f.lower())]
    w2v_models = [f for f in files if f.lower().endswith('.pkl') and ('kmeans' in f.lower()) and ('w2v' in f.lower() or 'word2vec' in f.lower())]
    chosen = None
    chosen_kind = None
    if tfidf_models:
        chosen = tfidf_models[0]
        chosen_kind = 'tfidf_svd'
    elif w2v_models:
        chosen = w2v_models[0]
        chosen_kind = 'w2v'
    if not chosen:
        return None

    new_cfg = dict(cfg)
    new_cfg['model_pkl'] = chosen
    if chosen_kind == 'tfidf_svd':
        new_cfg['embedding_name'] = 'tfidf_svd'
        # vectorizer
        vec = 'tfidf_vectorizer_ngram1.pkl'
        if os.path.exists(os.path.join(base_dir, vec)):
            new_cfg['embedding_pkl'] = vec
        # svd
        svd = ''
        for f in files:
            if f.startswith('tfidf_svd_') and f.endswith('.pkl'):
                svd = f
                break
        if svd:
            new_cfg['svd_pkl'] = svd
    else:
        new_cfg['embedding_name'] = 'w2v'
        w2v = 'word2vec_model.pkl'
        if os.path.exists(os.path.join(base_dir, w2v)):
            new_cfg['embedding_pkl'] = w2v
    return new_cfg


def _infer_from_results(base_dir: str, cfg: dict) -> dict | None:
    """Try to infer a KMeans model name from the results workbook columns and map to a .pkl."""
    try:
        res = cfg.get('results_file_name') or 'clustering_Results.xlsx'
        res_path = _resolve(base_dir, res)
        if not os.path.exists(res_path):
            return None
        xl = pd.ExcelFile(res_path)
        # Prefer 'clusters' sheet
        sheet = 'clusters' if 'clusters' in xl.sheet_names else xl.sheet_names[0]
        df = xl.parse(sheet, nrows=1)
        cols = list(df.columns)
        # Prefer TFIDF KMeans column, else W2V KMeans
        def pick(pred):
            for c in cols:
                s = str(c)
                if 'kmeans' in s.lower() and pred(s):
                    return s
            return None
        name = pick(lambda s: 'tfidf' in s.lower()) or pick(lambda s: 'w2v' in s.lower()) or pick(lambda s: True)
        if not name:
            return None
        safe = _safe_name(name)
        guess = f"{safe}.pkl"
        if os.path.exists(os.path.join(base_dir, guess)):
            new_cfg = dict(cfg)
            new_cfg['model_pkl'] = guess
            if 'tfidf' in name.lower():
                new_cfg['embedding_name'] = 'tfidf_svd'
                if os.path.exists(os.path.join(base_dir, 'tfidf_vectorizer_ngram1.pkl')):
                    new_cfg['embedding_pkl'] = 'tfidf_vectorizer_ngram1.pkl'
                for f in os.listdir(base_dir):
                    if f.startswith('tfidf_svd_') and f.endswith('.pkl'):
                        new_cfg['svd_pkl'] = f
                        break
            elif 'w2v' in name.lower():
                new_cfg['embedding_name'] = 'w2v'
                if os.path.exists(os.path.join(base_dir, 'word2vec_model.pkl')):
                    new_cfg['embedding_pkl'] = 'word2vec_model.pkl'
            return new_cfg
    except Exception:
        return None
    return None


def load_embeddings(base_dir: str, cfg: dict):
    name = (cfg.get('embedding_name') or '').lower()
    if name.startswith('tfidf'):
        vec_path = _resolve(base_dir, cfg.get('embedding_pkl', ''))
        if not vec_path or not os.path.exists(vec_path):
            raise FileNotFoundError('TF-IDF vectorizer not found')
        vec = joblib.load(vec_path)
        svd = None
        svd_name = cfg.get('svd_pkl')
        if svd_name:
            svd_path = _resolve(base_dir, svd_name)
            if os.path.exists(svd_path):
                svd = joblib.load(svd_path)
        return ('tfidf_svd', vec, svd)
    if name.startswith('w2v'):
        w2v_path = _resolve(base_dir, cfg.get('embedding_pkl', ''))
        if not w2v_path or not os.path.exists(w2v_path):
            raise FileNotFoundError('Word2Vec model not found')
        w2v = joblib.load(w2v_path)
        return ('w2v', w2v, None)
    raise RuntimeError('Unsupported embedding_name for deployment. Use tfidf_svd or w2v.')


def embed_texts(texts: pd.Series, emb_tuple):
    kind, obj, svd = emb_tuple
    if kind == 'tfidf_svd':
        Xs = obj.transform(texts.astype('U'))
        if svd is not None:
            return svd.transform(Xs)
        return Xs.toarray()
    if kind == 'w2v':
        try:
            from nltk.tokenize import word_tokenize
        except Exception:
            word_tokenize = lambda s: s.split()
        feats = []
        model = obj
        for doc in texts:
            toks = word_tokenize(str(doc))
            vecs = [model.wv[w] for w in toks if w in model.wv]
            if vecs:
                arr = np.asarray(vecs, dtype=np.float32)
                feats.append(arr.mean(axis=0))
            else:
                feats.append(np.zeros(model.vector_size, dtype=np.float32))
        return np.asarray(feats)
    raise RuntimeError('Unknown embedding tuple')


def predict_file(input_path: str, output_path: str, base_dir: str | None = None, cfg_override: dict | None = None, debug: bool = False):
    base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
    cfg = load_config(base_dir)
    if cfg_override:
        # Merge overrides (non-empty only)
        for k, v in cfg_override.items():
            if v:
                # map friendly keys to internal
                if k == 'model':
                    cfg['model_pkl'] = v
                elif k == 'embedding':
                    cfg['embedding_name'] = v
                elif k == 'embedding_pkl':
                    cfg['embedding_pkl'] = v
                elif k == 'svd_pkl':
                    cfg['svd_pkl'] = v
                else:
                    cfg[k] = v
    if debug:
        print('[DEBUG] Base directory:', base_dir)
        print('[DEBUG] Initial config:', cfg)
    # If model_pkl missing, try to infer a deployable model + embedding
    if not cfg.get('model_pkl'):
        maybe = _auto_infer_predictor(base_dir, cfg)
        if maybe:
            cfg = maybe
            if debug:
                print('[DEBUG] Auto-inferred config:', cfg)
        else:
            # Try inferring from results workbook
            maybe2 = _infer_from_results(base_dir, cfg)
            if maybe2:
                cfg = maybe2
                if debug:
                    print('[DEBUG] Inferred from results workbook:', cfg)
    model_path = _resolve(base_dir, cfg.get('model_pkl', ''))
    # Guard: BERT-based persisted models are not supported by this predictor
    try:
        if os.path.basename(cfg.get('model_pkl', '')).upper().find('BERT') != -1:
            raise RuntimeError('BERT-based clustering models are not supported by the predictor. Please choose a TF-IDF or W2V KMeans model and update swayamml.conf (model_pkl_file_name, embedding_name).')
    except Exception:
        pass
    if not model_path or not os.path.isfile(model_path):
        hint = ''
        try:
            if os.path.basename(model_path or '').upper().find('BERT') != -1:
                hint = ' (BERT models are not supported; select a TF-IDF or W2V KMeans model)'
        except Exception:
            pass
        # One more auto-infer attempt before failing
        maybe = _auto_infer_predictor(base_dir, cfg)
        if maybe and maybe.get('model_pkl') and os.path.isfile(os.path.join(base_dir, maybe['model_pkl'])):
            cfg = maybe
            model_path = os.path.join(base_dir, cfg['model_pkl'])
            if debug:
                print('[DEBUG] Auto-inferred on missing path:', cfg)
        else:
            if debug:
                try:
                    print('[DEBUG] Folder listing at', base_dir, ':', os.listdir(base_dir))
                except Exception:
                    pass
            # Clarify if path is a directory
            if model_path and os.path.isdir(model_path):
                raise FileNotFoundError(f"Persisted model is not a file: {model_path}. Please set model_pkl_file_name to a .pkl model file.{hint}")
            raise FileNotFoundError(f"Persisted model not found: {model_path}{hint}")
    model = joblib.load(model_path)
    emb_tuple = load_embeddings(base_dir, cfg)

    # Validate and, if needed, auto-correct SVD to match model expected dimension
    expected = _get_model_expected_dim(model)
    if expected is not None:
        kind, obj, svd = emb_tuple
        if kind == 'tfidf_svd':
            comp = _svd_components(svd) if svd is not None else None
            # If no SVD but model expects smaller dim, try finding a matching SVD
            if (comp is None and expected is not None) or (comp is not None and comp != expected):
                match = _try_match_svd_by_dim(base_dir, expected, debug=debug)
                if match:
                    try:
                        svd2 = joblib.load(os.path.join(base_dir, match))
                        emb_tuple = (kind, obj, svd2)
                        cfg['svd_pkl'] = match
                        if debug:
                            print('[DEBUG] Adjusted SVD to', match, 'to match expected dim =', expected)
                    except Exception:
                        pass
                else:
                    # Provide a friendly hint before proceeding to fail at predict
                    if debug:
                        print(f"[DEBUG] No SVD artifact matches model expected dim {expected}. Current SVD components = {comp}")

    # Read input
    _, ext = os.path.splitext(input_path.lower())
    if ext == '.csv':
        df = pd.read_csv(input_path)
    elif ext in ('.xlsx', '.xls'):
        df = pd.read_excel(input_path)
    else:
        raise ValueError('Unsupported input file type. Use CSV/XLSX.')

    # Build Cleaned_data if missing
    if 'Cleaned_data' not in df.columns:
        obj_cols = [c for c in df.columns if df[c].dtype == object]
        if obj_cols:
            df['Cleaned_data'] = df[obj_cols].astype(str).agg(' '.join, axis=1)
        else:
            raise RuntimeError('No Cleaned_data and no textual columns to build it from.')

    X = embed_texts(df['Cleaned_data'], emb_tuple)
    # Early dimension check to raise a clearer error
    exp = _get_model_expected_dim(model)
    if exp is not None and X.shape[1] != exp:
        raise RuntimeError(
            f"Feature dimension mismatch: got {X.shape[1]}, model expects {exp}. "
            f"Ensure swayamml.conf points to the correct embedding artifacts. For TF-IDF, set svd_pkl_file_name to the SVD with {exp} components."
        )
    try:
        preds = model.predict(X)
    except Exception as e:
        raise RuntimeError(f"Model does not support predict() or failed: {e}")

    out_df = df.copy()
    out_df['predicted_cluster'] = preds

    if output_path.lower().endswith('.xlsx'):
        with pd.ExcelWriter(output_path, engine='openpyxl') as w:
            out_df.to_excel(w, index=False)
    else:
        out_df.to_csv(output_path, index=False)


def main(argv=None):
    p = argparse.ArgumentParser(description='NLP Clustering Predictor (KMeans)')
    # Support both flags and positional args
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/XLSX')
    p.add_argument('positional_output', nargs='?', help='Path to output assignments CSV/XLSX')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/XLSX')
    p.add_argument('--output', dest='flag_output', help='Path to output assignments CSV/XLSX')
    # Optional overrides to bypass/incomplete config
    p.add_argument('--model', help='Override: model .pkl filename in this folder (e.g., KMeans_k_3_[TFIDF].pkl)')
    p.add_argument('--embedding', choices=['tfidf_svd', 'w2v'], help='Override: embedding type')
    p.add_argument('--embedding-pkl', help='Override: embedding artifact filename (tfidf_vectorizer_ngram1.pkl or word2vec_model.pkl)')
    p.add_argument('--svd-pkl', help='Override: SVD filename (required for tfidf_svd)')
    p.add_argument('--debug', action='store_true', help='Print diagnostic info (resolved config, shapes)')
    args = p.parse_args(argv)

    input_path = args.flag_input or args.positional_input
    if not input_path:
        p.error('An input file is required. Provide --input or a positional input path.')

    output_path = args.flag_output or args.positional_output
    if not output_path:
        base, ext = os.path.splitext(input_path)
        if ext.lower() == '.csv':
            output_path = f"{base}_assignments.csv"
        elif ext.lower() in ('.xlsx', '.xls'):
            output_path = f"{base}_assignments.xlsx"
        else:
            output_path = f"{base}_assignments.csv"

    overrides = {
        'model': args.model,
        'embedding': args.embedding,
        'embedding_pkl': args.embedding_pkl,
        'svd_pkl': args.svd_pkl,
    }
    predict_file(input_path, output_path, cfg_override=overrides, debug=args.debug)


if __name__ == '__main__':
    sys.exit(main())
