"""
Deployment script for NLP Classification.

Usage (PowerShell):
    # Backward-compatible flags
    python deployment_script_classification.py --input data.csv --output preds.csv

    # Or just provide the input path (output will default to data_preds.csv or data_preds.xlsx)
    python deployment_script_classification.py data.csv

Expects in the same folder:
    - swayamml.conf with keys:
      embedding_name (tfidf_svd|w2v)
      model_pkl_file_name
      label_pkl_file_name
      embedding_pkl_file_name (tfidf_vectorizer_ngram1.pkl or word2vec_model.pkl)
      svd_pkl_file_name (only for tfidf_svd)
  - the referenced model and embedding artifacts
"""
import os
import sys
import argparse
import configparser
import joblib
import pandas as pd
import numpy as np
from typing import Optional


def _as_list(csv_str: str) -> list:
    if not csv_str:
        return []
    return [c.strip() for c in str(csv_str).split(',') if str(c).strip()]


def load_config(base_dir: str) -> dict:
    cfg_path = os.path.join(base_dir, 'swayamml.conf')
    if not os.path.exists(cfg_path):
        raise FileNotFoundError(f"Config not found: {cfg_path}")
    cp = configparser.ConfigParser()
    cp.read(cfg_path)
    d = cp['DEFAULT'] if 'DEFAULT' in cp else {}
    return {
        'embedding_name': d.get('embedding_name', ''),
        'model': d.get('model_pkl_file_name', ''),
        'label': d.get('label_pkl_file_name', ''),
        'embedding_pkl': d.get('embedding_pkl_file_name', ''),
        'svd_pkl': d.get('svd_pkl_file_name', ''),
    }


def load_embeddings(base_dir: str, cfg: dict):
    name = (cfg.get('embedding_name') or '').lower()
    emb = None
    if name.startswith('tfidf'):
        # Load TF-IDF vectorizer and optional SVD
        vec_path = os.path.join(base_dir, cfg.get('embedding_pkl', ''))
        if not vec_path or not os.path.exists(vec_path):
            raise FileNotFoundError('TF-IDF vectorizer not found')
        vec = joblib.load(vec_path)
        svd_name = cfg.get('svd_pkl')
        if not svd_name:
            raise FileNotFoundError('svd_pkl_file_name missing in config for tfidf_svd; please include the saved SVD .pkl')
        svd_path = os.path.join(base_dir, svd_name)
        if not os.path.exists(svd_path):
            raise FileNotFoundError(f'SVD file not found: {svd_path}')
        svd = joblib.load(svd_path)
        return ('tfidf_svd', vec, svd)
    if name.startswith('w2v'):
        w2v_path = os.path.join(base_dir, cfg.get('embedding_pkl', ''))
        if not w2v_path or not os.path.exists(w2v_path):
            raise FileNotFoundError('Word2Vec model not found')
        w2v = joblib.load(w2v_path)
        return ('w2v', w2v, None)
    raise RuntimeError('Unsupported embedding_name for deployment. Use tfidf_svd or w2v.')


def embed_texts(texts: pd.Series, emb_tuple, debug: bool = False):
    kind, obj, svd = emb_tuple
    if kind == 'tfidf_svd':
        Xs = obj.transform(texts.astype('U'))
        if debug:
            try:
                print(f"[DEBUG] TF-IDF matrix shape: {getattr(Xs, 'shape', None)}")
            except Exception:
                pass
        out = svd.transform(Xs)
        if debug:
            print(f"[DEBUG] After SVD shape: {out.shape}")
        return out
    if kind == 'w2v':
        try:
            from nltk.tokenize import word_tokenize
        except Exception:
            word_tokenize = lambda s: s.split()
        feats = []
        model = obj
        for doc in texts:
            toks = word_tokenize(str(doc))
            vecs = [model.wv[w] for w in toks if w in model.wv]
            if vecs:
                arr = np.asarray(vecs, dtype=np.float32)
                feats.append(arr.mean(axis=0))
            else:
                feats.append(np.zeros(model.vector_size, dtype=np.float32))
        out = np.asarray(feats)
        if debug:
            print(f"[DEBUG] W2V feature matrix shape: {out.shape}")
        return out
    raise RuntimeError('Unknown embedding tuple')


def predict_file(input_path: str, output_path: str, base_dir: Optional[str] = None, debug: bool = False):
    base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
    cfg = load_config(base_dir)
    model = joblib.load(os.path.join(base_dir, cfg['model']))
    label_enc = joblib.load(os.path.join(base_dir, cfg['label']))
    emb_tuple = load_embeddings(base_dir, cfg)
    if debug:
        print(f"[DEBUG] Embedding: {cfg.get('embedding_name')}  | Model: {cfg.get('model')}  | Label: {cfg.get('label')}")

    # Read input
    _, ext = os.path.splitext(input_path.lower())
    if ext == '.csv':
        df = pd.read_csv(input_path)
    elif ext in ('.xlsx', '.xls'):
        df = pd.read_excel(input_path)
    else:
        raise ValueError('Unsupported input file type. Use CSV/XLSX.')

    # Build Cleaned_data if missing (simple join of object columns)
    if 'Cleaned_data' not in df.columns:
        obj_cols = [c for c in df.columns if df[c].dtype == object]
        if obj_cols:
            df['Cleaned_data'] = df[obj_cols].astype(str).agg(' '.join, axis=1)
        else:
            raise RuntimeError('No Cleaned_data and no textual columns to build it from.')

    X = embed_texts(df['Cleaned_data'], emb_tuple, debug=debug)
    if debug:
        expected = getattr(model, 'n_features_in_', 'unknown')
        print(f"[DEBUG] Final X shape: {getattr(X, 'shape', None)} | Model expects n_features_in_: {expected}")
    preds = model.predict(X)
    out_df = df.copy()
    out_df['predicted_label_encoded'] = preds
    try:
        out_df['predicted_label'] = label_enc.inverse_transform(preds)
    except Exception:
        pass

    # Try proba if available
    try:
        if hasattr(model, 'predict_proba'):
            proba = model.predict_proba(X)
            # top-1 probability
            mx = np.max(proba, axis=1)
            out_df['predicted_probability'] = mx
    except Exception:
        pass

    if output_path.lower().endswith('.xlsx'):
        with pd.ExcelWriter(output_path, engine='openpyxl') as w:
            out_df.to_excel(w, index=False)
    else:
        out_df.to_csv(output_path, index=False)


def main(argv=None):
    p = argparse.ArgumentParser(description='NLP Classification Deployment Predictor')
    # Support both flags and positional args (positional are optional)
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/XLSX')
    p.add_argument('positional_output', nargs='?', help='Path to output CSV/XLSX')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/XLSX')
    p.add_argument('--output', dest='flag_output', help='Path to output predictions CSV/XLSX')
    p.add_argument('--debug', action='store_true', help='Print embedding and feature shapes to diagnose mismatches')
    args = p.parse_args(argv)

    # Resolve input path: flag wins over positional
    input_path = args.flag_input or args.positional_input
    if not input_path:
        p.error('An input file is required. Provide --input or a positional input path.')

    # Resolve output path: flag wins; else positional; else default based on input
    output_path = args.flag_output or args.positional_output
    if not output_path:
        base, ext = os.path.splitext(input_path)
        if ext.lower() == '.csv':
            output_path = f"{base}_preds.csv"
        elif ext.lower() in ('.xlsx', '.xls'):
            output_path = f"{base}_preds.xlsx"
        else:
            # unknown extension, default to CSV
            output_path = f"{base}_preds.csv"

    predict_file(input_path, output_path, debug=args.debug)


if __name__ == '__main__':
    sys.exit(main())
