import os
import shutil
import pandas as pd
import time
import configparser
import logging
import traceback
import numpy as np
from scipy import sparse as _sps
from sklearn.preprocessing import PolynomialFeatures

from sklearn.model_selection import train_test_split

from log_util import setup_logger, remove_logger
from utils import copy_deployment_collaterals
from preprocess import TextPreprocess, FeaturePreprocess
from embeddings import TextEmbeddings
from modelling import Regression
from output import NLPRegressionOutput, NonNLPRegressionOutput

def regression_process(file, fields, target, output_folder, selection_policy: str | None = None):
    start_time = time.time()
    summary = None
    customer_files = []
    num_models = 0
    feature_importance = None

    processing_logger = {
        "process_complete": False,
        "preprocessing_complete": False,
        "num_rows_dropped_in_preprocessing": 0,
        "row_with_missing_target_dropped": 0,
        "duplicate_input_rows_dropped": 0,
        "num_catg_cols_present": 0,
        "textual_cols": [],
        "features": [],
        "feature_importance": None
    }

    pd.set_option('display.max_columns', None)

    _, file_extension = os.path.splitext(file)
    if file_extension == ".csv":
        Data = pd.read_csv(file)
    elif file_extension == ".xlsx" or file_extension == ".xls":
        Data = pd.read_excel(file)
    else:
        raise ValueError("Unsupported file format")

    initial_rows = Data.shape[0]

    Data = Data.dropna(subset=[target])
    processing_logger["row_with_missing_target_dropped"] = initial_rows - Data.shape[0]

    before_dup = Data.shape[0]
    Data = Data.drop_duplicates(subset=[*fields, target])
    processing_logger["duplicate_input_rows_dropped"] = before_dup - Data.shape[0]

    Data.reset_index(drop=True, inplace=True)

    train_df, test_df = train_test_split(Data[[*fields, target]], test_size=0.2, random_state=42)

    # Heuristic detection of true textual columns (exclude categorical short codes)
    def _detect_textual_columns(df: pd.DataFrame, candidate_cols: list[str]) -> list[str]:
        text_cols: list[str] = []
        for c in candidate_cols:
            try:
                s = df[c].dropna()
                if s.empty:
                    continue
                s = s.astype(str)
                n = len(s)
                unique_ratio = s.nunique() / max(n, 1)
                avg_len = float(s.str.len().mean())
                ws_ratio = float(s.str.contains(r"\s").mean())
                # numeric-like ratio
                num_like_ratio = pd.to_numeric(s, errors='coerce').notna().mean()
                if ((unique_ratio >= 0.3) and (avg_len >= 15 or ws_ratio >= 0.3)) and (num_like_ratio < 0.5):
                    text_cols.append(c)
            except Exception:
                continue
        return text_cols

    obj_cols = [c for c in fields if Data[c].dtype == object]
    detected_textual_cols = _detect_textual_columns(Data, obj_cols)

    Results = pd.DataFrame(columns=[
        "Model Name",
        "best_params",
        "validation_mean_squared_error",
        "validation_mean_absolute_error",
        "validation_r2_score",
        "validation_adjusted_r2_score",
    ])
    Validation_Results = {}  # initialize validation container

    modelling = Regression()
    modelling.set_output_folder(output_folder)

    feature_preprocess = FeaturePreprocess()
    preprocessed_train_data, textual_cols, datetime_cols, string_to_num_cols, categorical_cols_encoded, numerical_cols_scaled = feature_preprocess.preprocess_features(train_df[fields])
    # Override textual_cols with detected NLP text columns
    textual_cols = detected_textual_cols

    target_aligned = train_df.loc[preprocessed_train_data.index, target]
    train_df_processed = pd.concat([preprocessed_train_data, target_aligned], axis=1)

    if numerical_cols_scaled:
        # Use the proper test-time preprocessing API and correct argument order
        preprocessed_test_data = feature_preprocess.preprocess_text_data(
            test_df[fields],
            datetime_cols,
            string_to_num_cols,
            textual_cols,
            numerical_cols_scaled,
        )
        target_aligned = test_df.loc[preprocessed_test_data.index, target]
        test_df_processed = pd.concat([preprocessed_test_data, target_aligned], axis=1)
    else:
        test_df_processed = test_df.copy()

    features = []
    if numerical_cols_scaled:
        processing_logger["num_catg_cols_present"] = True
        encoding_pkl_file_name = "encoding.pkl"
        imputer_pkl_file_name = "imputer.pkl"
        scaler_pkl_file_name = "scaler.pkl"
        encoding_pkl_file = os.path.join(output_folder, encoding_pkl_file_name)
        imputer_pkl_file = os.path.join(output_folder, imputer_pkl_file_name)
        scaler_pkl_file = os.path.join(output_folder, scaler_pkl_file_name)
        feature_preprocess.save_encodings(encoding_pkl_file, imputer_pkl_file, scaler_pkl_file)

    # Build features: original numeric + encoded categoricals (categoricals are encoded, not scaled)
    numeric_feats = [c for c in (numerical_cols_scaled.keys() if numerical_cols_scaled else []) if c not in (textual_cols or [])]
    categorical_feats = [c for c in (categorical_cols_encoded.keys() if isinstance(categorical_cols_encoded, dict) else []) if c not in (textual_cols or [])]
    features = list(dict.fromkeys([*numeric_feats, *categorical_feats]))
    processing_logger["features"] = features

    # ---- Feature engineering: Polynomial interactions on numeric columns (degree=2) ----
    # Only if we have at least 2 numeric features
    engineered_feature_names = []
    def _to_dense(m):
        try:
            if hasattr(m, 'toarray'):
                return m.toarray()
            if hasattr(m, 'todense'):
                import numpy as _np
                return _np.asarray(m.todense())
            import numpy as _np
            return _np.asarray(m)
        except Exception:
            import numpy as _np
            return _np.asarray(m)
    try:
        if numeric_feats and len(numeric_feats) >= 1:
            poly = PolynomialFeatures(degree=2, include_bias=False, interaction_only=False)
            # Fit on train numeric features
            Xn_train = train_df_processed[numeric_feats].to_numpy()
            Xn_test = test_df_processed[numeric_feats].to_numpy()
            poly_train = _to_dense(poly.fit_transform(Xn_train))
            poly_test = _to_dense(poly.transform(Xn_test))
            # Get output names and keep only engineered columns beyond the originals
            try:
                out_names = list(poly.get_feature_names_out(input_features=numeric_feats))
            except Exception:
                # Fallback names x0, x1, x0^2, x0 x1 ... then map where possible
                out_names = []
                base = list(numeric_feats)
                for i in range(poly_train.shape[1]):
                    if i < len(base):
                        out_names.append(base[i])
                    else:
                        out_names.append(f"poly_{i}")
            # engineered indices exclude the first len(numeric_feats) originals
            start_idx = len(numeric_feats)
            new_names = []
            for name in out_names[start_idx:]:
                # sanitize names: replace ' ' with '*', '^2' with '_sq'
                nm = str(name).replace(' ', '*').replace('^2', '_sq')
                # also normalize parentheses if any
                nm = nm.replace('(', '').replace(')', '')
                new_names.append(nm)
            if new_names:
                eng_train = poly_train[:, start_idx:]
                eng_test = poly_test[:, start_idx:]
                # Optional: cap engineered features by selecting top-K by variance to guard performance
                try:
                    max_engineered = int(os.environ.get('MAX_ENGINEERED_FEATURES', '300'))
                except Exception:
                    max_engineered = 300
                try:
                    import numpy as _np
                    variances = _np.var(eng_train, axis=0)
                    order = _np.argsort(-variances)  # descending
                    if eng_train.shape[1] > max_engineered:
                        order = order[:max_engineered]
                        eng_train = eng_train[:, order]
                        eng_test = eng_test[:, order]
                        new_names = [new_names[i] for i in list(order)]
                except Exception:
                    pass
                # Assign into dataframes
                for j, col in enumerate(new_names):
                    # avoid collision
                    cname = col
                    k = 1
                    while cname in train_df_processed.columns:
                        cname = f"{col}_{k}"
                        k += 1
                    train_df_processed[cname] = eng_train[:, j]
                    test_df_processed[cname] = eng_test[:, j]
                    engineered_feature_names.append(cname)
                # Extend features set
                features = list(dict.fromkeys([*features, *engineered_feature_names]))
                processing_logger["features"] = features
                processing_logger["engineered_features_added"] = engineered_feature_names
    except Exception as fe_ex:
        logging.warning("Polynomial feature engineering skipped due to error: %s", fe_ex)
        logging.debug(traceback.format_exc())

    # ---- Feature selection: Greedy backward elimination by adjusted R2 (LinearRegression) ----
    def _adj_r2_for_feats(df_x: pd.DataFrame, y: pd.Series, feat_list: list[str]) -> float:
        try:
            from sklearn.linear_model import LinearRegression as _LR
            X = df_x[feat_list].to_numpy()
            model = _LR()
            model.fit(X, y)
            r2 = model.score(X, y)
            n = float(X.shape[0])
            p = float(X.shape[1]) if X.ndim == 2 else 1.0
            adj = 1.0 - (1.0 - float(r2)) * (n - 1.0) / max(n - p - 1.0, 1.0)
            return float(adj)
        except Exception:
            return float('nan')

    try:
        if features and len(features) > 1:
            y_train_series = train_df_processed[target]
            current_feats = list(features)
            baseline = _adj_r2_for_feats(train_df_processed, y_train_series, current_feats)
            improved = True
            removed = []
            tol = 1e-5
            while improved and len(current_feats) > 1:
                improved = False
                best_gain = 0.0
                best_to_drop = None
                for f in list(current_feats):
                    trial = [g for g in current_feats if g != f]
                    adj = _adj_r2_for_feats(train_df_processed, y_train_series, trial)
                    if not np.isnan(adj) and adj > baseline + tol and (adj - baseline) > best_gain:
                        best_gain = float(adj - baseline)
                        best_to_drop = f
                        best_new = adj
                if best_to_drop is not None:
                    current_feats.remove(best_to_drop)
                    removed.append(best_to_drop)
                    baseline = best_new
                    improved = True
            if removed:
                features = current_feats
                processing_logger["features"] = features
                processing_logger["features_removed_by_adj_r2"] = removed
    except Exception as fs_ex:
        logging.warning("Backward elimination skipped due to error: %s", fs_ex)
        logging.debug(traceback.format_exc())

    processing_logger["textual_cols"] = textual_cols

    # Track produced artifacts for UI download list
    produced_files = []

    # Helper to pick best model family by CV results if requested
    def _best_model_by_cv(grid_rows_list):
        try:
            import pandas as _pd
            gr = _pd.DataFrame(grid_rows_list)
            if gr.empty:
                return None
            # Prefer highest mean_test_r2; if rank available, use smallest rank
            if 'rank_test_r2' in gr.columns and gr['rank_test_r2'].notna().any():
                row = gr.sort_values('rank_test_r2', ascending=True).iloc[0]
                return str(row.get('model_family')) if row is not None else None
            if 'mean_test_r2' in gr.columns and gr['mean_test_r2'].notna().any():
                row = gr.sort_values('mean_test_r2', ascending=False).iloc[0]
                return str(row.get('model_family')) if row is not None else None
        except Exception:
            pass
        return None

    # NLP branch
    if textual_cols:
        # create concatenated text field from original raw columns, aligned to processed indices
        def _safe_row_join(row):
            return " ".join([str(v) for v in row if pd.notna(v)])
        raw_train_text = train_df.loc[train_df_processed.index, textual_cols]
        raw_test_text = test_df.loc[test_df_processed.index, textual_cols]
        train_df_processed["concatenated_fields"] = raw_train_text.apply(_safe_row_join, axis=1)
        test_df_processed["concatenated_fields"] = raw_test_text.apply(_safe_row_join, axis=1)
        num_rows_before_text = train_df_processed.shape[0]
        text_preprocess = TextPreprocess()
        selected_columns = [*features, target]

        train_df_processed = text_preprocess.preprocess_text(train_df_processed, selected_columns, "concatenated_fields")
        train_df_processed = train_df_processed.drop_duplicates(subset=["Cleaned_data", target])
        processing_logger["num_rows_dropped_in_text_preprocessing"] = num_rows_before_text - train_df_processed.shape[0]

        selected_columns = [*features, *textual_cols, target]
        test_df_processed = text_preprocess.preprocess_text(test_df_processed, selected_columns, "concatenated_fields")
        test_df_processed = test_df_processed.drop_duplicates(subset=["Cleaned_data", target])
        processing_logger["preprocessing_complete"] = True

        embeddings = TextEmbeddings()
        embeddings.set_output_folder(output_folder)

        tfidf_1gram_train = embeddings.embeddings_tfidf(train_df_processed["Cleaned_data"], ngram=1, sparse_return=False)
        tfidf_1gram_test = embeddings.embeddings_tfidf_test_data(test_df_processed["Cleaned_data"], sparse_return=False)
        # ensure dense numpy arrays
        tfidf_1gram_train = np.asarray(tfidf_1gram_train)
        tfidf_1gram_test = np.asarray(tfidf_1gram_test)

        w2v_train = embeddings.embeddings_word2vec(train_df_processed["Cleaned_data"])
        w2v_test = embeddings.embeddings_word2vec_test_data(test_df_processed["Cleaned_data"])

        bert_train = embeddings.embeddings_bert(train_df_processed["Cleaned_data"])
        bert_test = embeddings.embeddings_bert(test_df_processed["Cleaned_data"])
        bert_train = np.asarray(bert_train)
        bert_test = np.asarray(bert_test)

        if features:
            num_cols = list(features)
            tfidf_1gram_train = np.concatenate((tfidf_1gram_train, train_df_processed[num_cols].values), axis=1)
            tfidf_1gram_test = np.concatenate((tfidf_1gram_test, test_df_processed[num_cols].values), axis=1)

            w2v_train = np.concatenate((w2v_train, train_df_processed[num_cols].values), axis=1)
            w2v_test = np.concatenate((w2v_test, test_df_processed[num_cols].values), axis=1)

            bert_train = np.concatenate((bert_train, train_df_processed[num_cols].values), axis=1)
            bert_test = np.concatenate((bert_test, test_df_processed[num_cols].values), axis=1)

        y_train = train_df_processed[target]
        y_test = test_df_processed[target]

        # Run a set of models for each embedding (Validation_Results is passed through)
        Results, Validation_Results = modelling.linear_regression(
            Results,
            Validation_Results,
            tfidf_1gram_train,
            y_train,
            tfidf_1gram_test,
            y_test,
            model_name="linear_regression_tfidf"
        )

        Results, Validation_Results = modelling.decision_tree_regressor(
            Results,
            Validation_Results,
            tfidf_1gram_train,
            y_train,
            tfidf_1gram_test,
            y_test,
            model_name="decision_tree_regressor_tfidf"
        )

        Results, Validation_Results = modelling.random_forest_regressor(
            Results,
            Validation_Results,
            tfidf_1gram_train,
            y_train,
            tfidf_1gram_test,
            y_test,
            model_name="random_forest_regressor_tfidf"
        )

        Results, Validation_Results = modelling.xgboost_regressor(
            Results,
            Validation_Results,
            tfidf_1gram_train,
            y_train,
            tfidf_1gram_test,
            y_test,
            model_name="xgboost_regressor_tfidf"
        )

        Results, Validation_Results = modelling.support_vector_regressor(
            Results,
            Validation_Results,
            tfidf_1gram_train,
            y_train,
            tfidf_1gram_test,
            y_test,
            model_name="support_vector_regressor_tfidf"
        )

        Results, Validation_Results = modelling.bayesian_ridge_regressor(
            Results,
            Validation_Results,
            tfidf_1gram_train,
            y_train,
            tfidf_1gram_test,
            y_test,
            model_name="bayesian_ridge_regressor_tfidf"
        )

        # word2vec models
        Results, Validation_Results = modelling.linear_regression(
            Results,
            Validation_Results,
            w2v_train,
            y_train,
            w2v_test,
            y_test,
            model_name="linear_regression_w2v"
        )

        Results, Validation_Results = modelling.decision_tree_regressor(
            Results,
            Validation_Results,
            w2v_train,
            y_train,
            w2v_test,
            y_test,
            model_name="decision_tree_regressor_w2v"
        )

        Results, Validation_Results = modelling.random_forest_regressor(
            Results,
            Validation_Results,
            w2v_train,
            y_train,
            w2v_test,
            y_test,
            model_name="random_forest_regressor_w2v"
        )

        Results, Validation_Results = modelling.xgboost_regressor(
            Results,
            Validation_Results,
            w2v_train,
            y_train,
            w2v_test,
            y_test,
            model_name="xgboost_regressor_w2v"
        )

        Results, Validation_Results = modelling.support_vector_regressor(
            Results,
            Validation_Results,
            w2v_train,
            y_train,
            w2v_test,
            y_test,
            model_name="support_vector_regressor_w2v"
        )

        Results, Validation_Results = modelling.bayesian_ridge_regressor(
            Results,
            Validation_Results,
            w2v_train,
            y_train,
            w2v_test,
            y_test,
            model_name="bayesian_ridge_regressor_w2v"
        )

        # bert models
        Results, Validation_Results = modelling.linear_regression(
            Results,
            Validation_Results,
            bert_train,
            y_train,
            bert_test,
            y_test,
            model_name="linear_regression_bert"
        )

        Results, Validation_Results = modelling.decision_tree_regressor(
            Results,
            Validation_Results,
            bert_train,
            y_train,
            bert_test,
            y_test,
            model_name="decision_tree_regressor_bert"
        )

        Results, Validation_Results = modelling.random_forest_regressor(
            Results,
            Validation_Results,
            bert_train,
            y_train,
            bert_test,
            y_test,
            model_name="random_forest_regressor_bert"
        )

        Results, Validation_Results = modelling.xgboost_regressor(
            Results,
            Validation_Results,
            bert_train,
            y_train,
            bert_test,
            y_test,
            model_name="xgboost_regressor_bert"
        )

        Results, Validation_Results = modelling.support_vector_regressor(
            Results,
            Validation_Results,
            bert_train,
            y_train,
            bert_test,
            y_test,
            model_name="support_vector_regressor_bert"
        )

        # Output for NLP models
        output = NLPRegressionOutput()
        output_result_file = os.path.join(output_folder, "regression_results.xlsx")
        # First call to generate outputs (we will add run_config after knowing model filename)
        # If selection policy is CV, compute the best model family by CV to override selection
        selected_model_override = _best_model_by_cv(getattr(modelling, 'grid_rows', None)) if (selection_policy == 'cv') else None
        summary, model_pkl_file_name, embedding_name = output.generate_regression_excel_output(
            Results, Validation_Results, output_result_file,
            total_combinations=getattr(modelling, 'total_param_combinations', None),
            grid_rows=getattr(modelling, 'grid_rows', None),
            final_features=features,
            selected_model_override=selected_model_override
        )
        # Append run_config sheet with resolved artifact names
        try:
            run_config = {
                'inputs': list(fields),
                'target': target,
                'final_features': list(features),
                'artifacts': [
                    'encoding.pkl', 'imputer.pkl', 'scaler.pkl',
                    'tfidf_vectorizer_ngram1.pkl', 'word2vec_model.pkl',
                    str(model_pkl_file_name or '').strip()
                ]
            }
            # Re-open writer minimally to append run_config sheet
            import pandas as _pd
            with _pd.ExcelWriter(output_result_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                rc = _pd.DataFrame([
                    { 'key': k, 'value': ', '.join(v) if isinstance(v, (list, tuple)) else v }
                    for k, v in run_config.items()
                ])
                rc.to_excel(writer, sheet_name='run_config', index=False)
        except Exception:
            pass
        # track result file and model
        if output_result_file:
            produced_files.append(os.path.basename(output_result_file))
        if model_pkl_file_name:
            produced_files.append(model_pkl_file_name)

        # choose embedding file name based on embedding_name
        embedding_pkl_file_name = None
        if embedding_name:
            if "tfidf1gram" in embedding_name:
                embedding_pkl_file_name = "tfidf_vectorizer_ngram1.pkl"
            elif "w2v" in embedding_name:
                embedding_pkl_file_name = "word2vec_model.pkl"
            elif "bert" in embedding_name:
                embedding_pkl_file_name = "bert_model.pkl"

        sample_prediction_file_name = "sample_prediction.xlsx"
        if model_pkl_file_name and embedding_name:
            output.generate_test_prediction_excel(
                test_df_processed, embedding_name, model_pkl_file_name, embedding_pkl_file_name, embeddings, numerical_cols_scaled, textual_cols, features, target, output_folder, sample_prediction_file_name,
            )
            produced_files.append(sample_prediction_file_name)

        # count total hyperparameter combinations evaluated across models
        try:
            num_models = int(getattr(modelling, 'total_param_combinations', 0)) or int(Results.shape[0])
        except Exception:
            num_models = Results.shape[0]

    # Numerical-only branch (no textual columns)
    if (numerical_cols_scaled or categorical_cols_encoded) and not textual_cols:
        train_data = train_df_processed[list(features)]
        train_target = train_df_processed[target].values

        test_data = test_df_processed[list(features)]
        test_target = test_df_processed[target].values

        Results, Validation_Results = modelling.decision_tree_regressor(
            Results,
            Validation_Results,
            train_data,
            train_target,
            test_data,
            test_target,
            model_name="decision_tree_regressor_numerical"
        )

        Results, Validation_Results = modelling.random_forest_regressor(
            Results,
            Validation_Results,
            train_data,
            train_target,
            test_data,
            test_target,
            model_name="random_forest_regressor_numerical"
        )

        Results, Validation_Results = modelling.support_vector_regressor(
            Results,
            Validation_Results,
            train_data,
            train_target,
            test_data,
            test_target,
            model_name="support_vector_regressor_numerical"
        )

        Results, Validation_Results = modelling.xgboost_regressor(
            Results,
            Validation_Results,
            train_data,
            train_target,
            test_data,
            test_target,
            model_name="xgboost_regressor_numerical"
        )

        Results, Validation_Results = modelling.bayesian_ridge_regressor(
            Results,
            Validation_Results,
            train_data,
            train_target,
            test_data,
            test_target,
            model_name="bayesian_ridge_regressor_numerical"
        )

        try:
            num_models = int(getattr(modelling, 'total_param_combinations', 0)) or int(Results.shape[0])
        except Exception:
            num_models = Results.shape[0]
        output = NonNLPRegressionOutput()
        output_result_file = os.path.join(output_folder, "regression_results_numerical.xlsx")
        selected_model_override = _best_model_by_cv(getattr(modelling, 'grid_rows', None)) if (selection_policy == 'cv') else None
        summary, model_pkl_file_name, embedding_name = output.generate_regression_excel_output(
            Results, Validation_Results, output_result_file,
            total_combinations=getattr(modelling, 'total_param_combinations', None),
            grid_rows=getattr(modelling, 'grid_rows', None),
            final_features=features,
            selected_model_override=selected_model_override
        )
        # Append run_config for non-NLP
        try:
            run_config = {
                'inputs': list(fields),
                'target': target,
                'final_features': list(features),
                'artifacts': [ 'encoding.pkl', 'imputer.pkl', 'scaler.pkl', str(model_pkl_file_name or '').strip() ]
            }
            import pandas as _pd
            with _pd.ExcelWriter(output_result_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                rc = _pd.DataFrame([
                    { 'key': k, 'value': ', '.join(v) if isinstance(v, (list, tuple)) else v }
                    for k, v in run_config.items()
                ])
                rc.to_excel(writer, sheet_name='run_config', index=False)
        except Exception:
            pass
        # track result file and model
        if output_result_file:
            produced_files.append(os.path.basename(output_result_file))
        if model_pkl_file_name:
            produced_files.append(model_pkl_file_name)
        if model_pkl_file_name:
            feature_importance = output.generate_feature_importance(features, output_folder, model_pkl_file_name)
            if feature_importance:
                produced_files.append(os.path.basename(feature_importance))

        # Generate SHAP values
        if model_pkl_file_name:
            shap_values = output.generate_shap_values(train_data, model_pkl_file_name, output_folder)

        sample_prediction_file_name = "sample_prediction_numerical.xlsx"
        if model_pkl_file_name:
            output.generate_test_prediction_excel(
                test_df_processed, model_pkl_file_name, numerical_cols_scaled, features, target, output_folder, sample_prediction_file_name
            )
            produced_files.append(sample_prediction_file_name)

    # Prepare deployment artifacts depending on which data types present
    try:
        if textual_cols and numerical_cols_scaled:
            copy_deployment_collaterals(source_dir='.', target_dir=output_folder, model="Mixed Regression")
            preprocess_script = "preprocess.py"
            output_preprocess_script = os.path.join(output_folder, preprocess_script)
            shutil.copy(preprocess_script, output_preprocess_script)
            config = configparser.ConfigParser()

            config["DEFAULT"] = {}
            config["DEFAULT"]["embedding_name"] = str(embedding_name or "")
            config["DEFAULT"]["model_pkl_file_name"] = str(model_pkl_file_name or "")
            config["DEFAULT"]["embedding_pkl_file_name"] = str(embedding_pkl_file_name or "")
            config["DEFAULT"]["cols"] = ", ".join(fields)
            config["DEFAULT"]["encoding_pkl_file_name"] = encoding_pkl_file_name
            config["DEFAULT"]['imputer_pkl_file_name'] = imputer_pkl_file_name
            config["DEFAULT"]["scaler_pkl_file_name"] = scaler_pkl_file_name
            config["DEFAULT"]["features"] = ", ".join(features)
            config["DEFAULT"]["datetime_cols"] = ", ".join(list(datetime_cols)) if datetime_cols else ""
            config["DEFAULT"]["string_to_num_cols"] = ", ".join(list(string_to_num_cols)) if string_to_num_cols else ""
            config["DEFAULT"]["numerical_cols"] = ", ".join(list(numerical_cols_scaled.keys())) if numerical_cols_scaled else ""
            config["DEFAULT"]["regression_type"] = "Mixed Regression"

            config_file_name = "swayamml.conf"
            output_config_file = os.path.join(output_folder, config_file_name)
            with open(output_config_file, "w") as f:
                config.write(f)

            customer_files = [
                'deployment_script_regression.py',
                'requirements.txt',
                model_pkl_file_name,
                embedding_pkl_file_name,
                preprocess_script,
                encoding_pkl_file,
                imputer_pkl_file,
                scaler_pkl_file,
                config_file_name,
            ]
            customer_files = [f for f in customer_files if f]
            # include previously produced files (results/predictions)
            customer_files = list(dict.fromkeys(customer_files + produced_files))
        elif textual_cols:
            copy_deployment_collaterals(source_dir='.', target_dir=output_folder, model="NLP Regression")
            preprocess_script = "preprocess.py"
            output_preprocess_script = os.path.join(output_folder, preprocess_script)
            shutil.copy(preprocess_script, output_preprocess_script)

            config = configparser.ConfigParser()
            config["DEFAULT"] = {}
            config["DEFAULT"]["embedding_name"] = str(embedding_name or "")
            config["DEFAULT"]["model_pkl_file_name"] = str(model_pkl_file_name or "")
            config["DEFAULT"]["embedding_pkl_file_name"] = str(embedding_pkl_file_name or "")
            config["DEFAULT"]["cols"] = ", ".join(fields)
            config["DEFAULT"]["regression_type"] = "NLP"

            config_file_name = "swayamml.conf"
            output_config_file = os.path.join(output_folder, config_file_name)
            with open(output_config_file, "w") as f:
                config.write(f)

            customer_files = [
                'deployment_script_regression.py',
                'requirements.txt',
                model_pkl_file_name,
                embedding_pkl_file_name,
                preprocess_script,
                config_file_name,
            ]
            customer_files = [f for f in customer_files if f]
            customer_files = list(dict.fromkeys(customer_files + produced_files))
        elif numerical_cols_scaled:
            copy_deployment_collaterals(source_dir='.', target_dir=output_folder, model="NON NLP Regression")
            config = configparser.ConfigParser()
            config["DEFAULT"] = {}

            config["DEFAULT"]["model_pkl_file_name"] = str(model_pkl_file_name or "")
            config["DEFAULT"]["encoding_pkl_file_name"] = encoding_pkl_file_name
            config["DEFAULT"]["imputer_pkl_file_name"] = imputer_pkl_file_name
            config["DEFAULT"]["scaler_pkl_file_name"] = scaler_pkl_file_name
            config["DEFAULT"]["features"] = ", ".join(features)
            config["DEFAULT"]["datetime_cols"] = ", ".join(list(datetime_cols)) if datetime_cols else ""
            config["DEFAULT"]["string_to_num_cols"] = ", ".join(list(string_to_num_cols)) if string_to_num_cols else ""
            config["DEFAULT"]["numerical_cols"] = ", ".join(list(numerical_cols_scaled.keys())) if numerical_cols_scaled else ""
            config["DEFAULT"]["regression_type"] = "NON NLP"

            config_file_name = "swayamml.conf"
            output_config_file = os.path.join(output_folder, config_file_name)
            with open(output_config_file, "w") as f:
                config.write(f)

            customer_files = [
                'deployment_script_regression_non_nlp.py',
                'requirements.txt',
                model_pkl_file_name,
                encoding_pkl_file_name,
                imputer_pkl_file_name,
                scaler_pkl_file_name,
                config_file_name,
            ]
            customer_files = [f for f in customer_files if f]
            customer_files = list(dict.fromkeys(customer_files + produced_files))
    except Exception:
        logging.warning("Skipping deployment collateral creation due to missing artifacts or error.")
        logging.debug(traceback.format_exc())

    # If deployment block failed to populate, at least return produced files
    if not customer_files:
        customer_files = list(dict.fromkeys(produced_files))

    processing_logger["process_complete"] = True
    processing_logger["feature_importance"] = feature_importance

    end_time = time.time()
    logging.info(f"Regression process completed in {end_time - start_time} seconds.")

    # Report the total number of hyperparameter combinations (or rows fallback)
    try:
        reported_num_models = int(num_models) if num_models else int(Results.shape[0])
    except Exception:
        reported_num_models = len(Results)
    return summary, customer_files, reported_num_models, processing_logger

