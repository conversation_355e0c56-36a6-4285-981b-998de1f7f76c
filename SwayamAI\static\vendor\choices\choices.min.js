// Lightweight stub loader for Choices.js - this is a small wrapper that will fallback to CDN if full library not present.
// For production, replace with full minified Choices.js (v11.1.0) content.
(function(global){
  // If full Choices already loaded, skip
  if(global.Choices) return;
  console.warn('Local lightweight Choices stub in use. For full functionality, include full choices.min.js from upstream.');
  // Minimal constructor to avoid runtime errors for basic flows used here.
  function ChoicesStub(el, opts){
    this.el = el;
    this._options = Object.assign({removeItemButton:true,addItems:true}, opts||{});
    this._items = [];
    this._choices = Array.from(el.options||[]).map(function(o){ return {value: o.value, label: o.text}; });
    // expose enough API used by ui.js
    this.setChoiceByValue = function(val){
      // if option exists, select it
      var found = false;
      for(var i=0;i<el.options.length;i++){
        if(el.options[i].value==val){ el.options[i].selected = true; found=true; }
      }
      if(!found && this._options.addItems){ var opt = new Option(val,val,true,true); el.add(opt); }
    };
    this.getValue = function(){ return Array.from(el.selectedOptions).map(function(o){return o.value}); };
    this.removeActiveItems = function(){ this._items = []; };
    this.clearStore = function(){ this._items = []; };
    this.clearChoices = function(){ /* no-op for stub */ };
    this.setChoices = function(choices, valueKey, labelKey, replaceChoices, silent, highlight){
      // no-op stub
    };
    this.disable = function(){ el.disabled = true; };
    this.enable = function(){ el.disabled = false; };
  }
  global.Choices = ChoicesStub;
})(window);
