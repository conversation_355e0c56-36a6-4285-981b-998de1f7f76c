# Create the main CSS stylesheet
css_content = '''/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

/* App Container */
.app-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.app-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #4a5568;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.header-controls {
    display: flex;
    gap: 10px;
}

/* But<PERSON> Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Game Info Panel */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-item label {
    font-weight: 600;
    color: #4a5568;
}

.info-item span {
    font-weight: bold;
    color: #2d3748;
}

#difficulty-select {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #4a5568;
    font-weight: 600;
    cursor: pointer;
}

#difficulty-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Game Container */
.game-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

/* Sudoku Grid */
.sudoku-grid {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    grid-template-rows: repeat(9, 1fr);
    gap: 2px;
    background: #2d3748;
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    position: relative;
}

/* Create 3x3 box borders */
.sudoku-grid::before,
.sudoku-grid::after {
    content: '';
    position: absolute;
    background: #2d3748;
    z-index: 10;
    pointer-events: none;
}

.sudoku-grid::before {
    width: 4px;
    height: 100%;
    left: 33.33%;
    top: 0;
    transform: translateX(-50%);
}

.sudoku-grid::after {
    width: 4px;
    height: 100%;
    right: 33.33%;
    top: 0;
    transform: translateX(50%);
}

/* Horizontal lines for 3x3 boxes */
.sudoku-grid {
    background-image: 
        linear-gradient(0deg, #2d3748 33.33%, transparent 33.33%, transparent 36.66%, #2d3748 36.66%),
        linear-gradient(0deg, #2d3748 66.66%, transparent 66.66%, transparent 70%, #2d3748 70%);
    background-size: 100% 100%;
}

/* Grid Cell */
.grid-cell {
    width: 50px;
    height: 50px;
    background: white;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    z-index: 5;
}

.grid-cell:hover {
    background: #f7fafc;
    transform: scale(1.05);
}

.grid-cell.selected {
    background: #bee3f8;
    border-color: #3182ce;
    box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.5);
}

.grid-cell.given {
    background: #f0f9ff;
    color: #1e40af;
    font-weight: 900;
    cursor: default;
}

.grid-cell.given:hover {
    transform: none;
    background: #f0f9ff;
}

.grid-cell.error {
    background: #fed7d7;
    color: #c53030;
    animation: shake 0.5s ease-in-out;
}

.grid-cell.correct {
    background: #d4edda;
    color: #155724;
}

.grid-cell.hint {
    background: #fff3cd;
    color: #856404;
    animation: glow 0.8s ease-in-out;
}

/* Number Panel */
.number-panel {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.number-btn {
    width: 60px;
    height: 60px;
    border: 2px solid #e2e8f0;
    background: white;
    color: #4a5568;
    font-size: 24px;
    font-weight: bold;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.number-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.number-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.erase-btn {
    background: #fed7d7;
    color: #c53030;
    border-color: #feb2b2;
}

.erase-btn:hover {
    background: #feb2b2;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(1);
    transition: transform 0.3s ease;
}

.modal.hidden .modal-content {
    transform: scale(0.9);
}

.modal-content h2 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 2rem;
}

.modal-content p {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.modal-stats {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    display: flex;
    justify-content: space-around;
}

.modal-stats div {
    font-weight: bold;
    color: #2d3748;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

/* Loading Styles */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading.hidden {
    display: none;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e2e8f0;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading p {
    font-size: 1.2rem;
    color: #4a5568;
    font-weight: 600;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes glow {
    0%, 100% { background: #fff3cd; }
    50% { background: #ffeaa7; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .game-info {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .grid-cell {
        width: 35px;
        height: 35px;
        font-size: 18px;
    }
    
    .number-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .number-panel {
        grid-template-columns: repeat(5, 1fr);
        gap: 8px;
        padding: 15px;
    }
    
    .app-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .grid-cell {
        width: 30px;
        height: 30px;
        font-size: 16px;
    }
    
    .number-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .sudoku-grid {
        padding: 10px;
    }
}

/* High contrast mode for accessibility */
@media (prefers-contrast: high) {
    .grid-cell {
        border-width: 2px;
        border-color: #000;
    }
    
    .grid-cell.selected {
        background: #ffff00;
        color: #000;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}'''

# Save CSS file
with open('style.css', 'w', encoding='utf-8') as f:
    f.write(css_content)

print("✅ Created style.css")
print("🎨 CSS Features:")
print("   - Modern gradient design with glassmorphism effects")
print("   - Responsive grid layout for all screen sizes") 
print("   - Smooth animations and hover effects")
print("   - Accessibility features (high contrast, reduced motion)")
print("   - Professional color scheme")
print("   - Mobile-first responsive design")
print("   - Visual feedback for game states (selected, error, correct, hint)")
print("   - Loading and modal animations")