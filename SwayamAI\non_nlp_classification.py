import os
import os.path as op
import json
from typing import List, Dict, Any, <PERSON>ple

import numpy as np
import pandas as pd
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer
from sklearn.metrics import f1_score, balanced_accuracy_score, classification_report, confusion_matrix, roc_auc_score
from sklearn.model_selection import train_test_split, GridSearchCV, StratifiedKFold
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import OneHotEncoder, StandardScaler, LabelEncoder
from sklearn.linear_model import LogisticRegression, SGDClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.svm import SVC
import joblib


def _load_dataframe(file_path: str) -> pd.DataFrame:
    _, ext = op.splitext(file_path.lower())
    if ext == '.csv':
        return pd.read_csv(file_path)
    elif ext in ('.xlsx', '.xls'):
        return pd.read_excel(file_path)
    else:
        raise ValueError('Unsupported file format. Please upload CSV or Excel.')


def _build_preprocessor(df: pd.DataFrame, feat_cols: List[str]) -> Tuple[ColumnTransformer, List[str], List[str]]:
    cols = [c for c in feat_cols if c in df.columns]
    if not cols:
        raise ValueError('No valid feature columns selected.')
    X_df = df[cols]
    num_cols = [c for c in cols if pd.api.types.is_numeric_dtype(X_df[c])]
    cat_cols = [c for c in cols if c not in num_cols]

    num_pipe = Pipeline([
        ('imputer', SimpleImputer(strategy='median')),
        ('scaler', StandardScaler(with_mean=True, with_std=True)),
    ])
    # Build OneHotEncoder compatible across scikit-learn versions
    try:
        # sklearn >= 1.2
        ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=False)
    except TypeError:
        # sklearn < 1.2
        ohe = OneHotEncoder(handle_unknown='ignore', sparse=False)

    cat_pipe = Pipeline([
        ('imputer', SimpleImputer(strategy='most_frequent')),
        ('ohe', ohe),
    ])

    pre = ColumnTransformer(
        transformers=[
            ('num', num_pipe, num_cols),
            ('cat', cat_pipe, cat_cols),
        ],
        remainder='drop',
        sparse_threshold=0.0,
        n_jobs=None,
    )

    return pre, num_cols, cat_cols


def _candidates_and_grids() -> Tuple[Dict[str, Any], Dict[str, Dict[str, Any]]]:
    """
    Define candidate estimators and simple hyperparameter grids.
    Returns (models, grids)
    """
    ests: Dict[str, Any] = {
        'logreg': LogisticRegression(max_iter=1000, solver='lbfgs', random_state=42),
        'sgd': SGDClassifier(loss='log_loss', max_iter=1000, tol=1e-3, random_state=42),
        'rf': RandomForestClassifier(n_estimators=200, random_state=42),
        'et': ExtraTreesClassifier(n_estimators=300, random_state=42),
        'gb': GradientBoostingClassifier(random_state=42),
        'svc': SVC(probability=True, random_state=42),
    }

    grids: Dict[str, Dict[str, Any]] = {
        'logreg': {'C': [0.1, 1.0, 10.0]},
        'sgd': {'alpha': [1e-4, 1e-3], 'penalty': ['l2', 'l1']},
        # Avoid None for max_depth in grids so results don't show None
        'rf': {'n_estimators': [200, 400], 'max_depth': [10, 20, 30]},
        'et': {'n_estimators': [300, 600], 'max_depth': [10, 20, 30]},
        'gb': {'n_estimators': [100, 200], 'learning_rate': [0.05, 0.1]},
        'svc': {'C': [0.5, 1.0, 2.0], 'kernel': ['rbf', 'linear']},
    }

    # Optional XGBoost candidate
    try:
        from xgboost import XGBClassifier  # type: ignore
        ests['xgboost'] = XGBClassifier(random_state=42, verbosity=0, use_label_encoder=False)
        grids['xgboost'] = {
            'n_estimators': [200, 400],
            'max_depth': [3, 5],
            'learning_rate': [0.05, 0.1],
        }
    except Exception:
        pass

    return ests, grids


def _evaluate(y_true: np.ndarray, y_pred: np.ndarray, y_proba: np.ndarray | None) -> Dict[str, float]:
    avg = 'macro'
    out = {
        'f1_macro': float(f1_score(y_true, y_pred, average=avg, zero_division=0)),
        'balanced_accuracy': float(balanced_accuracy_score(y_true, y_pred)),
    }
    # ROC-AUC (only for binary or multilabel-binarized probabilities)
    try:
        if y_proba is not None:
            if y_proba.ndim == 1 or (y_proba.ndim == 2 and y_proba.shape[1] == 2):
                # binary: use positive class prob
                pos = y_proba if y_proba.ndim == 1 else y_proba[:, 1]
                out['roc_auc'] = float(roc_auc_score(y_true, pos))
    except Exception:
        pass
    return out


def non_nlp_classification_process(file_path: str, fields: List[str], label: str, output_folder: str):
    """
    Train multiple classifiers for mixed numerical/categorical features and select the best
    by F1-macro on a stratified hold-out set (tie-breaker: balanced accuracy). Writes:
    - classification_results.xlsx (per-model metrics + report + confusion matrix)
    - classification_model.pkl (best pipeline with preprocessing)
    - sample_prediction_classification.csv (first 10 predictions with probabilities)
    - deployment_script_non_nlp_classification.py (CLI to apply the model to new data)
    Returns: (summary_df, outputs_list, num_models)
    """
    os.makedirs(output_folder, exist_ok=True)
    df = _load_dataframe(file_path)

    if not label or label not in df.columns:
        raise ValueError('Target/label column is missing or not found in the dataset.')

    # Drop rows with missing label
    df = df.dropna(subset=[label]).copy()
    y = np.asarray(df[label].astype(str).values)  # ensure labels are numpy array of strings

    pre, num_cols, cat_cols = _build_preprocessor(df, fields or [c for c in df.columns if c != label])

    X = df[num_cols + cat_cols]

    # Train/test split with stratify
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    models, grids = _candidates_and_grids()
    results: Dict[str, Dict[str, Any]] = {}
    total_combinations = 0

    for name, clf in models.items():
        # Base pipeline for this model
        pipe = Pipeline([
            ('pre', pre),
            ('clf', clf)
        ])

        # Model-specific grid
        grid = grids.get(name, {})
        pref_grid = {f'clf__{k}': v for k, v in grid.items()} if grid else {}

        # Handle XGBoost-specific requirements
        label_encoder: LabelEncoder | None = None
        if name == 'xgboost':
            label_encoder = LabelEncoder()
            y_train_fit_tmp = label_encoder.fit_transform(y_train)
            # choose objective based on number of classes and guard tiny datasets
            n_classes = len(np.unique(y_train_fit_tmp))
            if n_classes < 2:
                # Skip XGBoost when only one class is present in the train split
                continue
            try:
                from xgboost import XGBClassifier  # type: ignore
                xgb_obj = 'binary:logistic' if n_classes <= 2 else 'multi:softprob'
                clf_local = XGBClassifier(
                    objective=xgb_obj,
                    eval_metric='logloss',
                    tree_method='hist',
                    random_state=42,
                    verbosity=0,
                    use_label_encoder=False
                )
                pipe = Pipeline([
                    ('pre', pre),
                    ('clf', clf_local)
                ])
            except Exception:
                # if XGBoost import/config fails, fall back to base clf
                pass
            y_train_fit = y_train_fit_tmp
        else:
            y_train_fit = y_train

        # Count combinations for this model
        if pref_grid:
            n_combo = 1
            for v in pref_grid.values():
                try:
                    n_combo *= len(v)
                except Exception:
                    n_combo *= 1
            total_combinations += n_combo
        else:
            total_combinations += 1

        # Safe CV decision (require at least 2 per class)
        y_train_fit_np = np.asarray(y_train_fit)
        min_count = int(pd.Series(y_train_fit_np).value_counts().min()) if y_train_fit_np.size > 0 else 0
        can_cv = bool(pref_grid) and min_count >= 2

        if can_cv:
            search = GridSearchCV(
                estimator=pipe,
                param_grid=pref_grid,
                scoring='f1_macro',
                cv=StratifiedKFold(n_splits=min(3, max(2, min_count)), shuffle=True, random_state=42),
                n_jobs=-1,
                refit=True,
            )
            search.fit(X_train, y_train_fit_np)
            best_pipe = search.best_estimator_
            best_params = {k.replace('clf__', ''): v for k, v in (search.best_params_ or {}).items()}
        else:
            best_pipe = pipe.fit(X_train, y_train_fit_np)
            best_params = {}

        # Predictions and probabilities
        y_pred_model = best_pipe.predict(X_test)
        if label_encoder is not None:
            y_pred = np.asarray(label_encoder.inverse_transform(np.asarray(y_pred_model)))
        else:
            y_pred = np.asarray(y_pred_model)

        y_proba = None
        try:
            y_proba = best_pipe.predict_proba(X_test)
        except Exception:
            y_proba = None

        # Metrics and results collection
        metrics = _evaluate(y_test, y_pred, y_proba)
        try:
            params_json = json.dumps(best_params, separators=(',', ':'), sort_keys=True)
        except Exception:
            params_json = ''

        results[name] = {
            'pipeline': best_pipe,
            'metrics': metrics,
            'params_json': params_json,
            'label_encoder': label_encoder,
            'report': classification_report(y_test, y_pred, output_dict=False, zero_division=0),
            'confusion_matrix': confusion_matrix(y_test, y_pred)
        }

    # pick best by (f1_macro, balanced_accuracy)
    best_name = None
    best_tuple = None
    for name, r in results.items():
        m = r['metrics']
        t = (m.get('f1_macro', -1.0), m.get('balanced_accuracy', -1.0))
        if best_tuple is None or t > best_tuple:
            best_tuple = t
            best_name = name

    assert best_name is not None
    best = results[best_name]

    # Save best model pipeline
    model_path = op.join(output_folder, 'classification_model.pkl')
    joblib.dump(best['pipeline'], model_path)

    # If best model used a label encoder (e.g., XGBoost), persist it for deployment
    le_best = best.get('label_encoder')
    le_path = None
    if le_best is not None:
        le_path = op.join(output_folder, 'label_encoder.pkl')
        try:
            joblib.dump(le_best, le_path)
        except Exception:
            le_path = None

    # Build per-model metrics DataFrame (include params and best flag)
    metrics_rows = []
    for name, r in results.items():
        row = {
            'model': name,
            'is_best': (name == best_name),
            'params': r.get('params_json', ''),
        }
        row.update(r['metrics'])
        metrics_rows.append(row)
    metrics_df = pd.DataFrame(metrics_rows).sort_values(['f1_macro', 'balanced_accuracy'], ascending=[False, False])

    # Write Excel report
    xlsx_path = op.join(output_folder, 'classification_results.xlsx')
    with pd.ExcelWriter(xlsx_path, engine='openpyxl') as w:
        metrics_df.to_excel(w, index=False, sheet_name='metrics')
        # confusion matrix for best model
        cm = pd.DataFrame(best['confusion_matrix'])
        cm.to_excel(w, index=False, sheet_name='confusion_matrix')
        # textual classification report for best model
        rep_df = pd.DataFrame({'report': best['report'].splitlines()})
        rep_df.to_excel(w, index=False, sheet_name='report')
        # hyperparameters per model (JSON string per row)
        params_rows = [{'model': n, 'params': r.get('params_json', '')} for n, r in results.items()]
        params_df = pd.DataFrame(params_rows)
        params_df.to_excel(w, index=False, sheet_name='hyperparameters')
        # run configuration sheet
        run_cfg = pd.DataFrame([
            {'key': 'target', 'value': label},
            {'key': 'selected_features', 'value': ', '.join(num_cols + cat_cols)},
            {'key': 'num_combinations', 'value': int(total_combinations)},
        ])
        run_cfg.to_excel(w, index=False, sheet_name='run_config')

    # Sample prediction file (first 10 rows of original X)
    sample_in = X.head(10).copy()
    sample_pred_model = best['pipeline'].predict(sample_in)
    # if best model used encoded labels (e.g., XGBoost), invert to original labels
    if le_best is not None:
        sample_pred = le_best.inverse_transform(np.asarray(sample_pred_model))
    else:
        sample_pred = sample_pred_model
    proba_df = pd.DataFrame()
    try:
        proba = best['pipeline'].predict_proba(sample_in)
        if proba is not None:
            # construct proba columns per class label as known by the classifier
            classes = None
            if le_best is not None:
                # use original labels order if available
                classes = getattr(le_best, 'classes_', None)
            if classes is None:
                classes = getattr(best['pipeline'].named_steps['clf'], 'classes_', None)
            if classes is not None and proba.ndim == 2:
                proba_df = pd.DataFrame(proba, columns=[f'proba_{c}' for c in classes])
                proba_df['proba_max'] = proba_df.max(axis=1)
    except Exception:
        pass

    # Include original sample input columns, predicted label, and probabilities
    sample_out = sample_in.reset_index(drop=True).copy()
    sample_out['predicted_label'] = pd.Series(sample_pred, index=sample_out.index)
    if not proba_df.empty:
        sample_out = pd.concat([sample_out, proba_df.reset_index(drop=True)], axis=1)
    sample_csv = op.join(output_folder, 'sample_prediction_classification.csv')
    sample_out.to_csv(sample_csv, index=False)

    # Write deployment script
    dep_path = op.join(output_folder, 'deployment_script_non_nlp_classification.py')
    try:
        with open(dep_path, 'w', encoding='utf-8') as f:
            f.write(DEPLOYMENT_SCRIPT)
    except Exception:
        pass

    outputs = [op.basename(xlsx_path), op.basename(model_path), op.basename(sample_csv), op.basename(dep_path)]
    if le_path:
        outputs.append(op.basename(le_path))
    # Return per-model metrics as summary
    return metrics_df, outputs, len(models)


DEPLOYMENT_SCRIPT = r"""
import argparse
import os.path as op
import os
import sys
import pandas as pd
import joblib


def _load(file_path: str) -> pd.DataFrame:
    _, ext = op.splitext(file_path.lower())
    if ext == '.csv':
        return pd.read_csv(file_path)
    else:
        return pd.read_excel(file_path)


def _expected_columns_from_pipeline(model) -> list | None:
    try:
        pre = model.named_steps.get('pre') if hasattr(model, 'named_steps') else None
    except Exception:
        pre = None
    if pre is None:
        return None
    cols = []
    tups = getattr(pre, 'transformers_', None) or getattr(pre, 'transformers', None)
    if not tups:
        return None
    for item in tups:
        try:
            columns = item[2]
            if columns is None:
                continue
            if isinstance(columns, (list, tuple)):
                cols.extend(list(columns))
            elif hasattr(columns, '__iter__') and not isinstance(columns, str):
                cols.extend(list(columns))
        except Exception:
            continue
    seen = set()
    ordered = []
    for c in cols:
        if c not in seen:
            seen.add(c)
            ordered.append(c)
    return ordered


def main(argv=None):
    p = argparse.ArgumentParser(description='Apply classification model (numerical/categorical) to new data')
    # Support both flags and positional args
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/Excel with the same feature columns used for training')
    p.add_argument('positional_output', nargs='?', help='Path to write predictions CSV/XLSX')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/Excel with the same feature columns used for training')
    p.add_argument('--output', dest='flag_output', help='Path to write predictions CSV/XLSX')
    args = p.parse_args(argv)

    inp = args.flag_input or args.positional_input
    if not inp:
        p.error('An input file is required. Provide --input or a positional input path.')
    outp = args.flag_output or args.positional_output
    if not outp:
        base, ext = op.splitext(inp)
        outp = f"{base}_preds.csv" if ext.lower() == '.csv' else f"{base}_preds.xlsx"

    here = op.dirname(op.abspath(__file__))
    model_path = op.join(here, 'classification_model.pkl')
    if not op.exists(model_path):
        print('Missing model artifact:', model_path)
        sys.exit(1)
    model = joblib.load(model_path)

    # Optional label encoder for models trained on encoded labels (e.g., XGBoost)
    le = None
    le_path = op.join(here, 'label_encoder.pkl')
    if op.exists(le_path):
        try:
            le = joblib.load(le_path)
        except Exception:
            le = None

    df = _load(inp)

    # Validate required columns if we can infer them
    req = _expected_columns_from_pipeline(model)
    if isinstance(req, list) and req:
        missing = [c for c in req if c not in df.columns]
        if missing:
            print('Error: missing required input columns:', ', '.join(missing))
            sys.exit(2)

    preds = model.predict(df)
    if le is not None:
        try:
            preds = le.inverse_transform(preds)
        except Exception:
            pass

    # Start with all original columns, then append predictions and probabilities
    out = df.copy()
    out['predicted_label'] = preds
    # probabilities (if available)
    try:
        proba = model.predict_proba(df)
        classes = None
        if le is not None:
            classes = getattr(le, 'classes_', None)
        if classes is None:
            classes = getattr(getattr(model, 'named_steps', {}).get('clf', None), 'classes_', None)
        if classes is not None:
            import numpy as np
            proba_df = pd.DataFrame(proba, columns=[f'proba_{c}' for c in classes])
            proba_df['proba_max'] = proba_df.max(axis=1)
            out = pd.concat([out, proba_df], axis=1)
    except Exception:
        pass

    if outp.lower().endswith('.xlsx'):
        with pd.ExcelWriter(outp, engine='openpyxl') as w:
            out.to_excel(w, index=False)
    else:
        out.to_csv(outp, index=False)
    print('Wrote', outp)


if __name__ == '__main__':
    main()
"""
