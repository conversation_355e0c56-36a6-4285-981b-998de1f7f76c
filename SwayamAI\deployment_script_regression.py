"""
Deployment script for NLP/Mixed Regression.

Usage (PowerShell):
    # Flags
    python deployment_script_regression.py --input data.csv --output preds.csv
    # Or positional args
    python deployment_script_regression.py data.csv preds.csv

Expects the following in the same folder:
    - swayamml.conf with keys: embedding_name, model_pkl_file_name, embedding_pkl_file_name, encoding_pkl_file_name, imputer_pkl_file_name, scaler_pkl_file_name, features, numerical_cols
  - <model>.pkl and embedding model/vectorizer pkl as per embedding_name
  - encoding.pkl, imputer.pkl, scaler.pkl if numeric features are used
"""
import os
import sys
import argparse
import configparser
import joblib
import pandas as pd
import numpy as np


def _as_list(csv_str: str) -> list:
    if not csv_str:
        return []
    return [c.strip() for c in str(csv_str).split(',') if str(c).strip()]


def load_config(base_dir: str) -> dict:
    cfg_path = os.path.join(base_dir, 'swayamml.conf')
    if not os.path.exists(cfg_path):
        raise FileNotFoundError(f"Config not found: {cfg_path}")
    cp = configparser.ConfigParser()
    cp.read(cfg_path)
    d = cp['DEFAULT'] if 'DEFAULT' in cp else {}
    return {
        'embedding_name': d.get('embedding_name', ''),
        'model': d.get('model_pkl_file_name', ''),
        'embedding_pkl': d.get('embedding_pkl_file_name', ''),
        'encoding': d.get('encoding_pkl_file_name', ''),
        'imputer': d.get('imputer_pkl_file_name', ''),
        'scaler': d.get('scaler_pkl_file_name', ''),
        'features': _as_list(d.get('features', '')),
        'numerical_cols': _as_list(d.get('numerical_cols', '')),
    }


def load_artifacts(base_dir: str, cfg: dict):
    model = joblib.load(os.path.join(base_dir, cfg['model'])) if cfg.get('model') else None
    embedding = None
    if cfg.get('embedding_pkl'):
        ep = os.path.join(base_dir, cfg['embedding_pkl'])
        if os.path.exists(ep):
            embedding = joblib.load(ep)
    enc = joblib.load(os.path.join(base_dir, cfg['encoding'])) if cfg.get('encoding') else {}
    imp = joblib.load(os.path.join(base_dir, cfg['imputer'])) if cfg.get('imputer') else None
    scl = joblib.load(os.path.join(base_dir, cfg['scaler'])) if cfg.get('scaler') else None
    return model, embedding, enc, imp, scl


def build_embeddings(df: pd.DataFrame, cfg: dict, loaded_embedding):
    name = (cfg.get('embedding_name') or '').lower()
    if not name:
        raise RuntimeError('embedding_name missing in config')
    text = df['Cleaned_data'] if 'Cleaned_data' in df.columns else df.iloc[:, 0].astype(str)
    if name.startswith('tfidf'):
        if loaded_embedding is None:
            raise RuntimeError('TF-IDF vectorizer not found (embedding_pkl_file_name)')
        return loaded_embedding.transform(text.astype('U')).toarray()
    if name.startswith('w2v'):
        # loaded_embedding is a gensim Word2Vec model
        try:
            from nltk.tokenize import word_tokenize
        except Exception:
            word_tokenize = lambda s: s.split()
        feats = []
        for doc in text:
            toks = word_tokenize(str(doc))
            vecs = [loaded_embedding.wv[w] for w in toks if w in loaded_embedding.wv]
            if vecs:
                arr = np.asarray(vecs, dtype=np.float32)
                feats.append(arr.mean(axis=0))
            else:
                feats.append(np.zeros(loaded_embedding.vector_size, dtype=np.float32))
        return np.asarray(feats)
    if name.startswith('bert'):
        raise RuntimeError('BERT embedding prediction requires transformers/torch. Provide numeric-only or tfidf/w2v config for deployment script.')
    raise RuntimeError(f'Unknown embedding_name: {name}')


def apply_encodings(df: pd.DataFrame, encodings: dict) -> pd.DataFrame:
    out = df.copy()
    for col, mapping in (encodings or {}).items():
        if col in out.columns and isinstance(mapping, dict):
            out[col] = out[col].map(mapping).fillna(0)
    return out


def impute_and_scale_numeric(df: pd.DataFrame, numerical_cols: list, imputer, scaler) -> pd.DataFrame:
    out = df.copy()
    num = list(numerical_cols or [])
    if not num:
        return out
    for c in num:
        if c in out.columns and out[c].dtype == object:
            out[c] = pd.to_numeric(out[c], errors='coerce')
    # Build matrix with NaN for missing cols so the imputer can apply training statistics
    X = out.reindex(columns=num)
    if imputer is not None:
        Xt = imputer.transform(X)
        X = pd.DataFrame(Xt, index=out.index, columns=num)
    if scaler is not None:
        Xt = scaler.transform(X)
        X = pd.DataFrame(Xt, index=out.index, columns=num)
    out.loc[:, num] = X
    return out


from typing import Optional


def _resolve_input_path(path: str, script_dir: str) -> str:
    # Try as provided (absolute or relative to CWD)
    cand = os.path.abspath(path)
    if os.path.exists(cand):
        return cand
    # Try relative to script directory
    cand2 = os.path.join(script_dir, path)
    if os.path.exists(cand2):
        return cand2
    raise SystemExit(
        f"Input file not found: '{path}'. Tried: '{cand}' and '{cand2}'. Provide a full path or place the file alongside this script."
    )


def predict_file(input_path: str, output_path: str, base_dir: Optional[str] = None, override_features: Optional[list] = None, override_num_cols: Optional[list] = None):
    base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
    cfg = load_config(base_dir)
    model, embedding, enc, imp, scl = load_artifacts(base_dir, cfg)
    if model is None:
        raise RuntimeError('Model artifact not found or could not be loaded.')

    # Read input
    _, ext = os.path.splitext(input_path.lower())
    if ext == '.csv':
        df = pd.read_csv(input_path)
    elif ext in ('.xlsx', '.xls'):
        df = pd.read_excel(input_path)
    else:
        raise ValueError('Unsupported input file type. Use CSV/XLSX.')

    # Require concatenated Cleaned_data (same UI pipeline). If not present, try to combine all object cols.
    if 'Cleaned_data' not in df.columns:
        obj_cols = [c for c in df.columns if df[c].dtype == object]
        if obj_cols:
            df['Cleaned_data'] = df[obj_cols].astype(str).agg(' '.join, axis=1)
        else:
            raise RuntimeError('Cleaned_data column not present and no textual columns to build it from.')

    X_emb = build_embeddings(df, cfg, embedding)

    # Append numeric features if configured
    final_feats = list(override_features) if override_features is not None else list(cfg.get('features', []))
    num_cols = list(override_num_cols) if override_num_cols is not None else list(cfg.get('numerical_cols', []))
    if final_feats or num_cols:
        dfp = apply_encodings(df, enc)
        dfp = impute_and_scale_numeric(dfp, num_cols, imp, scl)
        # Prefer final feature set; fallback to original numeric cols
        base_cols = final_feats if final_feats else num_cols
        X_num = dfp.reindex(columns=base_cols, fill_value=0).to_numpy()
        X = np.concatenate([X_emb, X_num], axis=1)
    else:
        X = X_emb

    preds = model.predict(X)
    out_df = df.copy()
    out_df['predicted_value'] = preds
    if output_path.lower().endswith('.xlsx'):
        with pd.ExcelWriter(output_path, engine='openpyxl') as w:
            out_df.to_excel(w, index=False)
    else:
        out_df.to_csv(output_path, index=False)


def main(argv=None):
    p = argparse.ArgumentParser(description='NLP/Mixed Regression Deployment Predictor')
    # Support flags and positional args
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/XLSX')
    p.add_argument('positional_output', nargs='?', help='Path to output predictions CSV/XLSX')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/XLSX')
    p.add_argument('--output', dest='flag_output', help='Path to output predictions CSV/XLSX')
    # Optional overrides
    p.add_argument('--features', default=None, help='Comma-separated list of numeric features in the exact training order (overrides config).')
    p.add_argument('--numerical-cols', default=None, help='Comma-separated list of original numeric columns to impute/scale (overrides config).')
    args = p.parse_args(argv)

    here = os.path.dirname(os.path.abspath(__file__))
    inp = args.flag_input or args.positional_input
    if not inp:
        p.error('An input file is required. Provide --input or a positional input path.')
    outp = args.flag_output or args.positional_output
    if not outp:
        base, ext = os.path.splitext(inp)
        outp = f"{base}_preds.csv" if ext.lower() == '.csv' else f"{base}_preds.xlsx"

    inp_resolved = _resolve_input_path(inp, here)
    override_features = [c.strip() for c in args.features.split(',')] if args.features else None
    override_num_cols = [c.strip() for c in args.numerical_cols.split(',')] if args.numerical_cols else None

    predict_file(inp_resolved, outp, base_dir=here, override_features=override_features, override_num_cols=override_num_cols)


if __name__ == '__main__':
    sys.exit(main())
