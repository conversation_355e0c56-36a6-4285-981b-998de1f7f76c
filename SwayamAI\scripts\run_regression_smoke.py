import os, sys, time
base = os.path.dirname(os.path.abspath(__file__))
root = os.path.dirname(base)
if root not in sys.path:
    sys.path.insert(0, root)
from Regression import regression_process

file = os.path.join(root, 'Titanic-Dataset.csv')
fields = ['Pclass','Age','SibSp','Parch']
label = 'Fare'
output_dir = os.path.join(root, 'outputs', f'smoke-{int(time.time())}')
os.makedirs(output_dir, exist_ok=True)

summary, customer_files, num_models = regression_process(file, fields, label, output_dir)[:3]
print('num_models=', num_models)
print('customer_files=', customer_files)
print('output_dir=', output_dir)
for f in customer_files:
    p = os.path.join(output_dir, os.path.basename(f))
    print(os.path.basename(f), 'exists?', os.path.exists(p))
