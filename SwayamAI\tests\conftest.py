import os
import sys
import types
import json
import builtins
import contextlib
import io
import tempfile
from pathlib import Path

import numpy as np
import pandas as pd
import pytest

# Ensure project root on sys.path for module imports
ROOT = Path(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))


@pytest.fixture
def tmp_output_dir(tmp_path):
    d = tmp_path / "out"
    d.mkdir(parents=True, exist_ok=True)
    return d


@pytest.fixture(autouse=True)
def fast_env(monkeypatch):
    """Speed-focused defaults for tests: reduce heavy work and randomness."""
    monkeypatch.setenv("SKLEARN_ALLOW_INVALID_RESPONSES", "1")
    # Cap engineered features in regression
    monkeypatch.setenv("MAX_ENGINEERED_FEATURES", "20")
    yield


class DummyPipeline:
    """A tiny sklearn-like pipeline for mocking transformers pipelines."""
    def __init__(self, labels=("NEGATIVE", "POSITIVE")):
        self.labels = labels
    def __call__(self, texts, truncation=True):
        out = []
        for t in texts:
            # deterministic pseudo score from hash
            h = abs(hash(str(t))) % 1000 / 1000.0
            lbl = self.labels[1] if h >= 0.5 else self.labels[0]
            out.append({"label": lbl, "score": h if h >= 0.5 else 1.0 - h})
        return out


@pytest.fixture
def patch_transformers(monkeypatch):
    """Monkeypatch transformers and torch to avoid heavy downloads and GPU in tests."""
    import types as _t
    # Build a minimal transformers module
    class _Tok:
        def __init__(self, *a, **k):
            pass
    class _Mdl:
        def __init__(self, *a, **k):
            class Cfg:
                id2label = {0: "NEGATIVE", 1: "POSITIVE"}
            self.config = Cfg()
    transformers_mod = _t.ModuleType('transformers')
    transformers_mod.AutoTokenizer = _Tok
    transformers_mod.AutoModelForSequenceClassification = _Mdl
    transformers_mod.AutoModel = _Mdl
    transformers_mod.pipeline = lambda *a, **k: DummyPipeline()
    monkeypatch.setitem(sys.modules, 'transformers', transformers_mod)

    # Minimal torch with cuda and utils.data.DataLoader
    torch_mod = _t.ModuleType('torch')
    torch_mod.cuda = _t.SimpleNamespace(is_available=lambda: False)
    torch_mod.no_grad = lambda: contextlib.nullcontext()
    torch_mod.manual_seed = lambda *a, **k: None
    utils_mod = _t.ModuleType('torch.utils')
    data_mod = _t.ModuleType('torch.utils.data')
    data_mod.DataLoader = lambda seq, batch_size=8, shuffle=False: [list(seq)]
    utils_mod.data = data_mod
    monkeypatch.setitem(sys.modules, 'torch', torch_mod)
    monkeypatch.setitem(sys.modules, 'torch.utils', utils_mod)
    monkeypatch.setitem(sys.modules, 'torch.utils.data', data_mod)

    yield


@pytest.fixture
def small_csv(tmp_path):
    """Create a tiny numeric CSV for quick e2e tests."""
    df = pd.DataFrame({
        'a': [1, 2, 3, 100],
        'b': [1.1, 1.2, 1.1, 50.0],
        'c': [0, 0, 1, 1],
        'text': ['foo', 'bar', 'foo bar', 'outlier text']
    })
    p = tmp_path / 'data.csv'
    df.to_csv(p, index=False)
    return str(p)
