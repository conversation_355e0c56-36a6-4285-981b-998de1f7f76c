import os
import pandas as pd
from affinity_analysis import affinity_analysis_process


def test_affinity_single_column(tmp_path):
    # Tiny dataset of baskets with simple co-occurrences
    df = pd.DataFrame({
        'basket_id': [1, 2, 3, 4, 5],
        'items': [
            'Bread, Milk',
            'Bread, Diaper, Beer, Eggs',
            'Milk, Diaper, Beer, Cola',
            'Bread, Milk, Diaper, Beer',
            'Bread, Milk, Diaper, Cola',
        ]
    })
    src = tmp_path / 'data.csv'
    df.to_csv(src, index=False)

    out_dir = tmp_path / 'out'
    out_dir.mkdir(exist_ok=True)

    summary, files, _ = affinity_analysis_process(
        str(src),
        str(out_dir),
        algo='apriori',
        index_col='basket_id',
        data_mode='single',
        single_text_column='items',
        delimiter=',',
        min_support=0.2,
        min_confidence=0.3,
        min_lift=1.0,
        max_rules=1000,
    )

    # Files present
    names = set(files)
    assert 'affinity_itemsets.csv' in names
    assert ('affinity_rules.csv' in names) or ('affinity_rules_top10k.csv' in names) or ('affinity_rules.db' in names)
    assert 'affinity_baskets.csv' in names

    # Rules CSV has renamed columns
    rules_csv = os.path.join(str(out_dir), 'affinity_rules.csv')
    if os.path.exists(rules_csv):
        rules_df = pd.read_csv(rules_csv)
        for col in ['Antecedent', 'Consequent', 'Support', 'Confidence', 'Lift']:
            assert col in rules_df.columns

    # Basket mapping contains the index and items
    baskets_csv = os.path.join(str(out_dir), 'affinity_baskets.csv')
    assert os.path.exists(baskets_csv)
    m = pd.read_csv(baskets_csv)
    assert 'basket_id' in m.columns
    assert 'items' in m.columns
    assert len(m) == 5

    # Summary contains top rows with renamed columns
    if not summary.empty:
        for col in ['Antecedent', 'Consequent', 'Support', 'Confidence', 'Lift']:
            assert col in summary.columns
