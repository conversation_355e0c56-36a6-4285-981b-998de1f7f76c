import os
import sys
import subprocess
import pandas as pd
import numpy as np

from non_nlp_clustering import non_nlp_clustering_process


def make_numeric_cluster_df(n=120, seed=0):
    rng = np.random.default_rng(seed)
    a = rng.normal(0, 0.5, size=(n//2, 2)) + np.array([0, 0])
    b = rng.normal(0, 0.5, size=(n - n//2, 2)) + np.array([3, 3])
    xy = np.vstack([a, b])
    df = pd.DataFrame(xy, columns=['x', 'y'])
    df['cat'] = rng.choice(['A','B'], size=n)
    return df


def test_non_nlp_clustering_e2e(tmp_path, tmp_output_dir):
    df = make_numeric_cluster_df()
    inp = tmp_path / 'clu.csv'
    df.to_csv(inp, index=False)

    summary, files, n_models, log = non_nlp_clustering_process(
        str(inp), fields=['x','y','cat'], output_folder=str(tmp_output_dir)
    )

    assert hasattr(summary, 'columns')
    # excel output
    assert (tmp_output_dir / 'clustering_Results.xlsx').exists()
