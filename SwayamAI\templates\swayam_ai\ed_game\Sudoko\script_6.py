# Create the puzzle generation script that can generate 1000+ puzzles
puzzle_generator_script = '''#!/usr/bin/env node

/**
 * Puzzle Database Generator Script
 * Generates 1000+ Sudoku puzzles and saves them to JSON files
 * Run with: node scripts/generate-puzzles.js
 */

const fs = require('fs').promises;
const path = require('path');

// Import our Sudoku classes (assuming they work in Node.js environment)
class SudokuSolver {
    // ... (Same implementation as browser version)
    constructor() {
        this.GRID_SIZE = 9;
        this.BOX_SIZE = 3;
        this.EMPTY = 0;
    }

    solve(grid) {
        const emptyCell = this.findEmptyCell(grid);
        if (!emptyCell) return true;

        const [row, col] = emptyCell;
        for (let num = 1; num <= 9; num++) {
            if (this.isValidMove(grid, row, col, num)) {
                grid[row][col] = num;
                if (this.solve(grid)) return true;
                grid[row][col] = this.EMPTY;
            }
        }
        return false;
    }

    findEmptyCell(grid) {
        for (let row = 0; row < this.GRID_SIZE; row++) {
            for (let col = 0; col < this.GRID_SIZE; col++) {
                if (grid[row][col] === this.EMPTY) {
                    return [row, col];
                }
            }
        }
        return null;
    }

    isValidMove(grid, row, col, num) {
        return this.isValidInRow(grid, row, num) &&
               this.isValidInColumn(grid, col, num) &&
               this.isValidInBox(grid, row, col, num);
    }

    isValidInRow(grid, row, num) {
        for (let col = 0; col < this.GRID_SIZE; col++) {
            if (grid[row][col] === num) return false;
        }
        return true;
    }

    isValidInColumn(grid, col, num) {
        for (let row = 0; row < this.GRID_SIZE; row++) {
            if (grid[row][col] === num) return false;
        }
        return true;
    }

    isValidInBox(grid, row, col, num) {
        const boxStartRow = Math.floor(row / this.BOX_SIZE) * this.BOX_SIZE;
        const boxStartCol = Math.floor(col / this.BOX_SIZE) * this.BOX_SIZE;

        for (let r = boxStartRow; r < boxStartRow + this.BOX_SIZE; r++) {
            for (let c = boxStartCol; c < boxStartCol + this.BOX_SIZE; c++) {
                if (grid[r][c] === num) return false;
            }
        }
        return true;
    }

    copyGrid(grid) {
        return grid.map(row => [...row]);
    }

    hasUniqueSolution(grid) {
        return this.countSolutions(this.copyGrid(grid), 2) === 1;
    }

    countSolutions(grid, maxCount = 2) {
        const emptyCell = this.findEmptyCell(grid);
        if (!emptyCell) return 1;

        const [row, col] = emptyCell;
        let solutionCount = 0;

        for (let num = 1; num <= 9; num++) {
            if (this.isValidMove(grid, row, col, num)) {
                grid[row][col] = num;
                solutionCount += this.countSolutions(grid, maxCount - solutionCount);
                grid[row][col] = this.EMPTY;
                if (solutionCount >= maxCount) break;
            }
        }
        return solutionCount;
    }
}

class SudokuGenerator {
    constructor() {
        this.solver = new SudokuSolver();
        this.GRID_SIZE = 9;
        this.EMPTY = 0;
        
        this.DIFFICULTY_SETTINGS = {
            easy: { minClues: 40, maxClues: 45, count: 400 },
            medium: { minClues: 30, maxClues: 35, count: 400 },
            hard: { minClues: 25, maxClues: 30, count: 300 }
        };
    }

    generateCompleteGrid() {
        const grid = Array(9).fill(null).map(() => Array(9).fill(0));
        this.fillDiagonalBoxes(grid);
        this.solver.solve(grid);
        return grid;
    }

    fillDiagonalBoxes(grid) {
        for (let box = 0; box < 3; box++) {
            this.fillBox(grid, box * 3, box * 3);
        }
    }

    fillBox(grid, startRow, startCol) {
        const numbers = this.shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);
        let index = 0;
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                grid[startRow + i][startCol + j] = numbers[index++];
            }
        }
    }

    generatePuzzle(difficulty = 'medium') {
        const settings = this.DIFFICULTY_SETTINGS[difficulty];
        let attempts = 0;
        const maxAttempts = 100;

        while (attempts < maxAttempts) {
            try {
                const solution = this.generateCompleteGrid();
                const puzzle = this.createPuzzleFromSolution(solution, settings);
                
                if (puzzle) {
                    const clueCount = this.countClues(puzzle);
                    if (clueCount >= settings.minClues && clueCount <= settings.maxClues) {
                        return {
                            puzzle: puzzle,
                            solution: solution,
                            difficulty: difficulty,
                            clues: clueCount,
                            id: this.generatePuzzleId(),
                            timestamp: new Date().toISOString()
                        };
                    }
                }
            } catch (error) {
                // Continue trying
            }
            attempts++;
        }

        throw new Error(`Failed to generate ${difficulty} puzzle after ${maxAttempts} attempts`);
    }

    createPuzzleFromSolution(solution, settings) {
        const puzzle = this.solver.copyGrid(solution);
        const cells = this.getAllCellPositions();
        this.shuffleArray(cells);

        let removedCount = 0;
        const maxRemove = 81 - settings.minClues;

        for (const [row, col] of cells) {
            if (removedCount >= maxRemove) break;

            const originalValue = puzzle[row][col];
            puzzle[row][col] = this.EMPTY;

            if (!this.solver.hasUniqueSolution(puzzle)) {
                puzzle[row][col] = originalValue;
            } else {
                removedCount++;
                const currentClues = 81 - removedCount;
                if (currentClues <= settings.maxClues && currentClues >= settings.minClues) {
                    break;
                }
            }
        }

        return puzzle;
    }

    getAllCellPositions() {
        const positions = [];
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                positions.push([row, col]);
            }
        }
        return positions;
    }

    countClues(grid) {
        let count = 0;
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (grid[row][col] !== this.EMPTY) count++;
            }
        }
        return count;
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    generatePuzzleId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

class PuzzleGenerator {
    constructor() {
        this.generator = new SudokuGenerator();
        this.outputDir = path.join(__dirname, '..', 'data', 'puzzles');
    }

    async generateAllPuzzles() {
        console.log('🚀 Starting puzzle generation...');
        console.log('📁 Output directory:', this.outputDir);
        
        try {
            // Ensure output directory exists
            await fs.mkdir(this.outputDir, { recursive: true });

            const difficulties = ['easy', 'medium', 'hard'];
            const allResults = {};

            for (const difficulty of difficulties) {
                console.log(`\\n🎯 Generating ${difficulty} puzzles...`);
                const puzzles = await this.generateDifficultySet(difficulty);
                allResults[difficulty] = puzzles;
            }

            // Generate summary report
            await this.generateSummaryReport(allResults);
            
            console.log('\\n✅ All puzzles generated successfully!');
            return allResults;

        } catch (error) {
            console.error('❌ Error generating puzzles:', error);
            throw error;
        }
    }

    async generateDifficultySet(difficulty) {
        const settings = this.generator.DIFFICULTY_SETTINGS[difficulty];
        const targetCount = settings.count;
        const puzzles = [];
        const startTime = Date.now();
        
        let successCount = 0;
        let failureCount = 0;

        console.log(`Target: ${targetCount} ${difficulty} puzzles (${settings.minClues}-${settings.maxClues} clues)`);

        while (successCount < targetCount) {
            try {
                const puzzle = this.generator.generatePuzzle(difficulty);
                puzzles.push(puzzle);
                successCount++;

                // Progress report every 50 puzzles
                if (successCount % 50 === 0) {
                    const elapsed = (Date.now() - startTime) / 1000;
                    const rate = successCount / elapsed;
                    console.log(`  Progress: ${successCount}/${targetCount} (${rate.toFixed(1)} puzzles/sec, ${failureCount} failed attempts)`);
                }

            } catch (error) {
                failureCount++;
                if (failureCount % 100 === 0) {
                    console.log(`  Warning: ${failureCount} failed attempts so far...`);
                }
            }
        }

        // Save to file
        const filename = `${difficulty}.json`;
        const filepath = path.join(this.outputDir, filename);
        
        const fileData = {
            difficulty: difficulty,
            count: puzzles.length,
            minClues: settings.minClues,
            maxClues: settings.maxClues,
            generated: new Date().toISOString(),
            puzzles: puzzles
        };

        await fs.writeFile(filepath, JSON.stringify(fileData, null, 2));
        
        const elapsed = (Date.now() - startTime) / 1000;
        const finalRate = successCount / elapsed;
        
        console.log(`✅ ${difficulty}: ${successCount} puzzles generated in ${elapsed.toFixed(1)}s (${finalRate.toFixed(1)} puzzles/sec)`);
        console.log(`📄 Saved to: ${filename}`);
        
        return {
            difficulty,
            count: successCount,
            failureCount,
            elapsed,
            rate: finalRate,
            filename
        };
    }

    async generateSummaryReport(results) {
        const summary = {
            generated: new Date().toISOString(),
            totalPuzzles: 0,
            difficulties: {}
        };

        for (const [difficulty, result] of Object.entries(results)) {
            summary.totalPuzzles += result.count;
            summary.difficulties[difficulty] = {
                count: result.count,
                failureCount: result.failureCount,
                generationTime: result.elapsed,
                rate: result.rate,
                filename: result.filename
            };
        }

        const summaryPath = path.join(this.outputDir, 'summary.json');
        await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));

        console.log('\\n📊 Generation Summary:');
        console.log(`Total puzzles: ${summary.totalPuzzles}`);
        for (const [difficulty, stats] of Object.entries(summary.difficulties)) {
            console.log(`  ${difficulty}: ${stats.count} puzzles (${stats.rate.toFixed(1)} puzzles/sec)`);
        }
        console.log(`📄 Summary saved to: summary.json`);
    }

    async validatePuzzles(difficulty) {
        console.log(`🔍 Validating ${difficulty} puzzles...`);
        
        const filepath = path.join(this.outputDir, `${difficulty}.json`);
        const data = JSON.parse(await fs.readFile(filepath, 'utf8'));
        
        let validCount = 0;
        let invalidCount = 0;

        for (const puzzle of data.puzzles) {
            try {
                // Basic validation
                if (this.validatePuzzleStructure(puzzle)) {
                    validCount++;
                } else {
                    invalidCount++;
                    console.log(`Invalid puzzle: ${puzzle.id}`);
                }
            } catch (error) {
                invalidCount++;
                console.log(`Error validating puzzle ${puzzle.id}:`, error.message);
            }
        }

        console.log(`✅ Validation complete: ${validCount} valid, ${invalidCount} invalid`);
        return { validCount, invalidCount };
    }

    validatePuzzleStructure(puzzle) {
        // Check required fields
        if (!puzzle.puzzle || !puzzle.solution || !puzzle.difficulty || !puzzle.clues) {
            return false;
        }

        // Check grid dimensions
        if (puzzle.puzzle.length !== 9 || puzzle.solution.length !== 9) {
            return false;
        }

        for (let i = 0; i < 9; i++) {
            if (puzzle.puzzle[i].length !== 9 || puzzle.solution[i].length !== 9) {
                return false;
            }
        }

        // Check clue count matches
        let actualClues = 0;
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (puzzle.puzzle[row][col] !== 0) {
                    actualClues++;
                }
            }
        }

        return actualClues === puzzle.clues;
    }
}

// Main execution
async function main() {
    const generator = new PuzzleGenerator();
    
    try {
        // Generate all puzzles
        const results = await generator.generateAllPuzzles();
        
        // Validate generated puzzles
        console.log('\\n🔍 Validating generated puzzles...');
        for (const difficulty of ['easy', 'medium', 'hard']) {
            await generator.validatePuzzles(difficulty);
        }
        
        console.log('\\n🎉 Puzzle generation complete!');
        console.log('📁 Check the data/puzzles/ directory for generated files.');
        
    } catch (error) {
        console.error('❌ Puzzle generation failed:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { PuzzleGenerator, SudokuGenerator, SudokuSolver };'''

# Save the puzzle generation script
with open('generate-puzzles.js', 'w', encoding='utf-8') as f:
    f.write(puzzle_generator_script)

print("✅ Created generate-puzzles.js")
print("🏭 Puzzle Generation Script Features:")
print("   - Generates 1100+ puzzles total (400 easy, 400 medium, 300 hard)")
print("   - Validates puzzle uniqueness and difficulty constraints")
print("   - Progress reporting and performance metrics")
print("   - Saves puzzles in organized JSON files")
print("   - Includes validation and summary reporting")
print("   - Can be run independently: node generate-puzzles.js")
print("   - Fault tolerance with retry mechanisms")