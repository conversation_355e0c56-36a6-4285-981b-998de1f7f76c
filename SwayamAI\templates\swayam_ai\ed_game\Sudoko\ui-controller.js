/**
 * UI Controller - Manages user interface interactions
 */

class UIController {
    constructor(gameLogic) {
        this.gameLogic = gameLogic;
        this.selectedCell = null;
        this.selectedNumber = null;
        this.gridElement = document.getElementById('sudoku-grid');
        this.numberButtons = document.querySelectorAll('.number-btn');

        this.initializeGrid();
        this.setupEventListeners();
    }

    initializeGrid() {
        this.gridElement.innerHTML = '';

        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                const cell = document.createElement('div');
                cell.className = 'grid-cell';
                cell.dataset.row = row;
                cell.dataset.col = col;

                cell.addEventListener('click', () => {
                    this.selectCell(row, col);
                });

                this.gridElement.appendChild(cell);
            }
        }
    }

    setupEventListeners() {
        // Number button events
        this.numberButtons.forEach(button => {
            button.addEventListener('click', () => {
                const number = parseInt(button.dataset.number);
                this.handleNumberInput(number);
            });
        });
    }

    displayPuzzle(puzzle) {
        const cells = this.gridElement.querySelectorAll('.grid-cell');

        cells.forEach((cell, index) => {
            const row = Math.floor(index / 9);
            const col = index % 9;
            const value = puzzle[row][col];

            cell.textContent = value === 0 ? '' : value;
            cell.className = 'grid-cell';

            if (value !== 0) {
                cell.classList.add('given');
            }
        });
    }

    displaySolution(solution) {
        const cells = this.gridElement.querySelectorAll('.grid-cell');

        cells.forEach((cell, index) => {
            const row = Math.floor(index / 9);
            const col = index % 9;
            const value = solution[row][col];

            cell.textContent = value;

            if (!cell.classList.contains('given')) {
                cell.classList.add('correct');
            }
        });
    }

    selectCell(row, col) {
        // Clear previous selection
        this.clearSelection();

        // Don't select given cells
        const cell = this.getCellElement(row, col);
        if (cell.classList.contains('given')) {
            return;
        }

        this.selectedCell = { row, col };
        cell.classList.add('selected');

        // Highlight related cells
        this.highlightRelatedCells(row, col);
    }

    clearSelection() {
        if (this.selectedCell) {
            const cell = this.getCellElement(this.selectedCell.row, this.selectedCell.col);
            cell.classList.remove('selected');
            this.selectedCell = null;
        }

        // Clear all highlights
        this.gridElement.querySelectorAll('.grid-cell').forEach(cell => {
            cell.classList.remove('highlighted', 'same-number');
        });
    }

    highlightRelatedCells(row, col) {
        const cells = this.gridElement.querySelectorAll('.grid-cell');
        const selectedValue = this.gameLogic.getCurrentGrid()[row][col];

        cells.forEach((cell, index) => {
            const cellRow = Math.floor(index / 9);
            const cellCol = index % 9;

            // Highlight same row, column, or box
            if (cellRow === row || cellCol === col || 
                (Math.floor(cellRow / 3) === Math.floor(row / 3) && 
                 Math.floor(cellCol / 3) === Math.floor(col / 3))) {
                cell.classList.add('highlighted');
            }

            // Highlight same numbers
            if (selectedValue !== 0 && this.gameLogic.getCurrentGrid()[cellRow][cellCol] === selectedValue) {
                cell.classList.add('same-number');
            }
        });
    }

    handleNumberInput(number) {
        if (!this.selectedCell) return;

        const { row, col } = this.selectedCell;
        const result = this.gameLogic.makeMove(row, col, number);

        if (result !== false) {
            this.updateCellDisplay(row, col, number, result.isCorrect);

            if (result.gameComplete) {
                this.clearSelection();
            }
        }
    }

    updateCellDisplay(row, col, value, isCorrect) {
        const cell = this.getCellElement(row, col);

        cell.textContent = value === 0 ? '' : value;

        // Remove previous state classes
        cell.classList.remove('error', 'correct', 'hint');

        if (value !== 0) {
            if (isCorrect) {
                cell.classList.add('correct');
                setTimeout(() => cell.classList.remove('correct'), 1000);
            } else {
                cell.classList.add('error');
                setTimeout(() => cell.classList.remove('error'), 2000);
            }
        }
    }

    showHint(hint) {
        if (!hint) return;

        const cell = this.getCellElement(hint.row, hint.col);
        cell.textContent = hint.value;
        cell.classList.add('hint');

        // Make the move in game logic
        this.gameLogic.makeMove(hint.row, hint.col, hint.value);

        // Remove hint styling after animation
        setTimeout(() => {
            cell.classList.remove('hint');
            cell.classList.add('correct');
            setTimeout(() => cell.classList.remove('correct'), 1000);
        }, 800);
    }

    handleArrowKey(key) {
        if (!this.selectedCell) {
            // Select center cell if none selected
            this.selectCell(4, 4);
            return;
        }

        let { row, col } = this.selectedCell;

        switch (key) {
            case 'ArrowUp':
                row = Math.max(0, row - 1);
                break;
            case 'ArrowDown':
                row = Math.min(8, row + 1);
                break;
            case 'ArrowLeft':
                col = Math.max(0, col - 1);
                break;
            case 'ArrowRight':
                col = Math.min(8, col + 1);
                break;
        }

        this.selectCell(row, col);
    }

    getCellElement(row, col) {
        return this.gridElement.querySelector(`[data-row="${row}"][data-col="${col}"]`);
    }

    updateNumberButtonStates() {
        // Update number button availability based on current state
        if (!this.selectedCell || !this.gameLogic.getCurrentGrid()) return;

        const { row, col } = this.selectedCell;
        const currentGrid = this.gameLogic.getCurrentGrid();

        this.numberButtons.forEach(button => {
            const number = parseInt(button.dataset.number);

            if (number === 0) {
                // Erase button is always available
                button.disabled = false;
                return;
            }

            const isValid = this.gameLogic.isValidMove(row, col, number);
            button.disabled = !isValid;

            // Visual feedback for invalid moves
            if (!isValid) {
                button.classList.add('invalid');
            } else {
                button.classList.remove('invalid');
            }
        });
    }

    showMessage(message, type = 'info') {
        // Create temporary message element
        const messageEl = document.createElement('div');
        messageEl.className = `game-message ${type}`;
        messageEl.textContent = message;

        document.body.appendChild(messageEl);

        // Animate in
        setTimeout(() => messageEl.classList.add('show'), 100);

        // Remove after delay
        setTimeout(() => {
            messageEl.classList.remove('show');
            setTimeout(() => messageEl.remove(), 300);
        }, 3000);
    }
}

if (typeof window !== 'undefined') {
    window.UIController = UIController;
}