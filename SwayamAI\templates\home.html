{% extends "base.html" %}

{% block title %}SwayamAI · Home{% endblock %}

{% block content %}
<!-- Hero -->
<section class="mb-5">
  <div class="p-4 p-md-5 rounded-4 position-relative overflow-hidden hero-grad hero-compact">
    <div class="row align-items-center g-4">
      <div class="col-12 col-lg-6 text-white">
        <div class="d-flex align-items-center gap-2 flex-wrap mb-3">
          <div class="d-inline-flex align-items-center gap-2 px-3 py-1 rounded-pill"
               style="background: rgba(255,255,255,.15); backdrop-filter: blur(6px);">
            <i class="bi bi-bricks" aria-hidden="true"></i>
            <span class="fw-semibold">SwayamAI</span>
          </div>
          <a class="d-inline-flex align-items-center gap-2 px-3 py-1 rounded-pill text-decoration-none"
             href="{{ url_for('swayam_ml') }}" style="background: rgba(255,255,255,.15); backdrop-filter: blur(6px); color: #fff;">
            <i class="bi bi-stars"></i>
            <span class="fw-semibold">What's new</span>
          </a>
        </div>
  <h1 class="display-6 fw-semibold mb-2">Curate Your Models.<br> Not Your Environment&nbsp;!</h1>
  <p class="lead mb-3" style="color: rgba(255,255,255,.9);">An effortless platform that removes the friction between you and your best work.</p>
        <div class="d-flex align-items-center gap-2 flex-wrap mb-3">
          <span class="trust-badge"><i class="bi bi-mortarboard-fill me-1" aria-hidden="true"></i> Educator</span>
          <span class="trust-badge"><i class="bi bi-person-check-fill me-1" aria-hidden="true"></i> Student</span>
          <span class="trust-badge"><i class="bi bi-rocket-takeoff-fill me-1" aria-hidden="true"></i> Startup</span>
        </div>
        <div class="d-flex gap-2 flex-wrap">
          <a class="btn btn-light text-primary fw-semibold" href="{{ url_for('swayam_ml') }}">
            <i class="bi bi-rocket-takeoff-fill me-1"></i> Get started with SwayamML
          </a>
          <a class="btn btn-outline-light" href="#modules">Explore modules</a>
        </div>
  <!-- Trust badges row reinstated -->
      </div>
      <div class="col-12 col-lg-6">
        <!-- Right side: large icon/image -->
        <div class="ms-lg-3 d-flex justify-content-center justify-content-lg-end">
          <div class="hero-graphic-frame">
            <img class="hero-graphic" src="{{ url_for('static', filename='img/swayam-hero.png') }}?v={{ current_year }}" alt="SwayamAI graphic" />
          </div>
        </div>
      </div>
    </div>
    <!-- Subtle animated shapes in hero background -->
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
  </div>
</section>

<!-- How it works moved below hero -->
<section class="mb-4">
  <div class="rounded-4 p-3 p-md-4" style="background: #ffffff; box-shadow: 0 20px 60px rgba(17,24,39,.08); border: 1px solid rgba(0,0,0,.06);">
    <div class="d-flex align-items-center justify-content-between mb-3">
      <div class="fw-semibold">How it works</div>
    </div>
    <div class="row g-3">
      <div class="col-12 col-md-4">
        <div class="d-flex align-items-start gap-3 h-100 p-3 rounded-3" style="background: rgba(99,102,241,.06);">
          <i class="bi bi-cloud-arrow-up text-primary" aria-hidden="true"></i>
          <div>
            <div class="fw-semibold mb-1">Upload</div>
            <div class="small text-muted">Bring a CSV/XLSX. We infer schema and get you set up.</div>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-4">
        <div class="d-flex align-items-start gap-3 h-100 p-3 rounded-3" style="background: rgba(99,102,241,.06);">
          <i class="bi bi-sliders2 text-primary" aria-hidden="true"></i>
          <div>
            <div class="fw-semibold mb-1">Configure</div>
            <div class="small text-muted">Pick target, features, and options—clear defaults included.</div>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-4">
        <div class="d-flex align-items-start gap-3 h-100 p-3 rounded-3" style="background: rgba(99,102,241,.06);">
          <i class="bi bi-download text-primary" aria-hidden="true"></i>
          <div>
            <div class="fw-semibold mb-1">Export</div>
            <div class="small text-muted">Download model + scripts. Use immediately in your workflow.</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
</section>

<!-- Benefits band moved to page end -->

<!-- Brands strip moved to page end -->

<!-- How it works moved into hero as right-side vertical stack -->

<!-- Modules grid -->
<section id="modules" class="mb-5">
  <div class="d-flex align-items-center justify-content-between mb-3">
    <h2 class="h5 mb-0">Explore modules</h2>
    <a class="btn btn-sm btn-outline-primary" href="{{ url_for('swayam_ml') }}">Open SwayamML</a>
  </div>
  <div class="grid">
    {% for s in sections %}
      <a class="card card-link" href="/{{ s.slug }}" data-animate data-delay="{{ loop.index0 * 60 }}">
        <div class="d-flex align-items-start justify-content-between">
          <div class="d-flex align-items-start gap-3">
            <div class="icon-badge">
              {% if s.slug == 'swayam-ml' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <rect x="3" y="3" width="18" height="14" rx="2"></rect><path d="M3 10h18"></path><path d="M7 21h10"></path>
                </svg>
              {% elif s.slug == 'swayam-images' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <rect x="3" y="5" width="18" height="14" rx="2"></rect><circle cx="8" cy="10" r="2"></circle><path d="M21 19l-6-6-4 4-2-2-4 4"></path>
                </svg>
              {% elif s.slug == 'swayam-rag' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l0 0a2 2 0 1 1-2.83 2.83l0 0A1.65 1.65 0 0 0 15 19.4"></path><path d="M4.6 9A1.65 1.65 0 0 0 4.27 7.18l0 0A2 2 0 1 1 7.1 4.35l0 0A1.65 1.65 0 0 0 9 4.6"></path>
                </svg>
              {% elif s.slug == 'swayam-agents' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <path d="M9 18v-6a3 3 0 0 1 6 0v6"></path><path d="M8 21h8"></path><circle cx="12" cy="5" r="2"></circle>
                </svg>
              {% elif s.slug == 'swayam-chat' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <path d="M21 15a4 4 0 0 1-4 4H7l-4 4V7a4 4 0 0 1 4-4h10a4 4 0 0 1 4 4z"></path>
                </svg>
              {% elif s.slug == 'swayam-api' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <path d="M4 7h16"></path><path d="M4 12h16"></path><path d="M4 17h16"></path>
                </svg>
              {% elif s.slug == 'swayam-ed-game' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <rect x="3" y="7" width="18" height="10" rx="2"></rect><circle cx="8" cy="12" r="1"></circle><circle cx="16" cy="10" r="1"></circle><circle cx="16" cy="14" r="1"></circle>
                </svg>
              {% elif s.slug == 'swayam-quiz-puzzles' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <path d="M14 10V6a2 2 0 1 0-4 0v4H6a2 2 0 1 0 0 4h4v4a2 2 0 1 0 4 0v-4h4a2 2 0 1 0 0-4z"></path>
                </svg>
              {% elif s.slug == 'swayam-building-blocks' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <rect x="3" y="8" width="7" height="7" rx="1"></rect><rect x="14" y="3" width="7" height="7" rx="1"></rect><rect x="14" y="14" width="7" height="7" rx="1"></rect>
                </svg>
              {% elif s.slug == 'swayam-dashboard' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <path d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0z"></path><path d="M12 7v5l3 3"></path>
                </svg>
              {% elif s.slug == 'swayam-courses' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <path d="M4 19.5V6.5a2 2 0 0 1 2-2h12"></path><path d="M20 22V4a2 2 0 0 0-2-2H8"></path><path d="M4 19.5L12 16l8 3.5"></path>
                </svg>
              {% elif s.slug == 'swayam-datasets' %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5v6c0 1.66 4.03 3 9 3s9-1.34 9-3V5"></path><path d="M3 11v6c0 1.66 4.03 3 9 3s9-1.34 9-3v-6"></path>
                </svg>
              {% else %}
                <svg viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                  <rect x="4" y="4" width="16" height="16" rx="2"></rect>
                </svg>
              {% endif %}
            </div>
            <div>
              <div class="fw-semibold">{{ s.title }}</div>
              <div class="small text-muted">{{ s.desc }}</div>
            </div>
          </div>
          <i class="bi bi-arrow-up-right"></i>
        </div>
      </a>
    {% endfor %}
  </div>
</section>

<!-- Testimonials carousel -->
<section class="mb-5" id="testimonials">
  <div class="d-flex align-items-center justify-content-between mb-3">
    <h2 class="h5 mb-0">Community Feedback</h2>
    <div class="d-none d-md-flex gap-2">
      <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev"><i class="bi bi-chevron-left"></i></button>
      <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next"><i class="bi bi-chevron-right"></i></button>
    </div>
  </div>
  <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
    <div class="carousel-inner">
      <div class="carousel-item active">
        <div class="testimonial-card card border-0 shadow-sm p-4" data-animate>
          <div class="d-flex align-items-center mb-3">
            <div class="avatar me-2" aria-hidden="true">TK</div>
            <div>
              <div class="fw-semibold">Tarun K.</div>
              <div class="small text-muted">Head of Data</div>
            </div>
          </div>
          <div class="small text-muted">“We shipped our first model the same day—exported scripts made deployment trivial.”</div>
        </div>
      </div>
      <div class="carousel-item">
        <div class="testimonial-card card border-0 shadow-sm p-4" data-animate>
          <div class="d-flex align-items-center mb-3">
            <div class="avatar me-2">AS</div>
            <div>
              <div class="fw-semibold">Asha S.</div>
              <div class="small text-muted">Educator</div>
            </div>
          </div>
          <div class="small text-muted">“Perfect for workshops—students go from data to results without wrestling with setup.”</div>
        </div>
      </div>
      <div class="carousel-item">
        <div class="testimonial-card card border-0 shadow-sm p-4" data-animate>
          <div class="d-flex align-items-center mb-3">
            <div class="avatar me-2">RB</div>
            <div>
              <div class="fw-semibold">Ravi B.</div>
              <div class="small text-muted">Founder</div>
            </div>
          </div>
          <div class="small text-muted">“A pragmatic toolkit—got us from POC to a demo-ready app in hours.”</div>
        </div>
      </div>
    </div>
    <div class="d-md-none d-flex justify-content-center gap-2 mt-3">
      <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev"><i class="bi bi-chevron-left"></i></button>
      <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next"><i class="bi bi-chevron-right"></i></button>
    </div>
  </div>
</section>

<!-- CTA band -->
<section class="mb-4">
  <div class="p-4 p-md-5 rounded-4 text-white d-flex flex-column flex-lg-row align-items-start align-items-lg-center justify-content-between cta-grad">
    <div class="mb-3 mb-lg-0">
      <div class="fw-semibold">Build your first model in minutes</div>
      <div style="color: rgba(255,255,255,.9);">Upload, configure, and export. No heavy setup required.</div>
    </div>
    <a class="btn btn-light text-primary fw-semibold" href="{{ url_for('swayam_ml') }}">Launch SwayamML</a>
  </div>
</section>

<!-- Benefits band (moved) -->
<section class="mb-5">
  <div class="row g-3 g-md-4">
    <div class="col-12 col-md-4">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);" data-animate data-delay="0">
        <div class="d-flex align-items-center gap-2 mb-2"><i class="bi bi-cpu-fill text-primary"></i><strong>Model Studio</strong></div>
        <div class="text-muted small">Upload, configure, run. We export artifacts and deployment scripts out‑of‑the‑box.</div>
      </div>
    </div>
    <div class="col-12 col-md-4">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);" data-animate data-delay="100">
        <div class="d-flex align-items-center gap-2 mb-2"><i class="bi bi-diagram-3-fill text-primary"></i><strong>Modular</strong></div>
        <div class="text-muted small">Pick and mix: RAG, Agents, APIs, Dashboards—grow capabilities as you go.</div>
      </div>
    </div>
    <div class="col-12 col-md-4">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);" data-animate data-delay="200">
        <div class="d-flex align-items-center gap-2 mb-2"><i class="bi bi-shield-check text-primary"></i><strong>Pragmatic</strong></div>
        <div class="text-muted small">Opinionated defaults, sensible outputs, and simple deployment pathways.</div>
      </div>
    </div>
    <div class="col-12 col-md-4">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);" data-animate data-delay="300">
        <div class="d-flex align-items-center gap-2 mb-2"><i class="bi bi-lightning-charge-fill text-primary"></i><strong>Fast to value</strong></div>
        <div class="text-muted small">From upload to export in minutes. No heavy setup needed.</div>
      </div>
    </div>
    <div class="col-12 col-md-4">
      <div class="p-4 rounded-3 h-100 border" style="border-color: rgba(0,0,0,.06);" data-animate data-delay="400">
        <div class="d-flex align-items-center gap-2 mb-2"><i class="bi bi-cloud-arrow-down text-primary"></i><strong>Deployable</strong></div>
        <div class="text-muted small">Export artifacts and starter scripts you can run anywhere.</div>
      </div>
    </div>
  </div>
</section>

<!-- Brands strip (moved to page end) -->
<section class="mb-5" aria-label="Trusted by">
  <div class="brand-strip d-flex flex-wrap justify-content-center align-items-center gap-4 gap-md-5">
    <div class="brand-logo" data-animate data-delay="0" aria-label="Brand 1">
      <svg class="brand-svg" viewBox="0 0 120 24" fill="none" xmlns="http://www.w3.org/2000/svg" role="img">
        <rect x="2" y="6" width="16" height="12" rx="2" fill="currentColor" opacity="0.85"/>
        <rect x="24" y="6" width="22" height="12" rx="2" fill="currentColor" opacity="0.75"/>
        <rect x="50" y="6" width="18" height="12" rx="2" fill="currentColor" opacity="0.65"/>
        <rect x="72" y="6" width="20" height="12" rx="2" fill="currentColor" opacity="0.55"/>
        <rect x="96" y="6" width="22" height="12" rx="2" fill="currentColor" opacity="0.45"/>
      </svg>
    </div>
    <div class="brand-logo" data-animate data-delay="80" aria-label="Brand 2">
      <svg class="brand-svg" viewBox="0 0 120 24" fill="none" xmlns="http://www.w3.org/2000/svg" role="img">
        <circle cx="12" cy="12" r="8" fill="currentColor" opacity="0.9"/>
        <rect x="26" y="6" width="22" height="12" rx="6" fill="currentColor" opacity="0.7"/>
        <rect x="52" y="6" width="46" height="12" rx="6" fill="currentColor" opacity="0.55"/>
      </svg>
    </div>
    <div class="brand-logo" data-animate data-delay="160" aria-label="Brand 3">
      <svg class="brand-svg" viewBox="0 0 120 24" fill="none" xmlns="http://www.w3.org/2000/svg" role="img">
        <rect x="2" y="9" width="24" height="6" rx="3" fill="currentColor"/>
        <rect x="30" y="9" width="24" height="6" rx="3" fill="currentColor" opacity="0.8"/>
        <rect x="58" y="9" width="24" height="6" rx="3" fill="currentColor" opacity="0.6"/>
        <rect x="86" y="9" width="32" height="6" rx="3" fill="currentColor" opacity="0.45"/>
      </svg>
    </div>
    <div class="brand-logo" data-animate data-delay="240" aria-label="Brand 4">
      <svg class="brand-svg" viewBox="0 0 120 24" fill="none" xmlns="http://www.w3.org/2000/svg" role="img">
        <rect x="2" y="6" width="32" height="12" rx="6" fill="currentColor" opacity="0.9"/>
        <rect x="38" y="6" width="26" height="12" rx="6" fill="currentColor" opacity="0.75"/>
        <circle cx="78" cy="12" r="6" fill="currentColor" opacity="0.6"/>
        <rect x="90" y="6" width="28" height="12" rx="6" fill="currentColor" opacity="0.5"/>
      </svg>
    </div>
    <div class="brand-logo" data-animate data-delay="320" aria-label="Brand 5">
      <svg class="brand-svg" viewBox="0 0 120 24" fill="none" xmlns="http://www.w3.org/2000/svg" role="img">
        <rect x="2" y="6" width="22" height="12" rx="2" fill="currentColor" opacity="0.8"/>
        <rect x="28" y="6" width="22" height="12" rx="2" fill="currentColor" opacity="0.7"/>
        <rect x="54" y="6" width="22" height="12" rx="2" fill="currentColor" opacity="0.6"/>
        <rect x="80" y="6" width="36" height="12" rx="2" fill="currentColor" opacity="0.45"/>
      </svg>
    </div>
    <div class="brand-logo" data-animate data-delay="400" aria-label="Brand 6">
      <svg class="brand-svg" viewBox="0 0 120 24" fill="none" xmlns="http://www.w3.org/2000/svg" role="img">
        <path d="M6 18V6h4l4 6 4-6h4v12h-4V12l-4 6-4-6v6H6z" fill="currentColor" opacity="0.8"/>
        <rect x="58" y="6" width="10" height="12" rx="2" fill="currentColor" opacity="0.7"/>
        <rect x="72" y="6" width="40" height="12" rx="2" fill="currentColor" opacity="0.5"/>
      </svg>
    </div>
  </div>
  <div class="text-center mt-2"><small class="text-muted">Logos are for illustration</small></div>
</section>

<style>
  .grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 18px; }
  .card-link { text-decoration: none; color: inherit; background: white; border-radius: 16px; padding: 18px; border: 1px solid rgba(0,0,0,.05); box-shadow: 0 4px 14px rgba(2,6,23,.06); transition: transform .18s ease, box-shadow .18s ease; }
  .card-link:hover { transform: translateY(-3px); box-shadow: 0 14px 28px rgba(2,6,23,.12); }
  .dark .card-link { background: #0f172a; border-color: #334155; }

  /* Icon badge */
  .icon-badge { width: 42px; height: 42px; display: inline-flex; align-items: center; justify-content: center; border-radius: 12px; color: #fff;
    background: linear-gradient(135deg,#6366f1,#8b5cf6); box-shadow: 0 8px 18px rgba(99,102,241,.35); }
  .icon-badge i { font-size: 18px; }
  .icon-badge svg { width: 22px; height: 22px; }

  /* Brands strip */
  .brand-strip { opacity:.95 }
  .brand-logo { color: #334155; opacity: .85; filter: grayscale(1); }
  .brand-svg { width: 120px; height: 24px; display: block; }
  .dark .brand-logo { color: #cbd5e1; opacity: .85; }

  /* Brand tile */
  .hero-logo-frame { width: 56px; height: 56px; border-radius: 14px; display: inline-flex; align-items: center; justify-content: center; background: rgba(255,255,255,.96); border: 1px solid rgba(255,255,255,.8); box-shadow: 0 6px 18px rgba(2,6,23,.25); }
  .hero-logo-large { width: 40px; height: 40px; object-fit: contain; border-radius: 8px; display: block; }
  @media (min-width: 992px) {
    .hero-logo-frame { width: 72px; height: 72px; border-radius: 16px; }
    .hero-logo-large { width: 56px; height: 56px; border-radius: 10px; }
  }

  /* Trust badge chips */
  .trust-badge { display: inline-block; padding: 4px 10px; border-radius: 999px; background: rgba(255,255,255,.18); color: #fff; font-weight: 600; letter-spacing: .5px; font-size: 11px; }

  /* Glass panel */
  .glass { background: rgba(255,255,255,.2); backdrop-filter: blur(8px); border: 1px solid rgba(255,255,255,.35); box-shadow: 0 20px 60px rgba(17,24,39,.15); }
  .terminal-header { background: rgba(255,255,255,.5); border-bottom: 1px solid rgba(0,0,0,.06); }
  .dot { width: 10px; height: 10px; border-radius: 50%; display: inline-block; }
  .dot-red { background: #ef4444; } .dot-yellow { background: #f59e0b; } .dot-green { background: #22c55e; }

  /* Hero gradients with dark-mode variants */
  .hero-grad { background: linear-gradient(90deg,#6366f1,#8b5cf6); box-shadow: 0 30px 80px rgba(99,102,241,.35); }
  /* Compact hero variant: reduce vertical padding and spacing */
  .hero-compact { padding: .5rem !important; }
  @media (min-width: 768px) { .hero-compact { padding: .75rem !important; } }
  .cta-grad { background: linear-gradient(90deg,#6366f1,#8b5cf6); box-shadow: 0 20px 60px rgba(99,102,241,.25); }
  .dark .hero-grad { background: linear-gradient(90deg,#4f46e5,#7c3aed); box-shadow: 0 30px 80px rgba(79,70,229,.35); }
  .dark .cta-grad { background: linear-gradient(90deg,#4f46e5,#7c3aed); box-shadow: 0 20px 60px rgba(79,70,229,.25); }

  /* Large hero graphic */
  .hero-graphic-frame { display: inline-block; padding: 8px; border-radius: 20px; background: linear-gradient(135deg, var(--sa-primary), var(--sa-primary-2)); box-shadow: 0 18px 44px rgba(99,102,241,.28); }
  .hero-graphic { width: 100%; max-width: 280px; max-height: 170px; height: auto; display: block; border-radius: 16px; background: var(--sa-surface); border: 1px solid rgba(255,255,255,.45); box-shadow: 0 20px 60px rgba(2,6,23,.18); object-fit: contain; }
  @media (min-width: 992px) {
    .hero-graphic { max-width: 320px; max-height: 200px; }
  }
  /* Compress vertical margins inside hero */
  .hero-compact .mb-3 { margin-bottom: .5rem !important; }
  /* Tighten hero text spacing slightly */
  .hero-compact .lead { margin-bottom: .5rem !important; }
  .hero-compact .display-6 { margin-bottom: .25rem !important; }
  @media (min-width: 992px) {
    .hero-watermark { background-size: 560px auto; background-position: right 36px center; opacity: .12; }
  }

  /* Animated shapes */
  .shape { position: absolute; border-radius: 9999px; filter: blur(28px); opacity: .35; animation: float 14s ease-in-out infinite; }
  .shape-1 { width: 160px; height: 160px; background: #e879f9; top: -30px; right: -30px; }
  .shape-2 { width: 130px; height: 130px; background: #60a5fa; bottom: -22px; left: -22px; animation-delay: 2s; }
  @keyframes float { 0%, 100% { transform: translateY(0) translateX(0); } 50% { transform: translateY(-8px) translateX(6px); } }

  /* Animated code caret */
  .cursor { display: inline-block; margin-left: 2px; width: 8px; animation: blink 1.1s steps(1) infinite; color: #0f172a; }
  .dark .cursor { color: #e2e8f0; }
  @keyframes blink { 0%, 49% { opacity: 1; } 50%, 100% { opacity: 0; } }

  /* Progress pulse */
  .bar-anim { width: 18%; animation: progressAnim 3.2s ease-in-out infinite; }
  @keyframes progressAnim {
    0% { width: 12%; }
    50% { width: 86%; }
    100% { width: 12%; }
  }

  /* Steps */
  .step-badge { display:inline-flex; align-items:center; justify-content:center; width:28px; height:28px; border-radius:999px; background: linear-gradient(135deg,#6366f1,#8b5cf6); color:#fff; font-weight:700; }
  .step-icon { color: #ffffff; opacity: .9; font-size: 18px; margin-top: 3px; }

  /* Testimonials */
  .testimonial-card { border-radius: 16px; }
  .avatar { width: 36px; height: 36px; border-radius: 999px; display: inline-flex; align-items: center; justify-content: center; font-weight: 800; color: #fff; background: linear-gradient(135deg,#6366f1,#8b5cf6); }

  /* Scroll-in animation */
  .reveal { opacity: 0; transform: translateY(12px); transition: opacity .45s ease, transform .45s ease; }
  .reveal.in { opacity: 1; transform: none; }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .reveal { opacity: 1 !important; transform: none !important; transition: none !important; }
    .shape { animation: none !important; }
    .bar-anim { animation: none !important; }
    .cursor { animation: none !important; }
  }
</style>

{% block scripts %}
<script>
  (function(){
    try {
      const els = document.querySelectorAll('[data-animate]');
      const reduce = window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      if (reduce) {
        els.forEach(el => el.classList.add('in'));
        return;
      }
      els.forEach(el => el.classList.add('reveal'));
      const io = new IntersectionObserver((entries) => {
        entries.forEach(e => {
          if (e.isIntersecting) {
            const d = e.target.getAttribute('data-delay');
            if (d) e.target.style.transitionDelay = d + 'ms';
            e.target.classList.add('in');
            io.unobserve(e.target);
          }
        });
      }, { threshold: 0.2 });
      els.forEach(el => io.observe(el));
    } catch (err) {}
  })();
</script>
{% endblock %}

{% endblock %}
