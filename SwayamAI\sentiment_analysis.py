import os
import os.path as op
from typing import List, Tu<PERSON>, Any
import pandas as pd
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from importlib import import_module


MODEL_ZOO = {
    # Fast general (English)
    'fast_general': 'distilbert-base-uncased-finetuned-sst-2-english',
    # Accurate general (English)
    'accurate_general': 'siebert/sentiment-roberta-large-english',
    # Financial domain (English)
    'financial': 'ProsusAI/finbert',
    # Multilingual
    'multilingual': 'cardiffnlp/twitter-xlm-roberta-base-sentiment',
}


def _load_dataframe(file_path: str) -> pd.DataFrame:
    _, ext = op.splitext(file_path.lower())
    if ext == '.csv':
        return pd.read_csv(file_path)
    elif ext in ('.xlsx', '.xls'):
        return pd.read_excel(file_path)
    else:
        raise ValueError('Unsupported file format. Please upload CSV or Excel.')


def _standardize_label(label: str, id2label: dict | None = None) -> str:
    """Map model-specific labels to one of: positive | neutral | negative.
    Handles LABEL_N via id2label if provided.
    """
    l = str(label).strip()
    # Map 'LABEL_0/1/2' via id2label if possible
    if l.upper().startswith('LABEL_') and id2label:
        try:
            idx = int(l.split('_')[-1])
            l = str(id2label.get(idx, l))
        except Exception:
            pass
    l_up = l.upper()
    if 'POS' in l_up:
        return 'positive'
    if 'NEU' in l_up:
        return 'neutral'
    if 'NEG' in l_up:
        return 'negative'
    # Unknown: fall back to raw lowercase
    return l.lower()


def sentiment_analysis_process(file_path: str, text_column: str, output_folder: str, profile: str = 'fast_general', neutral_band: bool = False):
    """
    Run sentiment analysis over a text column using a selected pretrained model profile.

    Returns: (summary_df, outputs_list, num_models)
    """
    os.makedirs(output_folder, exist_ok=True)

    if not text_column:
        raise ValueError('Please select a text column for sentiment analysis.')

    df = _load_dataframe(file_path)
    if text_column not in df.columns:
        raise ValueError(f'Text column "{text_column}" not found in the dataset.')

    model_id = MODEL_ZOO.get(profile)
    if not model_id:
        raise ValueError('Unknown sentiment profile: ' + str(profile))

    # Build pipeline
    tok = AutoTokenizer.from_pretrained(model_id)
    mdl = AutoModelForSequenceClassification.from_pretrained(model_id)
    tf = import_module('transformers')
    nlp: Any = tf.pipeline('sentiment-analysis', model=mdl, tokenizer=tok)
    id2label = getattr(getattr(mdl, 'config', None), 'id2label', None)
    try:
        # normalize keys to int in case they're strings
        if isinstance(id2label, dict):
            id2label = {int(k): v for k, v in id2label.items()}
    except Exception:
        pass

    texts = df[text_column].astype(str).fillna("").tolist()

    # Batch to avoid OOM
    results = []
    bs = 64
    for i in range(0, len(texts), bs):
        chunk = texts[i:i+bs]
        preds = nlp(chunk, truncation=True)
        for p in preds:
            raw_label = p.get('label', '')
            score = float(p.get('score', 0.0))
            label = _standardize_label(raw_label, id2label)
            # Optional neutral band for binary profiles
            if neutral_band and profile in ('fast_general', 'accurate_general'):
                # Treat low-confidence predictions around 0.5 as neutral (band: 0.45–0.55)
                if 0.45 <= score <= 0.55:
                    label = 'neutral'
            results.append((label, score))

    out_df = pd.DataFrame({
        text_column: df[text_column].astype(str).fillna("").values,
        'sentiment_label': [r[0] for r in results],
        'sentiment_score': [r[1] for r in results],
    })

    out_csv = op.join(output_folder, 'sentiments.csv')
    out_df.to_csv(out_csv, index=False)

    # Summary counts by label
    summary_df = out_df['sentiment_label'].value_counts(dropna=False).rename_axis('label').reset_index(name='count')
    out_xlsx = op.join(output_folder, 'sentiments_summary.xlsx')
    with pd.ExcelWriter(out_xlsx, engine='openpyxl') as w:
        summary_df.to_excel(w, index=False, sheet_name='summary')

    outputs = [op.basename(out_csv), op.basename(out_xlsx)]
    return summary_df, outputs, 1
