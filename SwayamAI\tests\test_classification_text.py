import os
import sys
import subprocess
import pandas as pd

from Classification_Modelling import classification_process


def make_text_class_df(n=80):
    texts = []
    labels = []
    for i in range(n):
        if i % 2 == 0:
            texts.append(f"I love this product {i}")
            labels.append('pos')
        else:
            texts.append(f"I hate this product {i}")
            labels.append('neg')
    return pd.DataFrame({'text': texts, 'label': labels})


def test_classification_text_e2e(tmp_path, tmp_output_dir, patch_transformers):
    df = make_text_class_df(60)
    inp = tmp_path / 'tc.csv'
    df.to_csv(inp, index=False)

    summary, files, n_models, log = classification_process(
        str(inp), fields=['text'], label='label', output_folder=str(tmp_output_dir)
    )

    # Results workbook
    assert (tmp_output_dir / 'classification_results.xlsx').exists()
    # label encoder and at least one model
    assert (tmp_output_dir / 'label_encoder.pkl').exists()
    assert any(str(p).endswith('_model.pkl') for p in (tmp_output_dir.iterdir()))

    # Try deployment script if present
    dep = tmp_output_dir / 'deployment_script_classification.py'
    if dep.exists():
        out_csv = tmp_path / 'preds.csv'
        proc = subprocess.run([sys.executable, str(dep), '--input', str(inp), '--output', str(out_csv)], capture_output=True, text=True)
        # Allow failure if artifacts not fully present, but prefer success
        assert proc.returncode == 0, proc.stderr
        assert out_csv.exists()
