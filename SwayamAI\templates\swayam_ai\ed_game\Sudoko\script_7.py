# Create remaining essential JavaScript modules

# Game Logic module
game_logic_js = '''/**
 * Game Logic - Core game state and rule management
 */

class GameLogic {
    constructor(solver) {
        this.solver = solver;
        this.originalPuzzle = null;
        this.currentGrid = null;
        this.solution = null;
        this.difficulty = 'medium';
        this.startTime = null;
        this.elapsedTime = 0;
        this.isPaused = false;
        this.timerInterval = null;
        this.errors = 0;
        this.maxErrors = 3;
        this.moveHistory = [];
        
        // Event callbacks
        this.onGameComplete = null;
        this.onGameOver = null;
        this.onGameUpdate = null;
    }

    startNewGame(puzzleData) {
        this.originalPuzzle = this.solver.copyGrid(puzzleData.puzzle);
        this.currentGrid = this.solver.copyGrid(puzzleData.puzzle);
        this.solution = puzzleData.solution;
        this.difficulty = puzzleData.difficulty;
        this.errors = 0;
        this.moveHistory = [];
        this.startTimer();
        
        console.log(`Game started: ${this.difficulty} with ${puzzleData.clues} clues`);
    }

    makeMove(row, col, value) {
        if (!this.currentGrid || this.isPuzzleComplete()) {
            return false;
        }

        // Don't allow moves on given cells
        if (this.originalPuzzle[row][col] !== 0) {
            return false;
        }

        // Store previous value for undo
        const previousValue = this.currentGrid[row][col];
        
        // Make the move
        this.currentGrid[row][col] = value;
        
        // Record move in history
        this.moveHistory.push({
            row, col, 
            previousValue, 
            newValue: value,
            timestamp: Date.now()
        });

        // Check if move is correct
        const isCorrect = value === 0 || value === this.solution[row][col];
        
        if (!isCorrect && value !== 0) {
            this.errors++;
            
            if (this.errors >= this.maxErrors) {
                this.pauseTimer();
                if (this.onGameOver) {
                    this.onGameOver();
                }
                return false;
            }
        }

        // Check for completion
        if (this.isPuzzleComplete()) {
            this.pauseTimer();
            if (this.onGameComplete) {
                this.onGameComplete();
            }
        }

        // Trigger update
        if (this.onGameUpdate) {
            this.onGameUpdate();
        }

        return { isCorrect, gameComplete: this.isPuzzleComplete() };
    }

    isPuzzleComplete() {
        if (!this.currentGrid) return false;
        
        // Check if all cells are filled
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.currentGrid[row][col] === 0) {
                    return false;
                }
            }
        }

        // Validate solution
        return this.solver.isValidSolution(this.currentGrid);
    }

    isValidMove(row, col, value) {
        if (value === 0) return true; // Erasing is always valid
        return this.solver.isValidMove(this.currentGrid, row, col, value);
    }

    getHint() {
        if (!this.currentGrid) return null;
        return this.solver.getHint(this.currentGrid);
    }

    solvePuzzle(solution) {
        if (!this.currentGrid) return;
        
        this.currentGrid = this.solver.copyGrid(solution);
        this.pauseTimer();
    }

    resetPuzzle() {
        if (!this.originalPuzzle) return;
        
        this.currentGrid = this.solver.copyGrid(this.originalPuzzle);
        this.errors = 0;
        this.moveHistory = [];
        this.startTimer();
        
        if (this.onGameUpdate) {
            this.onGameUpdate();
        }
    }

    startTimer() {
        this.startTime = Date.now();
        this.elapsedTime = 0;
        this.isPaused = false;
        
        this.timerInterval = setInterval(() => {
            if (!this.isPaused) {
                this.elapsedTime = Date.now() - this.startTime;
                if (this.onGameUpdate) {
                    this.onGameUpdate();
                }
            }
        }, 1000);
    }

    pauseTimer() {
        this.isPaused = true;
    }

    resumeTimer() {
        if (this.isPaused && this.startTime) {
            this.isPaused = false;
            this.startTime = Date.now() - this.elapsedTime;
        }
    }

    getTimeString() {
        const totalSeconds = Math.floor(this.elapsedTime / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    getElapsedTime() {
        return this.elapsedTime;
    }

    getErrorCount() {
        return this.errors;
    }

    getMaxErrors() {
        return this.maxErrors;
    }

    getDifficulty() {
        return this.difficulty;
    }

    getCurrentGrid() {
        return this.currentGrid;
    }

    hasProgress() {
        if (!this.originalPuzzle || !this.currentGrid) return false;
        
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.originalPuzzle[row][col] !== this.currentGrid[row][col]) {
                    return true;
                }
            }
        }
        return false;
    }

    getGameState() {
        return {
            currentGrid: this.currentGrid,
            originalPuzzle: this.originalPuzzle,
            solution: this.solution,
            difficulty: this.difficulty,
            elapsedTime: this.elapsedTime,
            errors: this.errors,
            moveHistory: this.moveHistory,
            startTime: this.startTime
        };
    }

    loadGameState(gameState, puzzleData) {
        this.currentGrid = gameState.currentGrid;
        this.originalPuzzle = gameState.originalPuzzle;
        this.solution = gameState.solution || puzzleData.solution;
        this.difficulty = gameState.difficulty;
        this.elapsedTime = gameState.elapsedTime || 0;
        this.errors = gameState.errors || 0;
        this.moveHistory = gameState.moveHistory || [];
        
        // Resume timer if game is not complete
        if (!this.isPuzzleComplete()) {
            this.startTime = Date.now() - this.elapsedTime;
            this.startTimer();
        }
    }
}

if (typeof window !== 'undefined') {
    window.GameLogic = GameLogic;
}'''

# UI Controller module
ui_controller_js = '''/**
 * UI Controller - Manages user interface interactions
 */

class UIController {
    constructor(gameLogic) {
        this.gameLogic = gameLogic;
        this.selectedCell = null;
        this.selectedNumber = null;
        this.gridElement = document.getElementById('sudoku-grid');
        this.numberButtons = document.querySelectorAll('.number-btn');
        
        this.initializeGrid();
        this.setupEventListeners();
    }

    initializeGrid() {
        this.gridElement.innerHTML = '';
        
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                const cell = document.createElement('div');
                cell.className = 'grid-cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                cell.addEventListener('click', () => {
                    this.selectCell(row, col);
                });
                
                this.gridElement.appendChild(cell);
            }
        }
    }

    setupEventListeners() {
        // Number button events
        this.numberButtons.forEach(button => {
            button.addEventListener('click', () => {
                const number = parseInt(button.dataset.number);
                this.handleNumberInput(number);
            });
        });
    }

    displayPuzzle(puzzle) {
        const cells = this.gridElement.querySelectorAll('.grid-cell');
        
        cells.forEach((cell, index) => {
            const row = Math.floor(index / 9);
            const col = index % 9;
            const value = puzzle[row][col];
            
            cell.textContent = value === 0 ? '' : value;
            cell.className = 'grid-cell';
            
            if (value !== 0) {
                cell.classList.add('given');
            }
        });
    }

    displaySolution(solution) {
        const cells = this.gridElement.querySelectorAll('.grid-cell');
        
        cells.forEach((cell, index) => {
            const row = Math.floor(index / 9);
            const col = index % 9;
            const value = solution[row][col];
            
            cell.textContent = value;
            
            if (!cell.classList.contains('given')) {
                cell.classList.add('correct');
            }
        });
    }

    selectCell(row, col) {
        // Clear previous selection
        this.clearSelection();
        
        // Don't select given cells
        const cell = this.getCellElement(row, col);
        if (cell.classList.contains('given')) {
            return;
        }
        
        this.selectedCell = { row, col };
        cell.classList.add('selected');
        
        // Highlight related cells
        this.highlightRelatedCells(row, col);
    }

    clearSelection() {
        if (this.selectedCell) {
            const cell = this.getCellElement(this.selectedCell.row, this.selectedCell.col);
            cell.classList.remove('selected');
            this.selectedCell = null;
        }
        
        // Clear all highlights
        this.gridElement.querySelectorAll('.grid-cell').forEach(cell => {
            cell.classList.remove('highlighted', 'same-number');
        });
    }

    highlightRelatedCells(row, col) {
        const cells = this.gridElement.querySelectorAll('.grid-cell');
        const selectedValue = this.gameLogic.getCurrentGrid()[row][col];
        
        cells.forEach((cell, index) => {
            const cellRow = Math.floor(index / 9);
            const cellCol = index % 9;
            
            // Highlight same row, column, or box
            if (cellRow === row || cellCol === col || 
                (Math.floor(cellRow / 3) === Math.floor(row / 3) && 
                 Math.floor(cellCol / 3) === Math.floor(col / 3))) {
                cell.classList.add('highlighted');
            }
            
            // Highlight same numbers
            if (selectedValue !== 0 && this.gameLogic.getCurrentGrid()[cellRow][cellCol] === selectedValue) {
                cell.classList.add('same-number');
            }
        });
    }

    handleNumberInput(number) {
        if (!this.selectedCell) return;
        
        const { row, col } = this.selectedCell;
        const result = this.gameLogic.makeMove(row, col, number);
        
        if (result !== false) {
            this.updateCellDisplay(row, col, number, result.isCorrect);
            
            if (result.gameComplete) {
                this.clearSelection();
            }
        }
    }

    updateCellDisplay(row, col, value, isCorrect) {
        const cell = this.getCellElement(row, col);
        
        cell.textContent = value === 0 ? '' : value;
        
        // Remove previous state classes
        cell.classList.remove('error', 'correct', 'hint');
        
        if (value !== 0) {
            if (isCorrect) {
                cell.classList.add('correct');
                setTimeout(() => cell.classList.remove('correct'), 1000);
            } else {
                cell.classList.add('error');
                setTimeout(() => cell.classList.remove('error'), 2000);
            }
        }
    }

    showHint(hint) {
        if (!hint) return;
        
        const cell = this.getCellElement(hint.row, hint.col);
        cell.textContent = hint.value;
        cell.classList.add('hint');
        
        // Make the move in game logic
        this.gameLogic.makeMove(hint.row, hint.col, hint.value);
        
        // Remove hint styling after animation
        setTimeout(() => {
            cell.classList.remove('hint');
            cell.classList.add('correct');
            setTimeout(() => cell.classList.remove('correct'), 1000);
        }, 800);
    }

    handleArrowKey(key) {
        if (!this.selectedCell) {
            // Select center cell if none selected
            this.selectCell(4, 4);
            return;
        }
        
        let { row, col } = this.selectedCell;
        
        switch (key) {
            case 'ArrowUp':
                row = Math.max(0, row - 1);
                break;
            case 'ArrowDown':
                row = Math.min(8, row + 1);
                break;
            case 'ArrowLeft':
                col = Math.max(0, col - 1);
                break;
            case 'ArrowRight':
                col = Math.min(8, col + 1);
                break;
        }
        
        this.selectCell(row, col);
    }

    getCellElement(row, col) {
        return this.gridElement.querySelector(`[data-row="${row}"][data-col="${col}"]`);
    }

    updateNumberButtonStates() {
        // Update number button availability based on current state
        if (!this.selectedCell || !this.gameLogic.getCurrentGrid()) return;
        
        const { row, col } = this.selectedCell;
        const currentGrid = this.gameLogic.getCurrentGrid();
        
        this.numberButtons.forEach(button => {
            const number = parseInt(button.dataset.number);
            
            if (number === 0) {
                // Erase button is always available
                button.disabled = false;
                return;
            }
            
            const isValid = this.gameLogic.isValidMove(row, col, number);
            button.disabled = !isValid;
            
            // Visual feedback for invalid moves
            if (!isValid) {
                button.classList.add('invalid');
            } else {
                button.classList.remove('invalid');
            }
        });
    }

    showMessage(message, type = 'info') {
        // Create temporary message element
        const messageEl = document.createElement('div');
        messageEl.className = `game-message ${type}`;
        messageEl.textContent = message;
        
        document.body.appendChild(messageEl);
        
        // Animate in
        setTimeout(() => messageEl.classList.add('show'), 100);
        
        // Remove after delay
        setTimeout(() => {
            messageEl.classList.remove('show');
            setTimeout(() => messageEl.remove(), 300);
        }, 3000);
    }
}

if (typeof window !== 'undefined') {
    window.UIController = UIController;
}'''

# Storage module
storage_js = '''/**
 * Storage - Local storage management for game state and statistics
 */

class SudokuStorage {
    constructor() {
        this.GAME_STATE_KEY = 'sudoku_game_state';
        this.STATISTICS_KEY = 'sudoku_statistics';
        this.SETTINGS_KEY = 'sudoku_settings';
    }

    saveGameState(gameState) {
        try {
            localStorage.setItem(this.GAME_STATE_KEY, JSON.stringify(gameState));
            return true;
        } catch (error) {
            console.error('Failed to save game state:', error);
            return false;
        }
    }

    loadGameState() {
        try {
            const data = localStorage.getItem(this.GAME_STATE_KEY);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Failed to load game state:', error);
            return null;
        }
    }

    clearGameState() {
        try {
            localStorage.removeItem(this.GAME_STATE_KEY);
            return true;
        } catch (error) {
            console.error('Failed to clear game state:', error);
            return false;
        }
    }

    saveGameCompletion(completionData) {
        try {
            const stats = this.loadStatistics();
            
            if (!stats.completions) {
                stats.completions = [];
            }
            
            stats.completions.push(completionData);
            
            // Update aggregate stats
            const difficulty = completionData.difficulty;
            if (!stats.byDifficulty[difficulty]) {
                stats.byDifficulty[difficulty] = {
                    played: 0,
                    completed: 0,
                    bestTime: null,
                    averageTime: 0,
                    totalTime: 0
                };
            }
            
            const diffStats = stats.byDifficulty[difficulty];
            diffStats.completed++;
            diffStats.totalTime += completionData.time;
            diffStats.averageTime = diffStats.totalTime / diffStats.completed;
            
            if (!diffStats.bestTime || completionData.time < diffStats.bestTime) {
                diffStats.bestTime = completionData.time;
            }
            
            // Keep only last 100 completions
            if (stats.completions.length > 100) {
                stats.completions = stats.completions.slice(-100);
            }
            
            this.saveStatistics(stats);
            return true;
            
        } catch (error) {
            console.error('Failed to save completion:', error);
            return false;
        }
    }

    loadStatistics() {
        try {
            const data = localStorage.getItem(this.STATISTICS_KEY);
            return data ? JSON.parse(data) : this.getDefaultStatistics();
        } catch (error) {
            console.error('Failed to load statistics:', error);
            return this.getDefaultStatistics();
        }
    }

    saveStatistics(stats) {
        try {
            localStorage.setItem(this.STATISTICS_KEY, JSON.stringify(stats));
            return true;
        } catch (error) {
            console.error('Failed to save statistics:', error);
            return false;
        }
    }

    getDefaultStatistics() {
        return {
            totalGamesStarted: 0,
            totalGamesCompleted: 0,
            byDifficulty: {
                easy: { played: 0, completed: 0, bestTime: null, averageTime: 0, totalTime: 0 },
                medium: { played: 0, completed: 0, bestTime: null, averageTime: 0, totalTime: 0 },
                hard: { played: 0, completed: 0, bestTime: null, averageTime: 0, totalTime: 0 }
            },
            completions: []
        };
    }

    incrementGamesStarted(difficulty) {
        try {
            const stats = this.loadStatistics();
            stats.totalGamesStarted++;
            stats.byDifficulty[difficulty].played++;
            this.saveStatistics(stats);
        } catch (error) {
            console.error('Failed to increment games started:', error);
        }
    }

    saveSettings(settings) {
        try {
            localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('Failed to save settings:', error);
            return false;
        }
    }

    loadSettings() {
        try {
            const data = localStorage.getItem(this.SETTINGS_KEY);
            return data ? JSON.parse(data) : this.getDefaultSettings();
        } catch (error) {
            console.error('Failed to load settings:', error);
            return this.getDefaultSettings();
        }
    }

    getDefaultSettings() {
        return {
            theme: 'default',
            soundEnabled: false,
            autoSave: true,
            showTimer: true,
            showErrors: true,
            highlightRelated: true,
            highlightSameNumbers: true
        };
    }

    exportData() {
        try {
            const data = {
                gameState: this.loadGameState(),
                statistics: this.loadStatistics(),
                settings: this.loadSettings(),
                exportDate: new Date().toISOString()
            };
            
            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error('Failed to export data:', error);
            return null;
        }
    }

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.gameState) {
                this.saveGameState(data.gameState);
            }
            
            if (data.statistics) {
                this.saveStatistics(data.statistics);
            }
            
            if (data.settings) {
                this.saveSettings(data.settings);
            }
            
            return true;
        } catch (error) {
            console.error('Failed to import data:', error);
            return false;
        }
    }

    clearAllData() {
        try {
            localStorage.removeItem(this.GAME_STATE_KEY);
            localStorage.removeItem(this.STATISTICS_KEY);
            localStorage.removeItem(this.SETTINGS_KEY);
            return true;
        } catch (error) {
            console.error('Failed to clear all data:', error);
            return false;
        }
    }

    getStorageUsage() {
        try {
            let total = 0;
            
            const gameState = localStorage.getItem(this.GAME_STATE_KEY);
            const statistics = localStorage.getItem(this.STATISTICS_KEY);
            const settings = localStorage.getItem(this.SETTINGS_KEY);
            
            if (gameState) total += gameState.length * 2; // UTF-16 encoding
            if (statistics) total += statistics.length * 2;
            if (settings) total += settings.length * 2;
            
            return {
                bytes: total,
                kb: (total / 1024).toFixed(2),
                gameState: gameState ? gameState.length * 2 : 0,
                statistics: statistics ? statistics.length * 2 : 0,
                settings: settings ? settings.length * 2 : 0
            };
        } catch (error) {
            console.error('Failed to calculate storage usage:', error);
            return null;
        }
    }
}

if (typeof window !== 'undefined') {
    window.SudokuStorage = SudokuStorage;
}'''

# Difficulty Manager module
difficulty_manager_js = '''/**
 * Difficulty Manager - Manages puzzle difficulty and loading
 */

class DifficultyManager {
    constructor() {
        this.puzzleDatabase = {
            easy: [],
            medium: [],
            hard: []
        };
        this.currentIndices = {
            easy: 0,
            medium: 0,
            hard: 0
        };
        this.isInitialized = false;
    }

    async init() {
        console.log('🎯 Initializing Difficulty Manager...');
        
        try {
            // Try to load puzzle data from files
            await this.loadPuzzleDatabase();
            
            // If no data loaded, generate sample puzzles
            if (!this.hasValidDatabase()) {
                console.log('📝 No puzzle database found, generating sample puzzles...');
                await this.generateSamplePuzzles();
            }
            
            this.isInitialized = true;
            console.log('✅ Difficulty Manager initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize Difficulty Manager:', error);
            // Generate minimal fallback puzzles
            this.generateFallbackPuzzles();
            this.isInitialized = true;
        }
    }

    async loadPuzzleDatabase() {
        const difficulties = ['easy', 'medium', 'hard'];
        
        for (const difficulty of difficulties) {
            try {
                const response = await fetch(`data/puzzles/${difficulty}.json`);
                if (response.ok) {
                    const data = await response.json();
                    this.puzzleDatabase[difficulty] = data.puzzles || [];
                    console.log(`📚 Loaded ${this.puzzleDatabase[difficulty].length} ${difficulty} puzzles`);
                }
            } catch (error) {
                console.warn(`⚠️ Could not load ${difficulty} puzzles:`, error);
            }
        }
    }

    hasValidDatabase() {
        return Object.values(this.puzzleDatabase).some(puzzles => puzzles.length > 0);
    }

    async generateSamplePuzzles() {
        // Generate a small set of sample puzzles for each difficulty
        const generator = new SudokuGenerator();
        
        const sampleCounts = { easy: 20, medium: 20, hard: 15 };
        
        for (const [difficulty, count] of Object.entries(sampleCounts)) {
            console.log(`🔧 Generating ${count} sample ${difficulty} puzzles...`);
            
            for (let i = 0; i < count; i++) {
                try {
                    const puzzle = generator.generatePuzzle(difficulty);
                    this.puzzleDatabase[difficulty].push(puzzle);
                } catch (error) {
                    console.warn(`Failed to generate ${difficulty} puzzle ${i + 1}:`, error);
                }
            }
            
            console.log(`✅ Generated ${this.puzzleDatabase[difficulty].length} ${difficulty} puzzles`);
        }
    }

    generateFallbackPuzzles() {
        console.log('🆘 Generating minimal fallback puzzles...');
        
        // Create very basic hardcoded puzzles as absolute fallback
        const fallbackPuzzles = {
            easy: [
                {
                    puzzle: [
                        [5,3,0,0,7,0,0,0,0],
                        [6,0,0,1,9,5,0,0,0],
                        [0,9,8,0,0,0,0,6,0],
                        [8,0,0,0,6,0,0,0,3],
                        [4,0,0,8,0,3,0,0,1],
                        [7,0,0,0,2,0,0,0,6],
                        [0,6,0,0,0,0,2,8,0],
                        [0,0,0,4,1,9,0,0,5],
                        [0,0,0,0,8,0,0,7,9]
                    ],
                    solution: [
                        [5,3,4,6,7,8,9,1,2],
                        [6,7,2,1,9,5,3,4,8],
                        [1,9,8,3,4,2,5,6,7],
                        [8,5,9,7,6,1,4,2,3],
                        [4,2,6,8,5,3,7,9,1],
                        [7,1,3,9,2,4,8,5,6],
                        [9,6,1,5,3,7,2,8,4],
                        [2,8,7,4,1,9,6,3,5],
                        [3,4,5,2,8,6,1,7,9]
                    ],
                    difficulty: 'easy',
                    clues: 42,
                    id: 'fallback-easy-1'
                }
            ],
            medium: [
                {
                    puzzle: [
                        [0,0,0,6,0,0,4,0,0],
                        [7,0,0,0,0,3,6,0,0],
                        [0,0,0,0,9,1,0,8,0],
                        [0,0,0,0,0,0,0,0,0],
                        [0,5,0,1,8,0,0,0,3],
                        [0,0,0,3,0,6,0,4,5],
                        [0,4,0,2,0,0,0,6,0],
                        [9,0,3,0,0,0,0,0,0],
                        [0,2,0,0,0,0,1,0,0]
                    ],
                    solution: [
                        [1,3,2,6,7,8,4,5,9],
                        [7,8,4,5,2,3,6,9,1],
                        [5,6,9,4,9,1,3,8,2],
                        [4,9,6,8,5,7,2,1,3],
                        [2,5,7,1,8,9,6,3,4],
                        [8,1,3,3,4,6,9,7,5],
                        [3,4,1,2,3,5,7,6,8],
                        [9,7,3,6,1,4,5,2,1],
                        [6,2,5,7,6,2,1,4,3]
                    ],
                    difficulty: 'medium',
                    clues: 28,
                    id: 'fallback-medium-1'
                }
            ],
            hard: [
                {
                    puzzle: [
                        [0,0,0,0,0,0,0,1,0],
                        [4,0,0,0,0,0,0,0,0],
                        [0,2,0,0,0,0,0,0,0],
                        [0,0,0,0,5,0,4,0,7],
                        [0,0,8,0,0,0,3,0,0],
                        [0,0,1,0,9,0,0,0,0],
                        [3,0,0,4,0,0,2,0,0],
                        [0,5,0,1,0,0,0,0,0],
                        [0,0,0,8,0,6,0,0,0]
                    ],
                    solution: [
                        [6,9,3,7,8,4,5,1,2],
                        [4,8,7,5,1,2,9,3,6],
                        [1,2,5,9,6,3,8,7,4],
                        [9,3,2,6,5,1,4,8,7],
                        [5,6,8,2,4,7,3,9,1],
                        [7,4,1,3,9,8,6,2,5],
                        [3,1,9,4,7,5,2,6,8],
                        [8,5,6,1,2,9,7,4,3],
                        [2,7,4,8,3,6,1,5,9]
                    ],
                    difficulty: 'hard',
                    clues: 24,
                    id: 'fallback-hard-1'
                }
            ]
        };

        this.puzzleDatabase = fallbackPuzzles;
        console.log('🆘 Fallback puzzles ready');
    }

    getPuzzle(difficulty = 'medium') {
        if (!this.isInitialized) {
            throw new Error('Difficulty Manager not initialized');
        }

        const puzzles = this.puzzleDatabase[difficulty];
        if (!puzzles || puzzles.length === 0) {
            throw new Error(`No ${difficulty} puzzles available`);
        }

        // Get current puzzle and increment index
        const currentIndex = this.currentIndices[difficulty];
        const puzzle = puzzles[currentIndex];
        
        // Move to next puzzle (wrap around if at end)
        this.currentIndices[difficulty] = (currentIndex + 1) % puzzles.length;

        return puzzle;
    }

    getRandomPuzzle(difficulty = 'medium') {
        if (!this.isInitialized) {
            throw new Error('Difficulty Manager not initialized');
        }

        const puzzles = this.puzzleDatabase[difficulty];
        if (!puzzles || puzzles.length === 0) {
            throw new Error(`No ${difficulty} puzzles available`);
        }

        const randomIndex = Math.floor(Math.random() * puzzles.length);
        return puzzles[randomIndex];
    }

    getPuzzleCount(difficulty) {
        return this.puzzleDatabase[difficulty]?.length || 0;
    }

    getTotalPuzzleCount() {
        return Object.values(this.puzzleDatabase).reduce((total, puzzles) => total + puzzles.length, 0);
    }

    getDifficultyStats() {
        const stats = {};
        
        for (const [difficulty, puzzles] of Object.entries(this.puzzleDatabase)) {
            if (puzzles.length > 0) {
                const clues = puzzles.map(p => p.clues);
                stats[difficulty] = {
                    count: puzzles.length,
                    minClues: Math.min(...clues),
                    maxClues: Math.max(...clues),
                    avgClues: (clues.reduce((a, b) => a + b, 0) / clues.length).toFixed(1)
                };
            }
        }
        
        return stats;
    }

    validatePuzzle(puzzle) {
        const required = ['puzzle', 'solution', 'difficulty', 'clues'];
        return required.every(field => puzzle.hasOwnProperty(field));
    }

    addCustomPuzzle(puzzle, difficulty) {
        if (!this.validatePuzzle(puzzle)) {
            throw new Error('Invalid puzzle format');
        }

        puzzle.difficulty = difficulty;
        puzzle.id = puzzle.id || `custom-${Date.now()}`;
        
        this.puzzleDatabase[difficulty].push(puzzle);
        console.log(`Added custom ${difficulty} puzzle: ${puzzle.id}`);
    }

    reset() {
        this.currentIndices = {
            easy: 0,
            medium: 0,
            hard: 0
        };
    }
}

if (typeof window !== 'undefined') {
    window.DifficultyManager = DifficultyManager;
}'''

# Save all the modules
files_to_create = [
    ('game-logic.js', game_logic_js),
    ('ui-controller.js', ui_controller_js),
    ('storage.js', storage_js),
    ('difficulty-manager.js', difficulty_manager_js)
]

for filename, content in files_to_create:
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

print("✅ Created remaining JavaScript modules:")
for filename, _ in files_to_create:
    print(f"   📄 {filename}")

print("\n🎮 Complete module set includes:")
print("   - Game Logic: Core game state and rule management")
print("   - UI Controller: User interface interactions and display")
print("   - Storage: Local storage for game state and statistics")
print("   - Difficulty Manager: Puzzle loading and difficulty management")