"""
Deployment script for NON-NLP Regression.

Usage (PowerShell):
  python deployment_script_regression_non_nlp.py --input data.csv --output preds.csv

Expects the following files in the same folder:
    - swayamml.conf with keys: model_pkl_file_name, encoding_pkl_file_name, imputer_pkl_file_name, scaler_pkl_file_name, features, numerical_cols
  - <model>.pkl, encoding.pkl, imputer.pkl, scaler.pkl

The script applies the saved frequency encoding for categoricals, imputes+scales only original numeric columns,
then selects the final feature set and predicts.
"""
import os
import sys
import argparse
import configparser
import joblib
import pandas as pd
import numpy as np


def _as_list(csv_str: str) -> list:
    if not csv_str:
        return []
    return [c.strip() for c in str(csv_str).split(',') if str(c).strip()]


def load_config(base_dir: str) -> dict:
    cfg_path = os.path.join(base_dir, 'swayamml.conf')
    if not os.path.exists(cfg_path):
        raise FileNotFoundError(f"Config not found: {cfg_path}")
    cp = configparser.ConfigParser()
    cp.read(cfg_path)
    d = cp['DEFAULT'] if 'DEFAULT' in cp else {}
    return {
        'model': d.get('model_pkl_file_name', ''),
        'encoding': d.get('encoding_pkl_file_name', 'encoding.pkl'),
        'imputer': d.get('imputer_pkl_file_name', 'imputer.pkl'),
        'scaler': d.get('scaler_pkl_file_name', 'scaler.pkl'),
        'features': _as_list(d.get('features', '')),
        'numerical_cols': _as_list(d.get('numerical_cols', '')),
    }


def load_artifacts(base_dir: str, cfg: dict):
    model = joblib.load(os.path.join(base_dir, cfg['model'])) if cfg.get('model') else None
    enc = joblib.load(os.path.join(base_dir, cfg['encoding'])) if cfg.get('encoding') else {}
    imp = joblib.load(os.path.join(base_dir, cfg['imputer'])) if cfg.get('imputer') else None
    scl = joblib.load(os.path.join(base_dir, cfg['scaler'])) if cfg.get('scaler') else None
    return model, enc, imp, scl


def apply_encodings(df: pd.DataFrame, encodings: dict) -> pd.DataFrame:
    if not isinstance(encodings, dict):
        return df
    out = df.copy()
    for col, mapping in encodings.items():
        if col in out.columns and isinstance(mapping, dict):
            out[col] = out[col].map(mapping).fillna(0)
    return out


def impute_and_scale_numeric(df: pd.DataFrame, numerical_cols: list, imputer, scaler) -> pd.DataFrame:
    out = df.copy()
    num = list(numerical_cols or [])
    if not num:
        return out
    # Ensure numeric dtype where possible
    for c in num:
        if c in out.columns and out[c].dtype == object:
            out[c] = pd.to_numeric(out[c], errors='coerce')
    # Align columns and apply; keep DataFrame during transforms to preserve feature names
    X = out.reindex(columns=num)
    if imputer is not None:
        Xt = imputer.transform(X)
        X = pd.DataFrame(Xt, index=out.index, columns=num)
    if scaler is not None:
        Xt = scaler.transform(X)
        X = pd.DataFrame(Xt, index=out.index, columns=num)
    out.loc[:, num] = X
    return out


from typing import Optional


def predict_file(input_path: str, output_path: str, base_dir: Optional[str] = None):
    base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
    cfg = load_config(base_dir)
    model, enc, imp, scl = load_artifacts(base_dir, cfg)
    if model is None:
        raise RuntimeError("Model artifact not found or could not be loaded.")

    # Read input
    _, ext = os.path.splitext(input_path.lower())
    if ext == '.csv':
        df = pd.read_csv(input_path)
    elif ext in ('.xlsx', '.xls'):
        df = pd.read_excel(input_path)
    else:
        raise ValueError("Unsupported input file type. Use CSV/XLSX.")

    # Preprocess
    dfp = apply_encodings(df, enc)
    dfp = impute_and_scale_numeric(dfp, cfg.get('numerical_cols', []), imp, scl)

    # Build features vector (final selected feature set)
    feat = list(cfg.get('features', []))
    if not feat:
        # fallback: use numerical cols only
        feat = list(cfg.get('numerical_cols', []))
    X = dfp.reindex(columns=feat, fill_value=0)

    preds = model.predict(X)
    out_df = df.copy()
    out_df['predicted_value'] = preds
    # add actual if present
    # try to infer from common names
    for cand in ('target', 'label', 'y', 'actual'):
        if cand in df.columns:
            out_df['actual_value'] = df[cand]
            break

    if output_path.lower().endswith('.xlsx'):
        with pd.ExcelWriter(output_path, engine='openpyxl') as w:
            out_df.to_excel(w, index=False)
    else:
        out_df.to_csv(output_path, index=False)


def main(argv=None):
    p = argparse.ArgumentParser(description='NON-NLP Regression Deployment Predictor')
    # Support both flags and positional args
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/XLSX')
    p.add_argument('positional_output', nargs='?', help='Path to output predictions CSV/XLSX')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/XLSX')
    p.add_argument('--output', dest='flag_output', help='Path to output predictions CSV/XLSX')
    args = p.parse_args(argv)

    # Resolve input path: flag wins
    input_path = args.flag_input or args.positional_input
    if not input_path:
        p.error('An input file is required. Provide --input or a positional input path.')

    # Resolve output path with sensible default based on input extension
    output_path = args.flag_output or args.positional_output
    if not output_path:
        base, ext = os.path.splitext(input_path)
        if ext.lower() == '.csv':
            output_path = f"{base}_preds.csv"
        elif ext.lower() in ('.xlsx', '.xls'):
            output_path = f"{base}_preds.xlsx"
        else:
            output_path = f"{base}_preds.csv"

    predict_file(input_path, output_path)


if __name__ == '__main__':
    sys.exit(main())
