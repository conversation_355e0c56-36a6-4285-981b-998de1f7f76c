import os
from typing import Optional
from log_util import setup_logger


def setup_logging(logger_name: str = 'sway<PERSON>i', output_folder: Optional[str] = None, level=None):
    """Compatibility wrapper used across the repo. Returns (logger, log_file_path).

    Keeps callers that expect `setup_logging` working while delegating to `log_util.setup_logger`.
    """
    logger, path = setup_logger(logger_name, output_folder=output_folder)
    return logger, path
