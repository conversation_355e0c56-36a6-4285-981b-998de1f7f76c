import os
import sys
import subprocess
import pandas as pd
import numpy as np

from anomaly_detection_numeric import anomaly_detection_numeric_process


def make_numeric_df(n=60, seed=0):
    rng = np.random.default_rng(seed)
    base = rng.normal(0, 1, size=(n, 3))
    # inject a few outliers
    base[-3:] += 10
    df = pd.DataFrame(base, columns=['x1', 'x2', 'x3'])
    df['id'] = [f'id_{i}' for i in range(n)]
    return df


def test_anomaly_all_mode(tmp_path, tmp_output_dir):
    df = make_numeric_df(n=50)
    inp = tmp_path / 'inp.csv'
    df.to_csv(inp, index=False)

    summary, files, n_models = anomaly_detection_numeric_process(
        str(inp), fields=['x1','x2','x3'], output_folder=str(tmp_output_dir), mode='all', index_column='id'
    )

    # summary is DataFrame with model rows
    assert hasattr(summary, 'columns') and not summary.empty
    assert n_models >= 1

    # artifacts
    for name in ['anomalies.csv', 'anomaly_results.xlsx', 'imputer.pkl', 'scaler.pkl', 'anomaly_model.pkl', 'deployment_script_anomaly_numeric.py']:
        assert name in files
        assert (tmp_output_dir / name).exists()

    # run deployment script
    out_csv = tmp_path / 'pred.csv'
    script = tmp_output_dir / 'deployment_script_anomaly_numeric.py'
    proc = subprocess.run([sys.executable, str(script), '--input', str(inp), '--output', str(out_csv)], capture_output=True, text=True)
    assert proc.returncode == 0, proc.stderr
    assert out_csv.exists()
    out = pd.read_csv(out_csv)
    assert 'is_anomaly' in out.columns and 'anomaly_score' in out.columns


def test_anomaly_per_column_mode(tmp_path, tmp_output_dir):
    df = make_numeric_df(n=45, seed=42)
    inp = tmp_path / 'inp.csv'
    df.to_csv(inp, index=False)

    summary, files, n_models = anomaly_detection_numeric_process(
        str(inp), fields=['x1','x2','x3'], output_folder=str(tmp_output_dir), mode='per_column', index_column='id'
    )

    assert hasattr(summary, 'columns') and not summary.empty
    assert n_models >= 6  # 6 algorithms * columns

    # per-column outputs
    assert (tmp_output_dir / 'anomalies_per_column.csv').exists()
    assert (tmp_output_dir / 'anomaly_per_column_results.xlsx').exists()
    # script exists
    assert (tmp_output_dir / 'deployment_script_anomaly_numeric_per_column.py').exists()
