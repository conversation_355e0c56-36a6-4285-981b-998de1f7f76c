class SudokuGenerator{ constructor(){ this.solver=new SudokuSolver(); this.GRID_SIZE=9; this.EMPTY=0; this.DIFFICULTY_SETTINGS={ easy:{minClues:40,maxClues:45,maxIterations:100,requiredTechniques:['naked-single','hidden-single']}, medium:{minClues:30,maxClues:35,maxIterations:200,requiredTechniques:['naked-single','hidden-single','naked-pairs']}, hard:{minClues:25,maxClues:30,maxIterations:300,requiredTechniques:['naked-single','hidden-single','naked-pairs','pointing-pairs']}}; }
 generateCompleteGrid(){ const grid=this.createEmptyGrid(); this.fillDiagonalBoxes(grid); this.solver.solve(grid); return grid; }
 generatePuzzle(difficulty='medium'){ const settings=this.DIFFICULTY_SETTINGS[difficulty]; if(!settings) throw new Error(`Invalid difficulty: ${difficulty}`); let best=null, attempts=0, maxAttempts=50; while(attempts<maxAttempts&&!best){ try{ const solution=this.generateCompleteGrid(); const puzzle=this.createPuzzleFromSolution(solution,settings); if(puzzle){ const clues=this.countClues(puzzle); if(clues>=settings.minClues&&clues<=settings.maxClues){ best={puzzle,solution,difficulty,clues,id:this.generatePuzzleId()}; } } }catch(_e){ } attempts++; } if(!best){ return this.generateFallbackPuzzle(difficulty); } return best; }
 createEmptyGrid(){ return Array(9).fill(null).map(()=>Array(9).fill(0)); }
 fillDiagonalBoxes(grid){ for(let b=0;b<3;b++){ this.fillBox(grid,b*3,b*3); } }
 fillBox(grid,rs,cs){ const nums=this.shuffleArray([1,2,3,4,5,6,7,8,9]); let ix=0; for(let i=0;i<3;i++){ for(let j=0;j<3;j++){ grid[rs+i][cs+j]=nums[ix++]; } } }
 createPuzzleFromSolution(solution,settings){ const puzzle=this.solver.copyGrid(solution); const cells=this.getAllCellPositions(); this.shuffleArray(cells); let removed=0; const maxRemove=81-settings.minClues; for(const [r,c] of cells){ if(removed>=maxRemove) break; const orig=puzzle[r][c]; puzzle[r][c]=this.EMPTY; if(!this.solver.hasUniqueSolution(puzzle)){ puzzle[r][c]=orig; } else { removed++; } const clues=81-removed; if(clues<=settings.maxClues&&clues>=settings.minClues){ if(this.meetsDifficultyRequirements(puzzle,settings)) break; } } return puzzle; }
 getAllCellPositions(){ const pos=[]; for(let r=0;r<9;r++){ for(let c=0;c<9;c++){ pos.push([r,c]); } } return pos; }
 meetsDifficultyRequirements(puzzle,settings){ const clues=this.countClues(puzzle); return clues>=settings.minClues&&clues<=settings.maxClues; }
 countClues(grid){ let count=0; for(let r=0;r<9;r++){ for(let c=0;c<9;c++){ if(grid[r][c]!==this.EMPTY) count++; } } return count; }
 generateFallbackPuzzle(difficulty){ const solution=this.generateCompleteGrid(); const puzzle=this.solver.copyGrid(solution); const settings=this.DIFFICULTY_SETTINGS[difficulty]; const targetClues=settings.maxClues; const cellsToRemove=81-targetClues; const positions=this.getAllCellPositions(); this.shuffleArray(positions); for(let i=0;i<Math.min(cellsToRemove,positions.length);i++){ const [r,c]=positions[i]; puzzle[r][c]=this.EMPTY; } return {puzzle,solution,difficulty,clues:targetClues,id:this.generatePuzzleId(),fallback:true}; }
 shuffleArray(a){ for(let i=a.length-1;i>0;i--){ const j=Math.floor(Math.random()*(i+1)); [a[i],a[j]]=[a[j],a[i]]; } return a; }
 generatePuzzleId(){ return Date.now().toString(36)+Math.random().toString(36).substr(2); }
 generatePuzzleSet(difficulty,count){ const out=[]; for(let i=0;i<count;i++){ try{ out.push(this.generatePuzzle(difficulty)); }catch(_e){} } return out; }
 validatePuzzle(obj){ const {puzzle,solution}=obj; if(!puzzle||!solution) return false; return true; }
 puzzleToString(p){ const ps=this.solver.gridToString(p.puzzle), ss=this.solver.gridToString(p.solution); return JSON.stringify({puzzle:ps,solution:ss,difficulty:p.difficulty,clues:p.clues,id:p.id}); }
 stringToPuzzle(str){ const d=JSON.parse(str); return {puzzle:this.solver.stringToGrid(d.puzzle),solution:this.solver.stringToGrid(d.solution),difficulty:d.difficulty,clues:d.clues,id:d.id}; }
}
if(typeof module!=='undefined'&&module.exports){ module.exports=SudokuGenerator; } else if(typeof window!=='undefined'){ window.SudokuGenerator=SudokuGenerator; }
