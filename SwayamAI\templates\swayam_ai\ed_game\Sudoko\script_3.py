# Create the Sudoku solver using backtracking algorithm
solver_js = '''/**
 * Sudoku Solver - Backtracking Algorithm
 * Efficiently solves Sudoku puzzles and validates solutions
 */

class SudokuSolver {
    constructor() {
        this.GRID_SIZE = 9;
        this.BOX_SIZE = 3;
        this.EMPTY = 0;
    }

    /**
     * Main solve function using backtracking
     * @param {number[][]} grid - 9x9 Sudoku grid (0 for empty cells)
     * @returns {boolean} - True if solved, false if unsolvable
     */
    solve(grid) {
        // Find next empty cell
        const emptyCell = this.findEmptyCell(grid);
        if (!emptyCell) {
            return true; // Puzzle solved
        }

        const [row, col] = emptyCell;

        // Try numbers 1-9
        for (let num = 1; num <= 9; num++) {
            if (this.isValidMove(grid, row, col, num)) {
                grid[row][col] = num;

                // Recursively solve
                if (this.solve(grid)) {
                    return true;
                }

                // Backtrack
                grid[row][col] = this.EMPTY;
            }
        }

        return false; // No solution found
    }

    /**
     * Find the next empty cell using Most Constrained Variable heuristic
     * @param {number[][]} grid - Sudoku grid
     * @returns {number[]|null} - [row, col] or null if no empty cells
     */
    findEmptyCell(grid) {
        let minOptions = 10;
        let bestCell = null;

        for (let row = 0; row < this.GRID_SIZE; row++) {
            for (let col = 0; col < this.GRID_SIZE; col++) {
                if (grid[row][col] === this.EMPTY) {
                    const options = this.countValidOptions(grid, row, col);
                    if (options < minOptions) {
                        minOptions = options;
                        bestCell = [row, col];
                        if (options === 0) {
                            return bestCell; // Dead end, return immediately
                        }
                    }
                }
            }
        }

        return bestCell;
    }

    /**
     * Count valid number options for a cell
     * @param {number[][]} grid - Sudoku grid
     * @param {number} row - Row index
     * @param {number} col - Column index
     * @returns {number} - Count of valid options
     */
    countValidOptions(grid, row, col) {
        let count = 0;
        for (let num = 1; num <= 9; num++) {
            if (this.isValidMove(grid, row, col, num)) {
                count++;
            }
        }
        return count;
    }

    /**
     * Check if placing a number is valid
     * @param {number[][]} grid - Sudoku grid
     * @param {number} row - Row index
     * @param {number} col - Column index
     * @param {number} num - Number to place (1-9)
     * @returns {boolean} - True if valid move
     */
    isValidMove(grid, row, col, num) {
        return this.isValidInRow(grid, row, num) &&
               this.isValidInColumn(grid, col, num) &&
               this.isValidInBox(grid, row, col, num);
    }

    /**
     * Check if number is valid in row
     * @param {number[][]} grid - Sudoku grid
     * @param {number} row - Row index
     * @param {number} num - Number to check
     * @returns {boolean} - True if valid
     */
    isValidInRow(grid, row, num) {
        for (let col = 0; col < this.GRID_SIZE; col++) {
            if (grid[row][col] === num) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check if number is valid in column
     * @param {number[][]} grid - Sudoku grid
     * @param {number} col - Column index
     * @param {number} num - Number to check
     * @returns {boolean} - True if valid
     */
    isValidInColumn(grid, col, num) {
        for (let row = 0; row < this.GRID_SIZE; row++) {
            if (grid[row][col] === num) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check if number is valid in 3x3 box
     * @param {number[][]} grid - Sudoku grid
     * @param {number} row - Row index
     * @param {number} col - Column index
     * @param {number} num - Number to check
     * @returns {boolean} - True if valid
     */
    isValidInBox(grid, row, col, num) {
        const boxStartRow = Math.floor(row / this.BOX_SIZE) * this.BOX_SIZE;
        const boxStartCol = Math.floor(col / this.BOX_SIZE) * this.BOX_SIZE;

        for (let r = boxStartRow; r < boxStartRow + this.BOX_SIZE; r++) {
            for (let c = boxStartCol; c < boxStartCol + this.BOX_SIZE; c++) {
                if (grid[r][c] === num) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Validate a complete Sudoku solution
     * @param {number[][]} grid - Completed Sudoku grid
     * @returns {boolean} - True if valid solution
     */
    isValidSolution(grid) {
        // Check all rows
        for (let row = 0; row < this.GRID_SIZE; row++) {
            if (!this.isValidSet(grid[row])) {
                return false;
            }
        }

        // Check all columns
        for (let col = 0; col < this.GRID_SIZE; col++) {
            const column = [];
            for (let row = 0; row < this.GRID_SIZE; row++) {
                column.push(grid[row][col]);
            }
            if (!this.isValidSet(column)) {
                return false;
            }
        }

        // Check all 3x3 boxes
        for (let boxRow = 0; boxRow < 3; boxRow++) {
            for (let boxCol = 0; boxCol < 3; boxCol++) {
                const box = [];
                for (let r = boxRow * 3; r < (boxRow + 1) * 3; r++) {
                    for (let c = boxCol * 3; c < (boxCol + 1) * 3; c++) {
                        box.push(grid[r][c]);
                    }
                }
                if (!this.isValidSet(box)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check if a set contains all numbers 1-9 exactly once
     * @param {number[]} set - Array of 9 numbers
     * @returns {boolean} - True if valid set
     */
    isValidSet(set) {
        const seen = new Set();
        for (const num of set) {
            if (num < 1 || num > 9 || seen.has(num)) {
                return false;
            }
            seen.add(num);
        }
        return seen.size === 9;
    }

    /**
     * Get valid numbers for a specific cell
     * @param {number[][]} grid - Sudoku grid
     * @param {number} row - Row index
     * @param {number} col - Column index
     * @returns {number[]} - Array of valid numbers
     */
    getValidNumbers(grid, row, col) {
        if (grid[row][col] !== this.EMPTY) {
            return [];
        }

        const validNumbers = [];
        for (let num = 1; num <= 9; num++) {
            if (this.isValidMove(grid, row, col, num)) {
                validNumbers.push(num);
            }
        }
        return validNumbers;
    }

    /**
     * Check if puzzle has unique solution
     * @param {number[][]} grid - Sudoku grid
     * @returns {boolean} - True if unique solution exists
     */
    hasUniqueSolution(grid) {
        const gridCopy1 = this.copyGrid(grid);
        const gridCopy2 = this.copyGrid(grid);

        // Try to find first solution
        if (!this.solve(gridCopy1)) {
            return false; // No solution
        }

        // Try to find second solution by modifying solve to continue searching
        const solutionCount = this.countSolutions(gridCopy2, 2); // Stop after finding 2
        return solutionCount === 1;
    }

    /**
     * Count number of solutions (up to maxCount)
     * @param {number[][]} grid - Sudoku grid
     * @param {number} maxCount - Maximum solutions to find
     * @returns {number} - Number of solutions found
     */
    countSolutions(grid, maxCount = 2) {
        const emptyCell = this.findEmptyCell(grid);
        if (!emptyCell) {
            return 1; // Found one solution
        }

        const [row, col] = emptyCell;
        let solutionCount = 0;

        for (let num = 1; num <= 9; num++) {
            if (this.isValidMove(grid, row, col, num)) {
                grid[row][col] = num;
                solutionCount += this.countSolutions(grid, maxCount - solutionCount);
                grid[row][col] = this.EMPTY;

                if (solutionCount >= maxCount) {
                    break; // Early termination
                }
            }
        }

        return solutionCount;
    }

    /**
     * Create deep copy of grid
     * @param {number[][]} grid - Original grid
     * @returns {number[][]} - Copied grid
     */
    copyGrid(grid) {
        return grid.map(row => [...row]);
    }

    /**
     * Convert string format to grid (for puzzle loading)
     * @param {string} puzzleString - 81 character string (0 for empty)
     * @returns {number[][]} - 9x9 grid
     */
    stringToGrid(puzzleString) {
        if (puzzleString.length !== 81) {
            throw new Error('Puzzle string must be exactly 81 characters');
        }

        const grid = [];
        for (let i = 0; i < 9; i++) {
            const row = [];
            for (let j = 0; j < 9; j++) {
                const char = puzzleString[i * 9 + j];
                row.push(char === '.' || char === '0' ? 0 : parseInt(char));
            }
            grid.push(row);
        }
        return grid;
    }

    /**
     * Convert grid to string format
     * @param {number[][]} grid - 9x9 grid
     * @returns {string} - 81 character string
     */
    gridToString(grid) {
        return grid.flat().map(cell => cell === 0 ? '0' : cell.toString()).join('');
    }

    /**
     * Find hint for current puzzle state
     * @param {number[][]} grid - Current puzzle state
     * @returns {object|null} - {row, col, value} or null if no hint
     */
    getHint(grid) {
        // Try to find a cell with only one valid option
        for (let row = 0; row < this.GRID_SIZE; row++) {
            for (let col = 0; col < this.GRID_SIZE; col++) {
                if (grid[row][col] === this.EMPTY) {
                    const validNumbers = this.getValidNumbers(grid, row, col);
                    if (validNumbers.length === 1) {
                        return {
                            row: row,
                            col: col,
                            value: validNumbers[0],
                            technique: 'Hidden Single'
                        };
                    }
                }
            }
        }

        // If no obvious hint, find any valid move
        for (let row = 0; row < this.GRID_SIZE; row++) {
            for (let col = 0; col < this.GRID_SIZE; col++) {
                if (grid[row][col] === this.EMPTY) {
                    const validNumbers = this.getValidNumbers(grid, row, col);
                    if (validNumbers.length > 0) {
                        return {
                            row: row,
                            col: col,
                            value: validNumbers[0],
                            technique: 'Trial and Error'
                        };
                    }
                }
            }
        }

        return null; // No hint available
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SudokuSolver;
} else if (typeof window !== 'undefined') {
    window.SudokuSolver = SudokuSolver;
}'''

# Save Sudoku solver file
with open('sudoku-solver.js', 'w', encoding='utf-8') as f:
    f.write(solver_js)

print("✅ Created sudoku-solver.js")
print("🧠 Solver Features:")
print("   - Backtracking algorithm with constraint propagation")
print("   - Most Constrained Variable (MCV) heuristic for optimization")
print("   - Solution validation and uniqueness checking")
print("   - Hint generation with technique identification")
print("   - Multiple format support (string/grid conversion)")
print("   - Efficient cell selection and pruning")
print("   - Solution counting for puzzle generation")