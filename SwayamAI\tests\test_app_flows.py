import os
import io
import json
import zipfile
import pandas as pd
import numpy as np
import pytest
from app import app as flask_app


@pytest.fixture
def client():
    flask_app.config['TESTING'] = True
    with flask_app.test_client() as c:
        yield c


def _post_file(client, route, filename, df, use_case, form_extra=None):
    buf = io.BytesIO()
    df.to_csv(buf, index=False)
    buf.seek(0)
    data = {'use_case': use_case}
    if form_extra:
        data.update(form_extra)
    resp = client.post(route, data={**data, 'file': (buf, filename)}, content_type='multipart/form-data')
    return resp


def test_app_upload_and_run_non_nlp_classification(client, tmp_path):
    # small dataset
    df = pd.DataFrame({
        'f1': [1,2,3,4,5,6,7,8,9,10],
        'f2': [0,1,0,1,0,1,0,1,0,1],
        'y': ['A','A','A','A','B','B','B','B','B','B'],
    })
    # upload
    r = _post_file(client, '/upload', 'train.csv', df, 'non_nlp_classification')
    assert r.status_code == 200
    assert b'configure' in r.data or b'Configure' in r.data

    # Extract job info from the response HTML
    # For simplicity, directly call /run using the known job id and fields
    # This avoids parsing HTML; we simulate form POST
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(r.data, 'html.parser')
    job_id_input = soup.find('input', attrs={'name': 'job_id'})
    filename_input = soup.find('input', attrs={'name': 'filename'})
    assert job_id_input is not None and filename_input is not None
    job_id = job_id_input['value']
    filename = filename_input['value']

    data = {
        'job_id': job_id,
        'use_case': 'non_nlp_classification',
        'filename': filename,
        'fields': ['f1','f2'],
        'label': 'y'
    }
    r2 = client.post('/run', data=data)
    assert r2.status_code == 200
    # should render results with downloads list
    assert b'results' in r2.data or b'Download' in r2.data


def test_swayam_ed_game_routes(client):
    # Hub should load
    r = client.get('/swayam-ed-game')
    assert r.status_code == 200
    assert b'SwayamEdGame' in r.data or b'Sudoku' in r.data

    # Sudoku page should load
    r2 = client.get('/swayam-ed-game/sudoku')
    assert r2.status_code == 200
    # Basic elements
    assert b'Sudoku' in r2.data
    # Check for grid and controls above the grid
    assert b'sudoku-grid' in r2.data
    assert b'undo-btn' in r2.data
    assert b'solve-btn' in r2.data
