"""
Deployment script for NLP Clustering to export saved per-row cluster assignments.

Usage (PowerShell):
    # Backward-compatible flags
    python deployment_script_clustering.py --input data.xlsx --output assignments.xlsx

    # Or just provide the input path (output will default to data_assignments.csv or data_assignments.xlsx)
    python deployment_script_clustering.py data.csv

Expects in the same folder:
    - swayamml.conf with keys:
      embedding_name (tfidf|bert|w2v)
      model_name (column name in clustering_Results.xlsx to use)
      results_file_name (clustering_Results.xlsx)
  - clustering_Results.xlsx with a sheet that includes per-row cluster assignments column matching model_name

Note: This script reads the saved per-row cluster assignments (for reproducibility of the run). For fresh clustering
on new data, re-run the UI pipeline to generate new models/embeddings.
"""
import os
import sys
import argparse
import configparser
import pandas as pd
import re


def load_config(base_dir: str) -> dict:
    cfg_path = os.path.join(base_dir, 'swayamml.conf')
    if not os.path.exists(cfg_path):
        raise FileNotFoundError(f"Config not found: {cfg_path}")
    cp = configparser.ConfigParser()
    cp.read(cfg_path)
    d = cp['DEFAULT'] if 'DEFAULT' in cp else {}
    return {
        'embedding_name': d.get('embedding_name', ''),
        'model_name': d.get('model_name', ''),
        'results_file_name': d.get('results_file_name', 'clustering_Results.xlsx'),
    }


def _resolve(base_dir: str, path: str) -> str:
    if not path:
        return ''
    p = str(path)
    return os.path.normpath(p) if os.path.isabs(p) else os.path.normpath(os.path.join(base_dir, p))


def predict_assignments(input_path: str, output_path: str, base_dir: str | None = None):
    base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
    cfg = load_config(base_dir)
    # Load the previously generated clustering workbook to fetch assignments
    res_path = _resolve(base_dir, cfg.get('results_file_name', 'clustering_Results.xlsx'))
    if not os.path.exists(res_path):
        raise FileNotFoundError(f"Results workbook not found: {res_path}")

    # Prefer 'Top 3 clusters' sheet if present, else legacy 'top3_clusters', else 'clusters'
    try:
        xl = pd.ExcelFile(res_path)
        if 'Top 3 clusters' in xl.sheet_names:
            sheet = 'Top 3 clusters'
        elif 'top3_clusters' in xl.sheet_names:
            sheet = 'top3_clusters'
        else:
            sheet = 'clusters'
        df_res = xl.parse(sheet)
    except Exception as e:
        raise RuntimeError(f"Failed to read results workbook: {e}")

    model_col = cfg.get('model_name')
    if not model_col or model_col not in df_res.columns:
        # fallback: choose the first model column present after inputs/Cleaned_data
        candidates = [c for c in df_res.columns if c.lower().startswith(('kmeans', 'agglomerative', 'dbscan', 'affinity'))]
        if not candidates:
            raise RuntimeError('No clustering model columns found in workbook to extract assignments.')
        model_col = candidates[0]

    # Read input to align columns to output
    _, ext = os.path.splitext(input_path.lower())
    if ext == '.csv':
        df_in = pd.read_csv(input_path)
    elif ext in ('.xlsx', '.xls'):
        df_in = pd.read_excel(input_path)
    else:
        raise ValueError('Unsupported input file type. Use CSV/XLSX.')

    # Build output by copying input and appending the saved model assignments
    out = df_in.copy()
    # if worksheet has fewer rows than input, fill with -1
    if len(df_res) >= len(out):
        out[model_col] = df_res[model_col].iloc[:len(out)].values
    else:
        import numpy as np
        vals = list(df_res[model_col].values) + [-1] * (len(out) - len(df_res))
        out[model_col] = vals

    if output_path.lower().endswith('.xlsx'):
        with pd.ExcelWriter(output_path, engine='openpyxl') as w:
            out.to_excel(w, index=False)
    else:
        out.to_csv(output_path, index=False)


def main(argv=None):
    p = argparse.ArgumentParser(description='NLP Clustering Deployment (assignments exporter)')
    # Support both flags and positional args
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/XLSX')
    p.add_argument('positional_output', nargs='?', help='Path to output CSV/XLSX containing assignments')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/XLSX')
    p.add_argument('--output', dest='flag_output', help='Path to output CSV/XLSX containing assignments')
    args = p.parse_args(argv)

    # Resolve input path: flag wins
    input_path = args.flag_input or args.positional_input
    if not input_path:
        p.error('An input file is required. Provide --input or a positional input path.')

    # Resolve output path: flag wins; else positional; else default based on input
    output_path = args.flag_output or args.positional_output
    if not output_path:
        base, ext = os.path.splitext(input_path)
        if ext.lower() == '.csv':
            output_path = f"{base}_assignments.csv"
        elif ext.lower() in ('.xlsx', '.xls'):
            output_path = f"{base}_assignments.xlsx"
        else:
            output_path = f"{base}_assignments.csv"

    predict_assignments(input_path, output_path)


if __name__ == '__main__':
    sys.exit(main())
