import os
import uuid
import traceback
import pandas as pd

from Cluster_Modelling import clustering_process

if __name__ == '__main__':
    try:
        sample_file = os.path.join(os.path.dirname(__file__), '..', 'Tweets.xlsx')
        sample_file = os.path.abspath(sample_file)
        print('Using file:', sample_file)
        df = pd.read_excel(sample_file)
        print('Columns:', df.columns.tolist())
        if df.shape[1] == 0:
            print('No columns in sample file, aborting')
        else:
            fields = [df.columns[0]]
            out_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'outputs', 'test_job_' + str(uuid.uuid4())))
            os.makedirs(out_dir, exist_ok=True)
            print('Output dir:', out_dir)
            try:
                ret = clustering_process(sample_file, fields, out_dir)
                print('Return type:', type(ret))
                print('Return repr:', repr(ret))
                try:
                    summary, customer_files, num_models, processing_logger = ret
                    print('Summary type:', type(summary))
                    print('Num models:', num_models)
                    print('Processing logger keys:', processing_logger.keys() if isinstance(processing_logger, dict) else processing_logger)
                except Exception as e:
                    print('Could not unpack return:', e)
                    print('Raw return:', ret)
            except Exception as e:
                print('clustering_process raised exception:')
                traceback.print_exc()
            print('Output dir contents:', os.listdir(out_dir))
    except Exception as e:
        print('Test harness failed:')
        traceback.print_exc()
