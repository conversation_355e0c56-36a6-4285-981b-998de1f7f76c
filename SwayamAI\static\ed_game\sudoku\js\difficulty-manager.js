class DifficultyManager{ constructor(){ this.puzzleDatabase={easy:[],medium:[],hard:[]}; this.currentIndices={easy:0,medium:0,hard:0}; this.isInitialized=false; }
 async init(){ try{ await this.loadPuzzleDatabase(); if(!this.hasValidDatabase()){ await this.generateSamplePuzzles(); } this.isInitialized=true; }catch(_e){ this.generateFallbackPuzzles(); this.isInitialized=true; } }
 async loadPuzzleDatabase(){ const diffs=['easy','medium','hard']; for(const d of diffs){ try{ const resp=await fetch(`${window.SUDOKU_ASSET_BASE || ''}data/puzzles/${d}.json`); if(resp.ok){ const data=await resp.json(); this.puzzleDatabase[d]=data.puzzles||[]; } }catch(_e){} } }
 hasValidDatabase(){ return Object.values(this.puzzleDatabase).some(p=>p.length>0); }
 async generateSamplePuzzles(){ const gen=new SudokuGenerator(); const counts={easy:5,medium:5,hard:3}; for(const [d,c] of Object.entries(counts)){ for(let i=0;i<c;i++){ try{ this.puzzleDatabase[d].push(gen.generatePuzzle(d)); }catch(_e){} } } }
 generateFallbackPuzzles(){ this.puzzleDatabase={ easy:[{puzzle:[[5,3,0,0,7,0,0,0,0],[6,0,0,1,9,5,0,0,0],[0,9,8,0,0,0,0,6,0],[8,0,0,0,6,0,0,0,3],[4,0,0,8,0,3,0,0,1],[7,0,0,0,2,0,0,0,6],[0,6,0,0,0,0,2,8,0],[0,0,0,4,1,9,0,0,5],[0,0,0,0,8,0,0,7,9]], solution:[[5,3,4,6,7,8,9,1,2],[6,7,2,1,9,5,3,4,8],[1,9,8,3,4,2,5,6,7],[8,5,9,7,6,1,4,2,3],[4,2,6,8,5,3,7,9,1],[7,1,3,9,2,4,8,5,6],[9,6,1,5,3,7,2,8,4],[2,8,7,4,1,9,6,3,5],[3,4,5,2,8,6,1,7,9]], difficulty:'easy', clues:42, id:'fallback-easy-1'}], medium:[{puzzle:[[0,0,0,2,6,0,7,0,1],[6,8,0,0,7,0,0,9,0],[1,9,0,0,0,4,5,0,0],[8,2,0,1,0,0,0,4,0],[0,0,4,6,0,2,9,0,0],[0,5,0,0,0,3,0,2,8],[0,0,9,3,0,0,0,7,4],[0,4,0,0,5,0,0,3,6],[7,0,3,0,1,8,0,0,0]], solution:[[4,3,5,2,6,9,7,8,1],[6,8,2,5,7,1,4,9,3],[1,9,7,8,3,4,5,6,2],[8,2,6,1,9,5,3,4,7],[3,7,4,6,8,2,9,1,5],[9,5,1,7,4,3,6,2,8],[5,1,9,3,2,6,8,7,4],[2,4,8,9,5,7,1,3,6],[7,6,3,4,1,8,2,5,9]], difficulty:'medium', clues:30, id:'fallback-medium-1'}], hard:[{puzzle:[[0,0,0,0,0,0,0,1,0],[4,0,0,0,0,0,0,0,0],[0,2,0,0,0,0,0,0,0],[0,0,0,0,5,0,4,0,7],[0,0,8,0,0,0,3,0,0],[0,0,1,0,9,0,0,0,0],[3,0,0,4,0,0,2,0,0],[0,5,0,1,0,0,0,0,0],[0,0,0,8,0,6,0,0,0]], solution:[[6,9,3,7,8,4,5,1,2],[4,8,7,5,1,2,9,3,6],[1,2,5,9,6,3,8,7,4],[9,3,2,6,5,1,4,8,7],[5,6,8,2,4,7,3,9,1],[7,4,1,3,9,8,6,2,5],[3,1,9,4,7,5,2,6,8],[8,5,6,1,2,9,7,4,3],[2,7,4,8,3,6,1,5,9]], difficulty:'hard', clues:24, id:'fallback-hard-1'}] }; }
 getPuzzle(d='medium'){ if(!this.isInitialized) throw new Error('Difficulty Manager not initialized'); const arr=this.puzzleDatabase[d]; if(!arr||arr.length===0) throw new Error(`No ${d} puzzles available`); const idx=this.currentIndices[d]; const p=arr[idx]; this.currentIndices[d]=(idx+1)%arr.length; return p; }
 getRandomPuzzle(d='medium'){ const arr=this.puzzleDatabase[d]; if(!arr||arr.length===0) throw new Error(`No ${d} puzzles available`); return arr[Math.floor(Math.random()*arr.length)]; }
}
if(typeof window!=='undefined'){ window.DifficultyManager=DifficultyManager; }
