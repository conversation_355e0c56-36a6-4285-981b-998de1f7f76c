class UIController {
  constructor(gameLogic) {
    this.gameLogic = gameLogic;
    this.selectedCell = null;
    this.gridElement = document.getElementById('sudoku-grid');
    this.numberPanel = null;
    this.initializeGrid();
    this.setupEventListeners();
  }

  initializeGrid() {
    this.gridElement.innerHTML = '';
    for (let r = 0; r < 9; r++) {
      for (let c = 0; c < 9; c++) {
        const cell = document.createElement('div');
        cell.className = 'grid-cell';
        cell.dataset.row = r;
        cell.dataset.col = c;
        cell.addEventListener('click', () => {
          this.selectCell(r, c);
        });
        this.gridElement.appendChild(cell);
      }
    }
    this.initializeNumberPanel();
  }

  initializeNumberPanel() {
    this.numberPanel = document.getElementById('number-popup');
    const buttons = this.numberPanel.querySelectorAll('.popup-btn');
    buttons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const number = parseInt(btn.dataset.number);
        this.handleNumberInput(number);
      });
    });
    
    // Initially hide the panel
    this.numberPanel.style.display = 'none';
  }

  setupEventListeners() {
    /* Number panel handles number input now */
  }

  displayPuzzle(p) {
    const cells = this.gridElement.querySelectorAll('.grid-cell');
    cells.forEach((cell, idx) => {
      const r = Math.floor(idx / 9), c = idx % 9, v = p[r][c];
      cell.textContent = v === 0 ? '' : v;
      cell.className = 'grid-cell';
      if (v !== 0) cell.classList.add('given');
    });
  }

  displaySolution(sol) {
    const cells = this.gridElement.querySelectorAll('.grid-cell');
    cells.forEach((cell, idx) => {
      const r = Math.floor(idx / 9), c = idx % 9, v = sol[r][c];
      cell.textContent = v;
      if (!cell.classList.contains('given')) cell.classList.add('correct');
    });
  }

  selectCell(r, c) {
    this.clearSelection();
    const cell = this.getCellElement(r, c);
    if (cell.classList.contains('given')) return;
    
    this.selectedCell = { row: r, col: c };
    cell.classList.add('selected');
    this.highlightRelatedCells(r, c);
    this.showNumberPanel();
  }

  showNumberPanel() {
    if (this.numberPanel) {
      this.numberPanel.style.display = 'block';
    }
  }

  hideNumberPanel() {
    if (this.numberPanel) {
      this.numberPanel.style.display = 'none';
    }
  }

  clearSelection() {
    if (this.selectedCell) {
      const cell = this.getCellElement(this.selectedCell.row, this.selectedCell.col);
      if (cell) cell.classList.remove('selected');
      this.selectedCell = null;
    }
    this.gridElement.querySelectorAll('.grid-cell').forEach(el => el.classList.remove('highlighted', 'same-number'));
    this.hideNumberPanel();
  }

  highlightRelatedCells(row, col) {
    const cells = this.gridElement.querySelectorAll('.grid-cell');
    const value = this.gameLogic.getCurrentGrid()[row][col];
    cells.forEach((cell, idx) => {
      const r = Math.floor(idx / 9), c = idx % 9;
      if (r === row || c === col || (Math.floor(r / 3) === Math.floor(row / 3) && Math.floor(c / 3) === Math.floor(col / 3)))
        cell.classList.add('highlighted');
      if (value !== 0 && this.gameLogic.getCurrentGrid()[r][c] === value)
        cell.classList.add('same-number');
    });
  }

  handleNumberInput(n) {
    if (!this.selectedCell) return;
    const { row, col } = this.selectedCell;
    const res = this.gameLogic.makeMove(row, col, n);
    if (res !== false) {
      this.updateCellDisplay(row, col, n, res.isCorrect);
      if (res.gameComplete) {
        this.clearSelection();
      }
    }
  }

  updateCellDisplay(r, c, val, isCorrect) {
    const cell = this.getCellElement(r, c);
    cell.textContent = val === 0 ? '' : val;
    // keep error until corrected
    cell.classList.remove('correct', 'hint');
    if (val === 0) {
      cell.classList.remove('error');
      return;
    }
    if (isCorrect) {
      cell.classList.remove('error');
      cell.classList.add('correct');
      setTimeout(() => cell.classList.remove('correct'), 800);
    } else {
      cell.classList.add('error');
    }
  }

  refreshCell(r, c, val) {
    const cell = this.getCellElement(r, c);
    if (!cell) return;
    cell.textContent = val === 0 ? '' : val;
    cell.classList.remove('error', 'correct', 'hint', 'selected');
  }

  handleArrowKey(key) {
    if (!this.selectedCell) {
      this.selectCell(4, 4);
      return;
    }
    let { row, col } = this.selectedCell;
    if (key === 'ArrowUp') row = Math.max(0, row - 1);
    else if (key === 'ArrowDown') row = Math.min(8, row + 1);
    else if (key === 'ArrowLeft') col = Math.max(0, col - 1);
    else if (key === 'ArrowRight') col = Math.min(8, col + 1);
    this.selectCell(row, col);
  }

  getCellElement(r, c) {
    return this.gridElement.querySelector(`[data-row="${r}"][data-col="${c}"]`);
  }

  showHint(h) {
    if (!h) return;
    const cell = this.getCellElement(h.row, h.col);
    cell.textContent = h.value;
    cell.classList.add('hint');
    this.gameLogic.makeMove(h.row, h.col, h.value);
    setTimeout(() => {
      cell.classList.remove('hint');
      cell.classList.add('correct');
      setTimeout(() => cell.classList.remove('correct'), 800);
    }, 600);
  }
}

if (typeof window !== 'undefined') {
  window.UIController = UIController;
}
