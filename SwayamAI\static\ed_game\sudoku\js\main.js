class SudokuApp{ constructor(){ this.solver=new SudokuSolver(); this.generator=new SudokuGenerator(); this.gameLogic=null; this.uiController=null; this.storage=new SudokuStorage(); this.difficultyManager=new DifficultyManager(); this.currentPuzzle=null; this.isInitialized=false; }
 async init(){ 
   try{ 
     this.showLoading(true); 
     await this.initializeComponents(); 
     await this.loadGameState(); 
     this.setupEventListeners(); 
     if(!this.currentPuzzle){ 
       await this.startNewGame(); 
     } 
     
     // Update stats display on initialization
     this.updateStats();
     
     this.showLoading(false); 
     this.isInitialized=true; 
   } catch(_e){ 
     alert('Failed to initialize Sudoku'); 
     this.showLoading(false); 
   } 
 }
 async initializeComponents(){ this.gameLogic=new GameLogic(this.solver); this.uiController=new UIController(this.gameLogic); await this.difficultyManager.init(); }
 setupEventListeners(){ const solveBtn=document.getElementById('solve-btn'); if(solveBtn) solveBtn.addEventListener('click',()=>this.solvePuzzle()); const hintBtn=document.getElementById('hint-btn'); if(hintBtn) hintBtn.addEventListener('click',()=>this.showHint()); const resetBtn=document.getElementById('reset-btn'); if(resetBtn) resetBtn.addEventListener('click',()=>this.resetPuzzle()); const undoBtn=document.getElementById('undo-btn'); if(undoBtn) undoBtn.addEventListener('click',()=>this.handleUndo()); const pauseBtn=document.getElementById('pause-btn'); if(pauseBtn) pauseBtn.addEventListener('click',()=>this.togglePause()); const diffSel=document.getElementById('difficulty-select'); if(diffSel) diffSel.addEventListener('change',e=>this.changeDifficulty(e.target.value,false)); this.bindDifficultyCards(); this.bindStatsActions(); const ngm=document.getElementById('new-game-modal-btn'); if(ngm) ngm.addEventListener('click',()=>{ this.hideModal(); this.startNewGame(); }); const cm=document.getElementById('close-modal-btn'); if(cm) cm.addEventListener('click',()=>this.hideModal()); document.addEventListener('keydown',e=>this.handleKeyboardInput(e)); document.addEventListener('visibilitychange',()=>{ if(this.gameLogic){ if(document.hidden) this.gameLogic.pauseTimer(); else this.gameLogic.resumeTimer(); }}); window.addEventListener('beforeunload',()=>this.saveGameState()); }
 async startNewGame(){ 
   try{ 
     this.showLoading(true); 
     
     // Clear pause state if game was paused
     this.clearPauseState();
     
     const difficulty=document.getElementById('difficulty-select').value; 
     const puzzleData=await this.difficultyManager.getPuzzle(difficulty); 
     this.currentPuzzle=puzzleData; 
     this.gameLogic.startNewGame(puzzleData); 
     this.uiController.displayPuzzle(puzzleData.puzzle); 
     
     // Increment games started counter
     this.storage.incrementGamesStarted(difficulty);
     
     this.updateGameInfo(); 
     this.updateStats(); // Update stats to reflect new game started
     this.hideModal(); 
     this.saveGameState(); 
   } finally{ 
     this.showLoading(false); 
   } 
 }
 solvePuzzle(){ 
   if(!this.currentPuzzle||!this.gameLogic) return; 
   
   // Clear pause state before solving
   this.clearPauseState();
   
   const sol=this.currentPuzzle.solution; 
   this.gameLogic.solvePuzzle(sol); 
   this.uiController.displaySolution(sol); 
   this.storage.setAutoSolvedFlag(true); 
   this.showCompletionModal(true); 
   this.storage.clearGameState(); 
 }
 showHint(){ if(!this.gameLogic||!this.currentPuzzle) return; const hint=this.gameLogic.getHint(); if(hint){ this.uiController.showHint(hint);} }
 resetPuzzle(){ 
   if(!this.currentPuzzle||!this.gameLogic) return; 
   if(confirm('Reset the puzzle?')){ 
     // Clear pause state before reset
     this.clearPauseState();
     
     this.gameLogic.resetPuzzle(); 
     this.uiController.displayPuzzle(this.currentPuzzle.puzzle); 
     this.updateGameInfo(); 
     this.saveGameState(); 
   } 
 }
 async changeDifficulty(newDiff, promptUser=true){ 
   if(!this.isInitialized) return; 
   
   const sel=document.getElementById('difficulty-select'); 
   if(sel) sel.value=newDiff; 
   
   if(promptUser && this.gameLogic&&this.gameLogic.hasProgress()){ 
     if(!confirm('Changing difficulty will start a new game. Continue?')){ 
       if(sel) sel.value=this.gameLogic.getDifficulty(); 
       return; 
     } 
   } 
   
   // Clear pause state before starting new game
   this.clearPauseState();
   
   await this.startNewGame(); 
   this.scrollToGrid(); 
 }
 handleKeyboardInput(e){ if(!this.uiController||!this.gameLogic) return; if(!document.getElementById('game-modal').classList.contains('hidden') || ['INPUT','SELECT'].includes(document.activeElement.tagName)) return; const k=e.key; if(k>='1'&&k<='9'){ e.preventDefault(); this.uiController.handleNumberInput(parseInt(k)); } else if(k==='Delete'||k==='Backspace'){ e.preventDefault(); this.uiController.handleNumberInput(0); } else if(['ArrowUp','ArrowDown','ArrowLeft','ArrowRight'].includes(k)){ e.preventDefault(); this.uiController.handleArrowKey(k); } else if(k==='Escape'){ e.preventDefault(); this.uiController.clearSelection(); } else if(k===' '){ e.preventDefault(); this.showHint(); } else if(e.ctrlKey && (k==='z'||k==='Z')){ e.preventDefault(); this.handleUndo(); } else if(k==='Enter'&&e.ctrlKey){ e.preventDefault(); this.startNewGame(); } }
 handleUndo(){ if(!this.gameLogic||!this.uiController) return; const res=this.gameLogic.undoMove(); if(res){ this.uiController.refreshCell(res.row,res.col,res.value); this.updateGameInfo(); this.saveGameState(); } }
 togglePause(){ if(!this.gameLogic) return; const gameContainer=document.querySelector('.game-container'); const btn=document.getElementById('pause-btn'); if(this.gameLogic.isPaused){ this.gameLogic.resumeTimer(); gameContainer?.classList.remove('is-paused'); if(btn) btn.textContent='Pause'; } else { this.gameLogic.pauseTimer(); gameContainer?.classList.add('is-paused'); if(btn) btn.textContent='Resume'; } }
 
 clearPauseState(){ 
   if(!this.gameLogic) return; 
   const gameContainer=document.querySelector('.game-container'); 
   const btn=document.getElementById('pause-btn'); 
   
   // Force resume if paused
   if(this.gameLogic.isPaused) {
     this.gameLogic.resumeTimer(); 
   }
   
   // Clear visual pause state
   gameContainer?.classList.remove('is-paused'); 
   if(btn) btn.textContent='Pause'; 
 }
 updateGameInfo(){ if(!this.gameLogic) return; const timer=document.getElementById('timer'); const errors=document.getElementById('error-count'); if(timer) timer.textContent=this.gameLogic.getTimeString(); if(errors) errors.textContent=`${this.gameLogic.getErrorCount()}/${this.gameLogic.getMaxErrors()}`; }
 updateStats(){ try{ const s=this.storage.loadStatistics(); const played = s.totalGamesStarted || 0; const won = s.totalGamesCompleted || Object.values(s.byDifficulty||{}).reduce((acc,d)=>acc+(d.completed||0),0); const bestTimes = Object.values(s.byDifficulty||{}).map(d=>d.bestTime).filter(Boolean); const best = bestTimes.length? Math.min(...bestTimes): null; const fmt = (ms)=>{ if(!ms&&ms!==0) return '00:00'; const sec=Math.floor(ms/1000); const m=Math.floor(sec/60), s=sec%60; return `${m.toString().padStart(2,'0')}:${s.toString().padStart(2,'0')}`; };
  const gp=document.getElementById('stat-games-played'); if(gp) gp.textContent=String(played);
  const gw=document.getElementById('stat-games-won'); if(gw) gw.textContent=String(won);
  const bt=document.getElementById('stat-best-time'); if(bt) bt.textContent=fmt(best);
 }catch(_e){}
 }
 saveGameState(){ if(!this.gameLogic||!this.currentPuzzle) return; const state={ puzzle:this.currentPuzzle, gameData:this.gameLogic.getGameState(), timestamp:Date.now() }; this.storage.saveGameState(state); }
 async loadGameState(){ const saved=this.storage.loadGameState(); if(this.storage.getAutoSolvedFlag()){ this.storage.clearGameState(); this.storage.setAutoSolvedFlag(false); return false; } if(saved&&saved.puzzle){ this.currentPuzzle=saved.puzzle; this.gameLogic.loadGameState(saved.gameData,saved.puzzle); this.uiController.displayPuzzle(this.gameLogic.getCurrentGrid()); document.getElementById('difficulty-select').value=saved.puzzle.difficulty; this.updateGameInfo(); return true; } return false; }
 showCompletionModal(auto=false){ const modal=document.getElementById('game-modal'); const title=document.getElementById('modal-title'); const msg=document.getElementById('modal-message'); const finalTime=document.getElementById('final-time'); const finalErrors=document.getElementById('final-errors'); if(auto){ title.textContent='Puzzle Solved!'; msg.textContent='The puzzle has been solved automatically.'; } else { title.textContent='Congratulations!'; msg.textContent='You successfully solved the puzzle!'; } finalTime.textContent=this.gameLogic.getTimeString(); finalErrors.textContent=this.gameLogic.getErrorCount().toString(); modal.classList.remove('hidden'); }
 hideModal(){ document.getElementById('game-modal').classList.add('hidden'); }
 showLoading(show){ const el=document.getElementById('loading'); if(show) el.classList.remove('hidden'); else el.classList.add('hidden'); }
 
 onGameComplete(){ 
   // Clear pause state when game completes
   this.clearPauseState();
   
   // Save the game completion (this automatically updates statistics)
   this.storage.saveGameCompletion({ 
     difficulty:this.currentPuzzle.difficulty, 
     time:this.gameLogic.getElapsedTime(), 
     errors:this.gameLogic.getErrorCount(), 
     timestamp:Date.now() 
   }); 
   
   // Update the stats display
   this.updateStats(); 
   this.showCompletionModal(false); 
   this.storage.clearGameState(); 
 }
 onGameOver(){ 
   // Clear pause state when game is over
   this.clearPauseState();
   
   alert('Game Over! Too many errors. Starting a new game...'); 
   this.startNewGame(); 
 }
 bindDifficultyCards(){ const cards=document.querySelectorAll('.difficulty-card'); if(!cards||!cards.length) return; const setActive=(diff)=>{ cards.forEach(c=>c.classList.toggle('active', c.dataset.diff===diff)); const sel=document.getElementById('difficulty-select'); if(sel) sel.value=diff; };
  cards.forEach(card=>{ card.addEventListener('click', async ()=>{ const diff=card.dataset.diff; setActive(diff); await this.changeDifficulty(diff,false); this.scrollToGrid(); }); });
  // set initial active based on select
  const sel=document.getElementById('difficulty-select'); setActive(sel? sel.value : 'medium'); }
 scrollToGrid(){ const grid=document.getElementById('sudoku-grid'); if(!grid) return; grid.scrollIntoView({ behavior:'smooth', block:'center' }); }
 bindStatsActions(){ const reset=document.getElementById('reset-stats-btn'); if(reset) reset.addEventListener('click',()=>{ if(confirm('Reset all Sudoku statistics?')){ localStorage.removeItem(this.storage.STATISTICS_KEY); this.updateStats(); } }); this.updateStats(); }
}
let sudokuApp; document.addEventListener('DOMContentLoaded',async()=>{ sudokuApp=new SudokuApp(); await sudokuApp.init(); if(sudokuApp.gameLogic){ sudokuApp.gameLogic.onGameComplete=()=>sudokuApp.onGameComplete(); sudokuApp.gameLogic.onGameOver=()=>sudokuApp.onGameOver(); sudokuApp.gameLogic.onGameUpdate=()=>sudokuApp.updateGameInfo(); } });
// Copied from collateral
