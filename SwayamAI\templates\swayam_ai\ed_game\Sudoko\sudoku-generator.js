/**
 * Sudoku Generator - Creates puzzles of varying difficulty
 * Uses "dig holes" strategy with difficulty calibration
 */

class SudokuGenerator {
    constructor() {
        this.solver = new SudokuSolver();
        this.GRID_SIZE = 9;
        this.EMPTY = 0;

        // Difficulty settings based on number of clues and techniques required
        this.DIFFICULTY_SETTINGS = {
            easy: { 
                minClues: 40, 
                maxClues: 45, 
                maxIterations: 100,
                requiredTechniques: ['naked-single', 'hidden-single']
            },
            medium: { 
                minClues: 30, 
                maxClues: 35, 
                maxIterations: 200,
                requiredTechniques: ['naked-single', 'hidden-single', 'naked-pairs']
            },
            hard: { 
                minClues: 25, 
                maxClues: 30, 
                maxIterations: 300,
                requiredTechniques: ['naked-single', 'hidden-single', 'naked-pairs', 'pointing-pairs']
            }
        };
    }

    /**
     * Generate a complete valid Sudoku grid
     * @returns {number[][]} - Complete 9x9 Sudoku grid
     */
    generateCompleteGrid() {
        // Start with empty grid
        const grid = this.createEmptyGrid();

        // Fill diagonal 3x3 boxes first (they don't affect each other)
        this.fillDiagonalBoxes(grid);

        // Fill remaining cells using backtracking
        this.solver.solve(grid);

        return grid;
    }

    /**
     * Generate puzzle of specified difficulty
     * @param {string} difficulty - 'easy', 'medium', or 'hard'
     * @returns {object} - {puzzle: number[][], solution: number[][], difficulty: string, clues: number}
     */
    generatePuzzle(difficulty = 'medium') {
        const settings = this.DIFFICULTY_SETTINGS[difficulty];
        if (!settings) {
            throw new Error(`Invalid difficulty: ${difficulty}`);
        }

        let bestPuzzle = null;
        let attempts = 0;
        const maxAttempts = 50;

        while (attempts < maxAttempts && !bestPuzzle) {
            try {
                // Generate complete solution
                const solution = this.generateCompleteGrid();

                // Create puzzle by removing numbers
                const puzzle = this.createPuzzleFromSolution(solution, settings);

                if (puzzle) {
                    const clueCount = this.countClues(puzzle);
                    if (clueCount >= settings.minClues && clueCount <= settings.maxClues) {
                        bestPuzzle = {
                            puzzle: puzzle,
                            solution: solution,
                            difficulty: difficulty,
                            clues: clueCount,
                            id: this.generatePuzzleId()
                        };
                    }
                }
            } catch (error) {
                console.warn(`Puzzle generation attempt ${attempts + 1} failed:`, error);
            }

            attempts++;
        }

        if (!bestPuzzle) {
            // Fallback: generate simpler puzzle
            console.warn(`Failed to generate ${difficulty} puzzle after ${maxAttempts} attempts, generating fallback`);
            return this.generateFallbackPuzzle(difficulty);
        }

        return bestPuzzle;
    }

    /**
     * Create empty 9x9 grid
     * @returns {number[][]} - Empty grid filled with zeros
     */
    createEmptyGrid() {
        return Array(9).fill(null).map(() => Array(9).fill(0));
    }

    /**
     * Fill the three diagonal 3x3 boxes
     * @param {number[][]} grid - Grid to fill
     */
    fillDiagonalBoxes(grid) {
        for (let box = 0; box < 3; box++) {
            this.fillBox(grid, box * 3, box * 3);
        }
    }

    /**
     * Fill a 3x3 box with random valid numbers
     * @param {number[][]} grid - Grid to fill
     * @param {number} startRow - Starting row of box
     * @param {number} startCol - Starting column of box
     */
    fillBox(grid, startRow, startCol) {
        const numbers = this.shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);
        let index = 0;

        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                grid[startRow + i][startCol + j] = numbers[index++];
            }
        }
    }

    /**
     * Create puzzle by removing numbers from complete solution
     * @param {number[][]} solution - Complete solution grid
     * @param {object} settings - Difficulty settings
     * @returns {number[][]} - Puzzle grid with removed numbers
     */
    createPuzzleFromSolution(solution, settings) {
        const puzzle = this.solver.copyGrid(solution);
        const cells = this.getAllCellPositions();
        this.shuffleArray(cells);

        let removedCount = 0;
        const maxRemove = 81 - settings.minClues;

        for (const [row, col] of cells) {
            if (removedCount >= maxRemove) break;

            // Store original value
            const originalValue = puzzle[row][col];

            // Remove the number
            puzzle[row][col] = this.EMPTY;

            // Check if puzzle still has unique solution
            if (!this.solver.hasUniqueSolution(puzzle)) {
                // Restore the number if removing it creates multiple solutions
                puzzle[row][col] = originalValue;
            } else {
                removedCount++;
            }

            // Early termination if we reach target difficulty
            const currentClues = 81 - removedCount;
            if (currentClues <= settings.maxClues && currentClues >= settings.minClues) {
                // Check if puzzle meets difficulty requirements
                if (this.meetsDifficultyRequirements(puzzle, settings)) {
                    break;
                }
            }
        }

        return puzzle;
    }

    /**
     * Get all cell positions in random order
     * @returns {number[][]} - Array of [row, col] positions
     */
    getAllCellPositions() {
        const positions = [];
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                positions.push([row, col]);
            }
        }
        return positions;
    }

    /**
     * Check if puzzle meets difficulty requirements
     * @param {number[][]} puzzle - Puzzle to check
     * @param {object} settings - Difficulty settings
     * @returns {boolean} - True if meets requirements
     */
    meetsDifficultyRequirements(puzzle, settings) {
        // For now, just check clue count
        // In a more advanced implementation, you would analyze required solving techniques
        const clues = this.countClues(puzzle);
        return clues >= settings.minClues && clues <= settings.maxClues;
    }

    /**
     * Count non-empty cells in grid
     * @param {number[][]} grid - Grid to count
     * @returns {number} - Number of filled cells
     */
    countClues(grid) {
        let count = 0;
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (grid[row][col] !== this.EMPTY) {
                    count++;
                }
            }
        }
        return count;
    }

    /**
     * Generate fallback puzzle if normal generation fails
     * @param {string} difficulty - Target difficulty
     * @returns {object} - Fallback puzzle
     */
    generateFallbackPuzzle(difficulty) {
        // Generate a simpler puzzle with more clues as fallback
        const solution = this.generateCompleteGrid();
        const puzzle = this.solver.copyGrid(solution);

        const settings = this.DIFFICULTY_SETTINGS[difficulty];
        const targetClues = settings.maxClues; // Use max clues for easier generation
        const cellsToRemove = 81 - targetClues;

        const positions = this.getAllCellPositions();
        this.shuffleArray(positions);

        // Remove cells without checking uniqueness (faster but less optimal)
        for (let i = 0; i < Math.min(cellsToRemove, positions.length); i++) {
            const [row, col] = positions[i];
            puzzle[row][col] = this.EMPTY;
        }

        return {
            puzzle: puzzle,
            solution: solution,
            difficulty: difficulty,
            clues: targetClues,
            id: this.generatePuzzleId(),
            fallback: true
        };
    }

    /**
     * Shuffle array in place (Fisher-Yates algorithm)
     * @param {any[]} array - Array to shuffle
     * @returns {any[]} - Shuffled array
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    /**
     * Generate unique puzzle ID
     * @returns {string} - Unique identifier
     */
    generatePuzzleId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Generate multiple puzzles for database
     * @param {string} difficulty - Target difficulty
     * @param {number} count - Number of puzzles to generate
     * @returns {object[]} - Array of puzzle objects
     */
    generatePuzzleSet(difficulty, count) {
        const puzzles = [];
        const startTime = Date.now();

        console.log(`Generating ${count} ${difficulty} puzzles...`);

        for (let i = 0; i < count; i++) {
            try {
                const puzzle = this.generatePuzzle(difficulty);
                puzzles.push(puzzle);

                if ((i + 1) % 10 === 0) {
                    const elapsed = (Date.now() - startTime) / 1000;
                    const rate = (i + 1) / elapsed;
                    console.log(`Generated ${i + 1}/${count} puzzles (${rate.toFixed(1)} puzzles/sec)`);
                }
            } catch (error) {
                console.error(`Failed to generate puzzle ${i + 1}:`, error);
            }
        }

        console.log(`Completed generating ${puzzles.length}/${count} ${difficulty} puzzles`);
        return puzzles;
    }

    /**
     * Validate generated puzzle
     * @param {object} puzzleObj - Puzzle object to validate
     * @returns {boolean} - True if valid
     */
    validatePuzzle(puzzleObj) {
        const { puzzle, solution } = puzzleObj;

        // Check if solution is valid
        if (!this.solver.isValidSolution(solution)) {
            return false;
        }

        // Check if puzzle can be solved to get the solution
        const testGrid = this.solver.copyGrid(puzzle);
        if (!this.solver.solve(testGrid)) {
            return false;
        }

        // Check if solved puzzle matches provided solution
        return this.gridsEqual(testGrid, solution);
    }

    /**
     * Check if two grids are equal
     * @param {number[][]} grid1 - First grid
     * @param {number[][]} grid2 - Second grid
     * @returns {boolean} - True if equal
     */
    gridsEqual(grid1, grid2) {
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (grid1[row][col] !== grid2[row][col]) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Convert puzzle to compact string format for storage
     * @param {object} puzzleObj - Puzzle object
     * @returns {string} - Compact string representation
     */
    puzzleToString(puzzleObj) {
        const puzzleStr = this.solver.gridToString(puzzleObj.puzzle);
        const solutionStr = this.solver.gridToString(puzzleObj.solution);

        return JSON.stringify({
            puzzle: puzzleStr,
            solution: solutionStr,
            difficulty: puzzleObj.difficulty,
            clues: puzzleObj.clues,
            id: puzzleObj.id
        });
    }

    /**
     * Parse puzzle from string format
     * @param {string} puzzleStr - String representation
     * @returns {object} - Puzzle object
     */
    stringToPuzzle(puzzleStr) {
        const data = JSON.parse(puzzleStr);

        return {
            puzzle: this.solver.stringToGrid(data.puzzle),
            solution: this.solver.stringToGrid(data.solution),
            difficulty: data.difficulty,
            clues: data.clues,
            id: data.id
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SudokuGenerator;
} else if (typeof window !== 'undefined') {
    window.SudokuGenerator = SudokuGenerator;
}