{% extends "base.html" %}

{% block title %}SwayamAI · Upload{% endblock %}

{% block content %}
<section class="sa-hero p-4 p-md-5 mb-4">
  <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center justify-content-between gap-3">
    <div>
  <div class="sa-pill mb-2"><i class="bi bi-activity"></i> SwayamML</div>
  <h1 class="h3 mb-2">Curate Your Model</h1>
  <p class="text-muted mb-0">Choose your use case, share your data, and unwind while we tailor the model and finalize deliverables!</p>
    </div>
    <div class="d-flex align-items-center gap-2">
      <span class="sa-badge">File Format Accepted: CSV/XLSX</span>
      <span class="sa-badge">Max Size Allowed: 50 MB</span>
    </div>
  </div>
</section>

<section class="sa-grid">
  {% for val, label in use_cases %}
    <div class="sa-card p-4">
      <div class="d-flex align-items-start justify-content-between mb-2">
        <div class="d-flex align-items-center gap-2">
          <div class="sa-icon-3d">
            {% if val in ['classification','non_nlp_classification'] %}
            <!-- 3D-ish classifier icon -->
            <svg viewBox="0 0 24 24" aria-hidden="true">
              <path class="g" d="M4 8h8M4 12h8M4 16h8"/>
              <circle class="f" cx="18" cy="8" r="2.5"/>
              <circle class="f" cx="18" cy="16" r="2.5"/>
            </svg>
            {% elif val in ['clustering','non_nlp_clustering'] %}
            <!-- 3D-ish clusters icon -->
            <svg viewBox="0 0 24 24" aria-hidden="true">
              <circle class="f" cx="7" cy="7" r="3"/>
              <circle class="f" cx="17" cy="7" r="3"/>
              <circle class="f" cx="12" cy="16" r="3"/>
            </svg>
            {% elif val == 'regression' %}
            <!-- 3D-ish trend line icon -->
            <svg viewBox="0 0 24 24" aria-hidden="true">
              <path class="g" d="M3 17l6-6 4 3 7-7"/>
            </svg>
            {% elif val == 'anomaly_numeric' %}
            <!-- 3D-ish shield alert icon -->
            <svg viewBox="0 0 24 24" aria-hidden="true">
              <path class="g" d="M12 3l7 3v5c0 4.5-3 8.5-7 10-4-1.5-7-5.5-7-10V6l7-3z"/>
              <path class="g" d="M12 8v4"/>
              <circle class="f" cx="12" cy="15.5" r="1.2"/>
            </svg>
            {% elif val == 'sentiment' %}
            <!-- 3D-ish smile icon -->
            <svg viewBox="0 0 24 24" aria-hidden="true">
              <path class="g" d="M12 4a8 8 0 1 1 0 16 8 8 0 0 1 0-16z"/>
              <circle class="f" cx="9" cy="11" r="1.2"/>
              <circle class="f" cx="15" cy="11" r="1.2"/>
              <path class="g" d="M8.5 14c1 .9 2.2 1.4 3.5 1.4s2.5-.5 3.5-1.4"/>
            </svg>
              {% elif val == 'text_similarity' %}
            <!-- 3D-ish link icon -->
            <svg viewBox="0 0 24 24" aria-hidden="true">
              <path class="g" d="M10 14a5 5 0 0 1 0-7l1.5-1.5a5 5 0 0 1 7 7L17 14"/>
              <path class="g" d="M14 10a5 5 0 0 1 0 7L12.5 19a5 5 0 1 1-7-7L7 10"/>
            </svg>
              {% elif val == 'affinity' %}
              <!-- 3D-ish basket icon -->
              <svg viewBox="0 0 24 24" aria-hidden="true">
                <path class="g" d="M3 7h18M5 7l2 12h10l2-12"/>
                <circle class="f" cx="9" cy="20" r="1.5"/>
                <circle class="f" cx="15" cy="20" r="1.5"/>
              </svg>
            {% else %}
            <svg viewBox="0 0 24 24" aria-hidden="true"><path class="g" d="M4 6h16M4 12h16M4 18h16"/></svg>
            {% endif %}
          </div>
          <div>
            <div class="fw-semibold">{{ label }}</div>
            <div class="small text-muted">
              {% if val == 'classification' %}
                Predict categories (e.g., churn yes/no, fraud vs. not fraud, spam vs. ham).
              {% elif val == 'non_nlp_classification' %}
                Classify using numerical/categorical features; output label/nominal number.
              {% elif val == 'regression' %}
                Predict continuous values (e.g., price, demand, score, time).
              {% elif val == 'clustering' or val == 'non_nlp_clustering' %}
                Group similar records into segments without labels.
              {% elif val == 'anomaly_numeric' %}
                Detect anomalies/outliers in numeric data using multiple detectors.
              {% elif val == 'sentiment' %}
                Analyze text sentiment with fast/accurate/financial/multilingual models.
              {% elif val == 'text_similarity' %}
                Compare one input text against a selected text column using cosine similarity.
              {% elif val == 'affinity' %}
                Discover affinities via Apriori/FP‑Growth; export Antecedent → Consequent with support, confidence, lift.
              {% else %}
                Select the {{ label }} pipeline.
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      <form method="post" action="/upload" enctype="multipart/form-data" class="d-flex align-items-center gap-2 uc-form">
        <input type="hidden" name="use_case" value="{{ val }}" />
        <input type="file" class="form-control form-control-sm uc-file" name="file" accept=".csv, .xls, .xlsx" data-maxsize="52428800" />
        <button type="submit" class="btn btn-sm sa-btn-primary uc-next" disabled title="Select a file to continue" aria-label="Next">
          <i class="bi bi-arrow-right-circle"></i>
        </button>
      </form>
    </div>
  {% endfor %}
</section>

{% endblock %}

{% block scripts %}
<script>
  // Per-row: enable Next when a file is selected; add a tiny spinner on submit
  document.querySelectorAll('.uc-form').forEach(function(frm){
    const fileInput = frm.querySelector('.uc-file');
    const nextBtn = frm.querySelector('.uc-next');
    if(fileInput && nextBtn){
      const update = () => {
        const has = fileInput.files && fileInput.files.length > 0;
        nextBtn.disabled = !has;
      };
      update();
      fileInput.addEventListener('change', update);
      frm.addEventListener('submit', function(){
        nextBtn.disabled = true;
        nextBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
      });
    }
  });
</script>
{% endblock %}
