import os
import sys
import subprocess
import pandas as pd
import numpy as np

from non_nlp_classification import non_nlp_classification_process


def make_classification_df(n=120, seed=0):
    rng = np.random.default_rng(seed)
    num1 = rng.normal(0, 1, size=n)
    num2 = rng.normal(1, 2, size=n)
    cat = rng.choice(['A','B','C'], size=n, p=[0.5, 0.3, 0.2])
    # target based on a simple rule
    y = (num1 + num2 + (cat == 'C')*0.5 > 1.0).astype(int).astype(str)
    return pd.DataFrame({'num1': num1, 'num2': num2, 'cat': cat, 'target': y})


def test_non_nlp_classification_end_to_end(tmp_path, tmp_output_dir):
    df = make_classification_df()
    inp = tmp_path / 'train.csv'
    df.to_csv(inp, index=False)

    summary, files, n_models = non_nlp_classification_process(
        str(inp), fields=['num1','num2','cat'], label='target', output_folder=str(tmp_output_dir)
    )

    assert hasattr(summary, 'columns') and not summary.empty
    # key files
    expect = ['classification_results.xlsx', 'classification_model.pkl', 'sample_prediction_classification.csv', 'deployment_script_non_nlp_classification.py']
    for name in expect:
        assert name in files
        assert (tmp_output_dir / name).exists()

    # run deployment script
    script = tmp_output_dir / 'deployment_script_non_nlp_classification.py'
    test_csv = tmp_path / 'new.csv'
    # Use same feature columns
    df.drop(columns=['target']).head(8).to_csv(test_csv, index=False)
    out_csv = tmp_path / 'preds.csv'
    proc = subprocess.run([sys.executable, str(script), '--input', str(test_csv), '--output', str(out_csv)], capture_output=True, text=True)
    assert proc.returncode == 0, proc.stderr
    out = pd.read_csv(out_csv)
    assert 'predicted_label' in out.columns
