import os
import os.path as op
import pandas as pd
import numpy as np
from typing import List, <PERSON><PERSON>, Dict, Any
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import silhouette_score, calinski_harabasz_score
import joblib
import re
import json

# PyOD models
from pyod.models.iforest import IForest
from pyod.models.knn import KNN
from pyod.models.lof import LOF
from pyod.models.copod import COPOD
from pyod.models.ocsvm import OCSVM
from pyod.models.rod import ROD


def _load_dataframe(file_path: str) -> pd.DataFrame:
    _, ext = op.splitext(file_path.lower())
    if ext == '.csv':
        return pd.read_csv(file_path)
    elif ext in ('.xlsx', '.xls'):
        return pd.read_excel(file_path)
    else:
        raise ValueError('Unsupported file format. Please upload CSV or Excel.')


def _prepare_numeric_matrix(df: pd.DataFrame, fields: List[str] | None) -> <PERSON><PERSON>[pd.DataFrame, np.n<PERSON>ray, SimpleImputer, RobustScaler, List[str]]:
    # limit to selected fields if provided
    if fields:
        fields = [c for c in fields if c in df.columns]
        df = df[fields]
    # keep only numeric
    num_df = df.select_dtypes(include=[np.number]).copy()
    if num_df.shape[1] == 0:
        raise ValueError('No numeric features available for anomaly detection. Please select numeric columns.')
    cols = list(num_df.columns)
    # impute + scale
    imputer = SimpleImputer(strategy='median')
    X_imp = imputer.fit_transform(num_df.values)
    scaler = RobustScaler()
    X = scaler.fit_transform(X_imp)
    return num_df, X, imputer, scaler, cols


def _fit_models(X: np.ndarray, contamination: float = 0.1) -> Dict[str, Any]:
    models = {
        'isolation_forest': IForest(contamination=contamination, random_state=42),
        'knn': KNN(contamination=contamination),
        'lof': LOF(contamination=contamination),
        'copod': COPOD(),  # parameter-free
        'rod': ROD(),      # parameter-free
        'oneclass_svm': OCSVM(contamination=contamination),
    }
    results = {}
    for name, clf in models.items():
        clf.fit(X)
        # Ensure labels and scores are computed
        labels = clf.labels_ if hasattr(clf, 'labels_') else clf.predict(X)
        scores = clf.decision_scores_ if hasattr(clf, 'decision_scores_') else clf.decision_function(X)
        # Compute metrics (binary cluster silhouette/CH)
        if len(np.unique(labels)) >= 2:
            try:
                sil = silhouette_score(X, labels, metric='euclidean')
            except Exception:
                sil = -1.0
            try:
                ch = calinski_harabasz_score(X, labels)
            except Exception:
                ch = -1.0
        else:
            sil, ch = -1.0, -1.0
        results[name] = {
            'estimator': clf,
            'labels': labels.astype(int),
            'scores': np.asarray(scores).astype(float),
            'silhouette': float(sil),
            'calinski_harabasz': float(ch),
        }
    return results


def _pick_best(results: Dict[str, Dict[str, Any]]) -> str:
    # primary: silhouette (higher is better), tiebreaker: calinski-harabasz
    best_name = None
    best_tuple = None
    for name, r in results.items():
        t = (r['silhouette'], r['calinski_harabasz'])
        if best_tuple is None or t > best_tuple:
            best_tuple = t
            best_name = name
    return best_name or list(results.keys())[0]


def _safe_name(name: str) -> str:
    """Make a filesystem-safe name from a column label."""
    s = re.sub(r"[^A-Za-z0-9_\-]+", "_", str(name).strip())
    # avoid leading/trailing underscores proliferation
    s = re.sub(r"_+", "_", s).strip("_")
    return s or "col"


def anomaly_detection_numeric_process(
    file_path: str,
    fields: List[str],
    output_folder: str,
    mode: str = 'all',
    index_column: str | None = None,
):
    """
    Run anomaly detection on numeric data using multiple detectors and select the best
    by silhouette score (then Calinski–Harabasz as tie-breaker).

    Params
    - file_path: path to uploaded CSV/Excel
    - fields: selected fields (can be empty to use all columns)
    - output_folder: where to write artifacts
    - mode: 'all' to use all numeric features; 'per_column' to run each numeric feature individually
    - index_column: optional column name from the input to include as an identifier in outputs

    Returns tuple: (summary_df, [list of output filenames], num_models)
    """
    os.makedirs(output_folder, exist_ok=True)

    df = _load_dataframe(file_path)

    outputs: List[str] = []

    if mode == 'per_column':
        # Run each numeric column independently (univariate)
        num_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        if fields:
            num_cols = [c for c in num_cols if c in fields]
        if not num_cols:
            raise ValueError('No numeric columns available for per-column anomaly detection.')

        summary_rows = []
        # base frame with chosen identifier (index_column) or the DataFrame index
        base_out = pd.DataFrame(index=df.index)
        if index_column and index_column in df.columns:
            base_out[index_column] = df[index_column].values
        else:
            base_out['index'] = df.index
        result_frames = [base_out]
        for col in num_cols:
            sub_df = df[[col]].copy()
            # prepare
            imputer = SimpleImputer(strategy='median')
            X_imp = imputer.fit_transform(sub_df.values)
            scaler = RobustScaler()
            X = scaler.fit_transform(X_imp)
            # fit
            results = _fit_models(X)
            best = _pick_best(results)
            r = results[best]
            # include original value of this column along with flags and scores
            pred = pd.DataFrame({
                col: df[col].values,
                f'{col}_is_anomaly': r['labels'],
                f'{col}_score': r['scores']
            }, index=df.index)
            result_frames.append(pred)

            # persist artifacts per column so user can deploy per-column model
            safe = _safe_name(col)
            imp_path = op.join(output_folder, f'imputer_{safe}.pkl')
            sc_path = op.join(output_folder, f'scaler_{safe}.pkl')
            mdl_path = op.join(output_folder, f'anomaly_model_{safe}.pkl')
            try:
                joblib.dump(imputer, imp_path)
                joblib.dump(scaler, sc_path)
                joblib.dump(r['estimator'], mdl_path)
                outputs.extend([op.basename(imp_path), op.basename(sc_path), op.basename(mdl_path)])
            except Exception:
                # ignore persistence errors per column; continue
                pass

            summary_rows.append({
                'column': col,
                'best_model': best,
                'silhouette': r['silhouette'],
                'calinski_harabasz': r['calinski_harabasz']
            })
        # aggregate
        summary_df = pd.DataFrame(summary_rows)
        out_summary = op.join(output_folder, 'anomaly_per_column_results.xlsx')
        with pd.ExcelWriter(out_summary, engine='openpyxl') as w:
            summary_df.to_excel(w, sheet_name='summary', index=False)
        outputs.append(op.basename(out_summary))

        combined = pd.concat(result_frames, axis=1)
        combined_out = op.join(output_folder, 'anomalies_per_column.csv')
        # Write without DataFrame index; identifier column already included
        if 'index' in combined.columns:
            combined = combined.reset_index(drop=True)
        combined.to_csv(combined_out, index=False)
        outputs.append(op.basename(combined_out))

        # Write per-column deployment script (positional args supported)
        try:
            dep_path = op.join(output_folder, 'deployment_script_anomaly_numeric_per_column.py')
            with open(dep_path, 'w', encoding='utf-8') as f:
                f.write(DEPLOYMENT_SCRIPT_PER_COLUMN)
            outputs.append('deployment_script_anomaly_numeric_per_column.py')
        except Exception:
            pass

        num_models = len(num_cols) * 6
        return summary_df, outputs, num_models

    # Default: mode == 'all' => multivariate detection
    num_df, X, imputer, scaler, cols = _prepare_numeric_matrix(df, fields)
    results = _fit_models(X)
    best = _pick_best(results)
    r = results[best]

    # Save predictions merged with original data
    out_df = num_df.copy()
    # include selected index/identifier column if provided
    if index_column and index_column in df.columns:
        # assign then move as first column
        out_df[index_column] = df[index_column].values
        ordered_cols = [index_column] + [c for c in out_df.columns if c != index_column]
        out_df = out_df[ordered_cols]
    out_df['is_anomaly'] = r['labels']
    out_df['anomaly_score'] = r['scores']
    out_file = op.join(output_folder, 'anomalies.csv')
    out_df.to_csv(out_file, index=False)
    outputs.append(op.basename(out_file))

    # Save summary
    summary_df = pd.DataFrame([
        {'model': name, 'silhouette': res['silhouette'], 'calinski_harabasz': res['calinski_harabasz']}
        for name, res in results.items()
    ]).sort_values(['silhouette', 'calinski_harabasz'], ascending=[False, False])
    out_summary = op.join(output_folder, 'anomaly_results.xlsx')
    with pd.ExcelWriter(out_summary, engine='openpyxl') as w:
        summary_df.to_excel(w, sheet_name='results', index=False)
    outputs.append(op.basename(out_summary))

    # Persist artifacts for deployment
    joblib.dump(imputer, op.join(output_folder, 'imputer.pkl'))
    joblib.dump(scaler, op.join(output_folder, 'scaler.pkl'))
    joblib.dump(r['estimator'], op.join(output_folder, 'anomaly_model.pkl'))
    outputs.extend(['imputer.pkl', 'scaler.pkl', 'anomaly_model.pkl'])

    # Persist feature column names used during training to aid deployment alignment
    try:
        with open(op.join(output_folder, 'feature_columns.json'), 'w', encoding='utf-8') as f:
            json.dump(cols, f, ensure_ascii=False, indent=2)
        with open(op.join(output_folder, 'feature_columns.txt'), 'w', encoding='utf-8') as f:
            f.write("\n".join(cols))
        outputs.extend(['feature_columns.json', 'feature_columns.txt'])
    except Exception:
        pass

    # Write a simple deployment script next to artifacts
    try:
        dep_path = op.join(output_folder, 'deployment_script_anomaly_numeric.py')
        with open(dep_path, 'w', encoding='utf-8') as f:
            f.write(DEPLOYMENT_SCRIPT_CONTENT)
        outputs.append('deployment_script_anomaly_numeric.py')
    except Exception:
        pass

    num_models = len(results)
    return summary_df, outputs, num_models


DEPLOYMENT_SCRIPT_CONTENT = r"""
import argparse
import os
import os.path as op
import sys
import json
import pandas as pd
import numpy as np
import joblib


def _load(file_path: str) -> pd.DataFrame:
    _, ext = op.splitext(file_path.lower())
    if ext == '.csv':
        return pd.read_csv(file_path)
    else:
        return pd.read_excel(file_path)


def _get_expected_n_features(imputer) -> int:
    n = getattr(imputer, 'n_features_in_', None)
    if n is None:
        stats = getattr(imputer, 'statistics_', None)
        if stats is not None:
            n = int(stats.shape[0])
    if not n:
        raise SystemExit('Unable to infer expected feature count from imputer.')
    return int(n)


def _load_training_columns(artifacts_dir: str, expected_n: int | None = None):
    json_path = op.join(artifacts_dir, 'feature_columns.json')
    txt_path = op.join(artifacts_dir, 'feature_columns.txt')
    if op.exists(json_path):
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                cols = json.load(f)
            if isinstance(cols, list) and all(isinstance(c, str) for c in cols):
                return cols
        except Exception:
            pass
    if op.exists(txt_path):
        try:
            with open(txt_path, 'r', encoding='utf-8') as f:
                cols = [line.strip() for line in f if line.strip()]
            if cols:
                return cols
        except Exception:
            pass
    csv_path = op.join(artifacts_dir, 'anomalies.csv')
    if op.exists(csv_path):
        try:
            sample_df = pd.read_csv(csv_path, nrows=100)
            cols = list(sample_df.columns)
            if 'is_anomaly' in cols:
                idx = cols.index('is_anomaly')
                pre = cols[:idx]
                num_cols = [c for c in pre if pd.api.types.is_numeric_dtype(sample_df[c])]
                if expected_n and len(num_cols) != expected_n:
                    if len(pre) > len(num_cols) and len(num_cols) > expected_n:
                        num_cols = num_cols[-expected_n:]
                    elif len(num_cols) > expected_n:
                        num_cols = num_cols[:expected_n]
                    elif len(num_cols) < expected_n:
                        pass
                if num_cols:
                    return num_cols
        except Exception:
            pass
    return None


def _align_features(df: pd.DataFrame, feature_names):
    data = {}
    for col in feature_names:
        if col in df.columns:
            data[col] = pd.to_numeric(df[col], errors='coerce')
        else:
            data[col] = np.nan
    return pd.DataFrame(data, index=df.index)


def main(argv=None):
    p = argparse.ArgumentParser(description='Run anomaly detection (numeric) using saved artifacts')
    # Support both flags and positional args
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/Excel with numeric columns')
    p.add_argument('positional_output', nargs='?', help='Path to write predictions CSV/XLSX')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/Excel with numeric columns')
    p.add_argument('--output', dest='flag_output', help='Path to write predictions CSV/XLSX')
    p.add_argument('--allow-mismatch', action='store_true',
                   help='If feature count differs from training, select first N numeric columns or pad with NaN to match expected feature count.')
    p.add_argument('--features', default=None,
                   help='Comma-separated list of feature column names in the exact order used for training. Overrides auto-detection.')
    args = p.parse_args(argv)

    inp = args.flag_input or args.positional_input
    if not inp:
        p.error('An input file is required. Provide --input or a positional input path.')
    outp = args.flag_output or args.positional_output
    if not outp:
        base, ext = op.splitext(inp)
        outp = f"{base}_anomalies.csv" if ext.lower() == '.csv' else f"{base}_anomalies.xlsx"

    df = _load(inp)

    here = op.dirname(op.abspath(__file__))
    imputer = joblib.load(op.join(here, 'imputer.pkl'))
    scaler = joblib.load(op.join(here, 'scaler.pkl'))
    model = joblib.load(op.join(here, 'anomaly_model.pkl'))

    expected_n = _get_expected_n_features(imputer)
    training_cols = None
    if args.features:
        training_cols = [c.strip() for c in args.features.split(',') if c.strip()]
    if not training_cols:
        training_cols = _load_training_columns(here, expected_n)

    if training_cols:
        feat_df = _align_features(df, training_cols)
        if feat_df.shape[1] != expected_n:
            if feat_df.shape[1] > expected_n:
                feat_df = feat_df.iloc[:, :expected_n]
            else:
                for i in range(expected_n - feat_df.shape[1]):
                    feat_df[f'_missing_{i}'] = np.nan
    else:
        num_df = df.select_dtypes(include=[np.number]).copy()
        if num_df.shape[1] == 0:
            raise SystemExit('No numeric columns found in input file.')
        if num_df.shape[1] != expected_n:
            if args.allow_mismatch:
                if num_df.shape[1] > expected_n:
                    print(f"[warn] Input has {num_df.shape[1]} numeric columns; expected {expected_n}. Using the first {expected_n} columns.", file=sys.stderr)
                    feat_df = num_df.iloc[:, :expected_n]
                else:
                    print(f"[warn] Input has {num_df.shape[1]} numeric columns; expected {expected_n}. Padding with NaNs to reach {expected_n}.", file=sys.stderr)
                    feat_df = num_df.copy()
                    for i in range(expected_n - num_df.shape[1]):
                        feat_df[f'_missing_{i}'] = np.nan
            else:
                raise SystemExit(
                    f"Feature mismatch: input has {num_df.shape[1]} numeric columns but the trained imputer expects {expected_n}. "
                    f"Provide --features with the training column names, place feature_columns.json next to artifacts, or rerun with --allow-mismatch."
                )
        else:
            feat_df = num_df

    X = scaler.transform(imputer.transform(feat_df.values))
    labels = model.predict(X)
    scores = None
    try:
        scores = model.decision_function(X)
    except Exception:
        try:
            scores = model.score_samples(X)
        except Exception:
            scores = None
    if scores is None:
        scores = np.zeros(len(labels))

    # Include all original columns in the output, then append anomaly flags/scores
    out = df.copy()
    out['is_anomaly'] = labels
    out['anomaly_score'] = scores
    if outp.lower().endswith('.xlsx'):
        with pd.ExcelWriter(outp, engine='openpyxl') as w:
            out.to_excel(w, index=False)
    else:
        out.to_csv(outp, index=False)

    print('Wrote', outp)

if __name__ == '__main__':
    sys.exit(main())
"""


DEPLOYMENT_SCRIPT_PER_COLUMN = r"""
import argparse
import os
import os.path as op
import re
import sys
import pandas as pd
import numpy as np
import joblib


def _safe_name(name: str) -> str:
    s = re.sub(r"[^A-Za-z0-9_\-]+", "_", str(name).strip())
    s = re.sub(r"_+", "_", s).strip("_")
    return s or "col"


def _load(file_path: str) -> pd.DataFrame:
    _, ext = op.splitext(file_path.lower())
    if ext == '.csv':
        return pd.read_csv(file_path)
    else:
        return pd.read_excel(file_path)


def _resolve_input_path(path: str, script_dir: str) -> str:
    # Return an absolute path. Try as-is, then relative to CWD, then relative to the script directory.
    # as provided (absolute or relative to CWD)
    cand = op.abspath(path)
    if op.exists(cand):
        return cand
    # relative to script directory
    cand2 = op.join(script_dir, path)
    if op.exists(cand2):
        return cand2
    raise SystemExit(
        f"Input file not found: '{path}'. Tried: '{cand}' and '{cand2}'. "
        "Provide a full path or place the file alongside this script."
    )


def main(argv=None):
    p = argparse.ArgumentParser(description='Apply saved per-column anomaly model to new data')
    # Support both flags and positional args: input, column, [output]
    p.add_argument('positional_input', nargs='?', help='Path to input CSV/Excel with the column present')
    p.add_argument('positional_column', nargs='?', help='Column name in the input to score')
    p.add_argument('positional_output', nargs='?', help='Path to write predictions CSV/XLSX')
    p.add_argument('--input', dest='flag_input', help='Path to input CSV/Excel with the column present')
    p.add_argument('--output', dest='flag_output', help='Path to write predictions CSV/XLSX')
    p.add_argument('--column', dest='flag_column', help='Column name in the input to score')
    p.add_argument('--id-column', default=None, help='Optional identifier column (kept for compatibility; output includes all columns)')
    p.add_argument('--artifacts-dir', default=None, help='Directory containing imputer_*.pkl, scaler_*.pkl, anomaly_model_*.pkl; defaults to this script\'s directory')
    p.add_argument('--artifact-name', default=None, help='Override safe artifact name if different from the column name')
    args = p.parse_args(argv)

    inp = args.flag_input or args.positional_input
    col = args.flag_column or args.positional_column
    if not inp:
        p.error('An input file is required. Provide --input or a positional input path.')
    if not col:
        p.error('A column name is required. Provide --column or a positional column argument.')

    outp = args.flag_output or args.positional_output
    base, ext = op.splitext(inp)
    safe_col = _safe_name(col)
    if not outp:
        outp = f"{base}_{safe_col}_anomalies.csv" if ext.lower() == '.csv' else f"{base}_{safe_col}_anomalies.xlsx"

    here = args.artifacts_dir or op.dirname(op.abspath(__file__))
    inp_resolved = _resolve_input_path(inp, here)
    df = _load(inp_resolved)
    if col not in df.columns:
        raise SystemExit(f"Column '{col}' not found in input.")

    safe = args.artifact_name or _safe_name(col)
    imp_path = op.join(here, f'imputer_{safe}.pkl')
    sc_path = op.join(here, f'scaler_{safe}.pkl')
    mdl_path = op.join(here, f'anomaly_model_{safe}.pkl')
    if not (op.exists(imp_path) and op.exists(sc_path) and op.exists(mdl_path)):
        raise SystemExit('Required artifacts not found for column: ' + col)

    imputer = joblib.load(imp_path)
    scaler = joblib.load(sc_path)
    model = joblib.load(mdl_path)

    # build 2D matrix for the single column
    col_df = pd.DataFrame({col: pd.to_numeric(df[col], errors='coerce')})
    X = scaler.transform(imputer.transform(col_df.values))
    labels = model.predict(X)
    scores = getattr(model, 'decision_scores_', None)
    if scores is None:
        try:
            scores = model.decision_function(X)
        except Exception:
            scores = None
    if scores is None:
        scores = np.zeros(len(labels))

    # Include all original columns, then append this column's anomaly flags and scores
    out = df.copy()
    out[f'{col}_is_anomaly'] = labels
    out[f'{col}_score'] = scores
    if outp.lower().endswith('.xlsx'):
        with pd.ExcelWriter(outp, engine='openpyxl') as w:
            out.to_excel(w, index=False)
    else:
        out.to_csv(outp, index=False)

    print('Wrote', outp)

if __name__ == '__main__':
    sys.exit(main())
"""
