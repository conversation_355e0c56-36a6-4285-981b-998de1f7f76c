document.addEventListener('DOMContentLoaded', function(){
  // ui.js - small helpers: dark mode toggle + file input validation + select enhancement
  function $(s){return document.querySelector(s)}
  function $all(s){return Array.from(document.querySelectorAll(s))}

  // Dark mode
  var toggle = $('#darkModeToggle');
  var html = document.documentElement;
  var icon = $('#darkIcon');
  var LSKEY = 'swayamai:dark';

  function setDark(dark){
    if(dark){
      html.classList.add('dark');
      if(icon) icon.className = 'bi bi-sun-fill';
      try{ localStorage.setItem(LSKEY, '1'); }catch(e){}
    } else {
      html.classList.remove('dark');
      if(icon) icon.className = 'bi bi-moon-stars-fill';
      try{ localStorage.removeItem(LSKEY); }catch(e){}
    }
  }

  try{
    var pref = localStorage.getItem(LSKEY);
    if(pref === '1') setDark(true);
  }catch(e){/* ignore */}

  if(toggle){
    toggle.addEventListener('click', function(e){
      var isDark = html.classList.contains('dark');
      setDark(!isDark);
    });
  }

  // Initialize Choices.js for the multi-select fields element if present
  try{
    if(window.Choices){
      var fields = $('#fields');
      if(fields){
        // Use taggable multi-select so users can quickly add/remove tags; keep choices synced
        var choicesInstance = new Choices(fields, {
          removeItemButton: true,
          shouldSort: false,
          searchEnabled: true,
          placeholderValue: 'Select fields...',
          duplicateItemsAllowed: false,
          editItems: false,
          addItems: true // allow arbitrary/taggable items
        });

        // Expose instance and helpers for other scripts (e.g., configure.html) to toggle enable/disable
        try {
          window.swayamFieldsChoices = choicesInstance;
          window.swayamEnableFields = function(){
            try { choicesInstance.enable(); } catch(e){}
            try { fields.disabled = false; fields.removeAttribute('disabled'); fields.setAttribute('aria-disabled','false'); } catch(e){}
            try {
              var wrap = fields.closest('.choices') || (fields.parentElement && fields.parentElement.querySelector('.choices'));
              if(wrap){ wrap.classList.remove('is-disabled'); wrap.setAttribute('aria-disabled','false'); }
            } catch(e){}
          };
          window.swayamDisableFields = function(){
            try { choicesInstance.disable(); } catch(e){}
            try { fields.disabled = true; fields.setAttribute('disabled','disabled'); fields.setAttribute('aria-disabled','true'); } catch(e){}
            try {
              var wrap = fields.closest('.choices') || (fields.parentElement && fields.parentElement.querySelector('.choices'));
              if(wrap){ wrap.classList.add('is-disabled'); wrap.setAttribute('aria-disabled','true'); }
            } catch(e){}
          };
        } catch(e) { /* ignore */ }

        // wire select-all / clear-all buttons (works with underlying <select>)
        var selectAllBtn = document.getElementById('selectAllFields');
        var clearAllBtn = document.getElementById('clearAllFields');
        function selectAllFields(){
          try{
            var opts = Array.from(fields.options).map(function(o){ return o.value; });
            // select each native option and inform Choices by setting choice by value
            opts.forEach(function(v){
              // mark native option selected
              var opt = Array.from(fields.options).find(function(o){ return o.value==v; });
              if(opt) opt.selected = true;
              try{ choicesInstance.setChoiceByValue(v); }catch(e){}
            });
          }catch(e){console.warn(e)}
        }
        function clearAllFields(){
          try{
            // Deselect native options
            Array.from(fields.options).forEach(function(o){ o.selected = false; });
            // Use Choices API where available to remove selected items without rebuilding
            if(typeof choicesInstance.removeActiveItems === 'function'){
              choicesInstance.removeActiveItems();
            } else if(typeof choicesInstance.clearStore === 'function'){
              choicesInstance.clearStore();
            }
          }catch(e){
            // As a last resort, rebuild the instance
            try{ choicesInstance = new Choices(fields, choicesInstance._options || {}); }catch(er){/* ignore */}
          }
        }
        if(selectAllBtn) selectAllBtn.addEventListener('click', selectAllFields);
        if(clearAllBtn) clearAllBtn.addEventListener('click', clearAllFields);
      }

      // Enhance single-select for use case on the landing page to avoid native select color issues
      var useCase = $('#use_case');
      if(useCase){
        try{
          new Choices(useCase, {
            searchEnabled: false,
            shouldSort: false,
            itemSelectText: '',
            allowHTML: false,
            placeholder: false
          });
        }catch(e){ console.warn('Choices init for use_case failed', e); }
      }
    }
  }catch(e){console.warn('Choices init failed', e);}

  // File input validations (simple client-side checks)
  $all('input[type=file]').forEach(function(inp){
    inp.addEventListener('change', function(){
      var f = inp.files && inp.files[0];
      if(!f) return;
      var max = parseInt(inp.getAttribute('data-maxsize') || '0', 10);
      if(max && f.size > max){
        alert('File too large: ' + Math.round(f.size/1024/1024) + ' MB. Max ' + Math.round(max/1024/1024) + ' MB.');
        inp.value = '';
        return;
      }
      var allow = (inp.getAttribute('accept')||'').split(',').map(function(x){return x.trim().toLowerCase();}).filter(Boolean);
      if(allow.length){
        var ok = allow.some(function(a){
          if(a.startsWith('.')) return f.name.toLowerCase().endsWith(a);
          return f.type === a;
        });
        if(!ok){
          alert('Unsupported file type. Allowed: ' + allow.join(', '));
          inp.value = '';
          return;
        }
      }
    });
  });
});
