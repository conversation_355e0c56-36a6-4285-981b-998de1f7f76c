/**
 * <PERSON>do<PERSON> Solver - Backtracking Algorithm
 * Efficiently solves Sudoku puzzles and validates solutions
 */

class SudokuSolver {
	constructor() {
		this.GRID_SIZE = 9;
		this.BOX_SIZE = 3;
		this.EMPTY = 0;
	}

	solve(grid) { const emptyCell = this.findEmptyCell(grid); if (!emptyCell) return true; const [row,col] = emptyCell; for (let num=1; num<=9; num++){ if(this.isValidMove(grid,row,col,num)){ grid[row][col]=num; if(this.solve(grid)) return true; grid[row][col]=this.EMPTY; } } return false; }
	findEmptyCell(grid){ let minOptions=10, best=null; for(let r=0;r<9;r++){ for(let c=0;c<9;c++){ if(grid[r][c]===this.EMPTY){ const opts=this.countValidOptions(grid,r,c); if(opts<minOptions){ minOptions=opts; best=[r,c]; if(opts===0) return best; } } } } return best; }
	countValidOptions(grid,row,col){ let count=0; for(let n=1;n<=9;n++){ if(this.isValidMove(grid,row,col,n)) count++; } return count; }
	isValidMove(grid,row,col,num){ return this.isValidInRow(grid,row,num)&&this.isValidInColumn(grid,col,num)&&this.isValidInBox(grid,row,col,num); }
	isValidInRow(grid,row,num){ for(let c=0;c<9;c++){ if(grid[row][c]===num) return false; } return true; }
	isValidInColumn(grid,col,num){ for(let r=0;r<9;r++){ if(grid[r][col]===num) return false; } return true; }
	isValidInBox(grid,row,col,num){ const br=Math.floor(row/3)*3, bc=Math.floor(col/3)*3; for(let r=br;r<br+3;r++){ for(let c=bc;c<bc+3;c++){ if(grid[r][c]===num) return false; } } return true; }
	isValidSolution(grid){ for(let r=0;r<9;r++){ if(!this.isValidSet(grid[r])) return false; } for(let c=0;c<9;c++){ const col=[]; for(let r=0;r<9;r++) col.push(grid[r][c]); if(!this.isValidSet(col)) return false; } for(let br=0;br<3;br++){ for(let bc=0;bc<3;bc++){ const box=[]; for(let r=br*3;r<(br+1)*3;r++){ for(let c=bc*3;c<(bc+1)*3;c++){ box.push(grid[r][c]); } } if(!this.isValidSet(box)) return false; } } return true; }
	isValidSet(arr){ const seen=new Set(); for(const n of arr){ if(n<1||n>9||seen.has(n)) return false; seen.add(n); } return seen.size===9; }
	getValidNumbers(grid,row,col){ if(grid[row][col]!==this.EMPTY) return []; const out=[]; for(let n=1;n<=9;n++){ if(this.isValidMove(grid,row,col,n)) out.push(n); } return out; }
	hasUniqueSolution(grid){ const g1=this.copyGrid(grid), g2=this.copyGrid(grid); if(!this.solve(g1)) return false; const count=this.countSolutions(g2,2); return count===1; }
	countSolutions(grid,maxCount=2){ const empty=this.findEmptyCell(grid); if(!empty) return 1; const [row,col]=empty; let count=0; for(let n=1;n<=9;n++){ if(this.isValidMove(grid,row,col,n)){ grid[row][col]=n; count+=this.countSolutions(grid,maxCount-count); grid[row][col]=this.EMPTY; if(count>=maxCount) break; } } return count; }
	copyGrid(grid){ return grid.map(r=>[...r]); }
	stringToGrid(s){ if(s.length!==81) throw new Error('Puzzle string must be exactly 81 characters'); const g=[]; for(let i=0;i<9;i++){ const row=[]; for(let j=0;j<9;j++){ const ch=s[i*9+j]; row.push(ch==='.'||ch==='0'?0:parseInt(ch)); } g.push(row); } return g; }
	gridToString(grid){ return grid.flat().map(v=>v===0?'0':v.toString()).join(''); }
	getHint(grid){ for(let r=0;r<9;r++){ for(let c=0;c<9;c++){ if(grid[r][c]===this.EMPTY){ const vals=this.getValidNumbers(grid,r,c); if(vals.length===1) return {row:r,col:c,value:vals[0],technique:'Hidden Single'}; } } } for(let r=0;r<9;r++){ for(let c=0;c<9;c++){ if(grid[r][c]===this.EMPTY){ const vals=this.getValidNumbers(grid,r,c); if(vals.length>0) return {row:r,col:c,value:vals[0],technique:'Trial and Error'}; } } } return null; }
}

if(typeof module!=='undefined'&&module.exports){ module.exports=SudokuSolver; } else if(typeof window!=='undefined'){ window.SudokuSolver=SudokuSolver; }
